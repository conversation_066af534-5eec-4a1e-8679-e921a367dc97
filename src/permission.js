import router from "./router";
import NProgress from "nprogress"; // progress bar
import "nprogress/nprogress.css"; // progress bar style
import i18n from "@/i18n"; // internationalization
NProgress.configure({ showSpinner: false }); // NProgress Configuration

router.beforeEach((to, from, next) => {
  // 检查当前路由是否已经有 lang 参数
  if (!to.query.lang) {
    // 如果没有 lang 参数，添加默认值
    let lang = i18n.locale; // 默认语言，可根据需求调整
    next({
      ...to,
      query: {
        ...to.query,
        lang: lang, // 默认语言，可根据需求调整
      },
    });
  } else {
    // 如果已有 lang 参数，直接继续
    if (!i18n.messages[to.query.lang]) {
      next({
        ...to,
        query: {
          ...to.query,
          lang: i18n.locale, // 默认语言，可根据需求调整
        },
      });
    } else {
      i18n.locale = to.query.lang; // 设置当前语言
      next();
    }
  }
});

// router.beforeEach(async(to, from, next) => {
// const lang = to.params.lang || i18n.locale ||  "zh-CN"; // 默认值
// router.push({ path: to.path, query: { lang: lang }})

// i18n.locale = lang
// console.log(to);
// // next()

//   // start progress bar
//   NProgress.start()

//   // set page title
//   document.title = getPageTitle(to.meta.title)

//   // determine whether the user has logged in
//   const hasToken = getToken()

//   if (hasToken) {
//     if (to.path === '/login') {
//       // if is logged in, redirect to the home page
//       next({ path: '/' })
//       NProgress.done()
//     } else {
//       const hasGetUserInfo = store.getters.name
//       if (hasGetUserInfo) {
//         next()
//       } else {
//         try {
//           // get user info
//           await store.dispatch('user/getInfo')

//           next()
//         } catch (error) {
//           // remove token and go to login page to re-login
//           await store.dispatch('user/resetToken')
//           Message.error(error || 'Has Error')
//           next(`/login?redirect=${to.path}`)
//           NProgress.done()
//         }
//       }
//     }
//   } else {
//     /* has no token*/

//     if (whiteList.indexOf(to.path) !== -1) {
//       // in the free login whitelist, go directly
//       next()
//     } else {
//       // other pages that do not have permission to access are redirected to the login page.
//       next(`/login?redirect=${to.path}`)
//       NProgress.done()
//     }
//   }
// })

// router.afterEach(() => {
//   // finish progress bar
//   NProgress.done()
// })
