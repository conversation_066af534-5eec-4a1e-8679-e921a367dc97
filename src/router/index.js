import Vue from "vue";
import Router from "vue-router";
Vue.use(Router);
/* Layout */
import Layout from "@/layout";

export const constantRoutes = [
  {
    path: "/404",
    component: () => import("@/views/404"),
    hidden: true,
  },

  {
    path: "/",
    component: Layout,
    redirect: "/dashboard",
    children: [
      {
        path: "dashboard",
        name: "首页",
        component: () => import("@/views/dashboard/index"),
        meta: { title: "首页", icon: "el-icon-home" },
      },
    ],
  },
  {
    path: "/presale",
    component: Layout,
    name: "发行预售",
    meta: { title: "发行预售", icon: "el-icon-transfer" },
    children: [
      {
        path: "simpleMint",
        name: "标准Mint",
        component: () => import("@/views/preSale/simpleMint"),
        meta: { title: "标准Mint", icon: "el-icon-transfer" },
      },
      {
        path: "mintAddSale",
        name: "Mint加池",
        component: () => import("@/views/preSale/mintAddSale"),
        meta: { title: "Mint加池", icon: "el-icon-transfer" },
      },
      {
        path: "mintDonate",
        name: "Mint捐赠",
        component: () => import("@/views/preSale/mintDonate"),
        meta: { title: "Mint捐赠", icon: "el-icon-transfer" },
      },
      {
        path: "console",
        name: "预售控制台",
        component: () => import("@/views/preSale/console"),
        meta: { title: "预售控制台", icon: "el-icon-console" },
      },
      {
        path: "mintAddSaleDetail",
        name: "mintAddSaleDetail",
        component: () => import("@/views/preSale/mintAddSaleDetail"),
      },
    ],
  },
  {
    path: "/coinrelease",
    component: Layout,
    redirect: "/coinrelease/table",
    name: "发行代币",
    meta: { title: "发行代币", icon: "el-icon-magic-stick" },
    children: [
      {
        path: "stardand",
        name: "标准代币",
        component: () => import("@/views/coinRelease/index"),
        meta: { title: "标准代币", icon: "el-icon-coin" },
      },
      {
        path: "holdReflection",
        name: "分红本币",
        component: () => import("@/views/coinRelease/holdReflection"),
        meta: { title: "分红本币", icon: "el-icon-coin" },
      },

      {
        path: "LPReflection",
        name: "LP分红",
        component: () => import("@/views/coinRelease/LPReflection"),
        meta: { title: "LP分红", icon: "el-icon-coin" },
      },
      {
        path: "LPwithInviter",
        name: "LP分红+推荐奖励",
        component: () => import("@/views/coinRelease/LPwithInviter"),
        meta: { title: "LP分红+推荐奖励", icon: "el-icon-coin" },
      },
      {
        path: "blackHole",
        name: "黑洞分红",
        component: () => import("@/views/coinRelease/blackHole"),
        meta: { title: "黑洞分红", icon: "el-icon-coin" },
      },
      {
        path: "314协议",
        name: "314协议",
        component: () => import("@/views/coinRelease/314"),
        meta: { title: "314协议", icon: "el-icon-coin" },
      },
      {
        path: "holdWithInviter",
        name: "持币复利+推荐奖励",
        component: () => import("@/views/coinRelease/holdInviter"),
        meta: { title: "持币复利+推荐奖励", icon: "el-icon-coin" },
      },
      {
        path: "HoldRefOthers",
        name: "Mint+暴力分红",
        component: () => import("@/views/coinRelease/holdRefOthers"),
        meta: { title: "Mint+暴力分红", icon: "el-icon-coin" },
      },
      {
        path: "LPBurn",
        name: "Mint+底池燃烧",
        component: () => import("@/views/coinRelease/LPBurn"),
        meta: { title: "Mint+底池燃烧", icon: "el-icon-coin" },
      },
      {
        path: "LPMine",
        name: "LP挖矿+推荐奖励",
        component: () => import("@/views/coinRelease/LPMine"),
        meta: { title: "LP挖矿+推荐奖励", icon: "el-icon-coin" },
      },

      {
        path: "console",
        name: "控制台V1",
        component: () => import("@/views/coinRelease/console"),
        meta: { title: "控制台V1", icon: "el-icon-console" },
      },
      {
        path: "consoleV1",
        name: "控制台V2",
        component: () => import("@/views/coinRelease/consoleV1"),
        meta: { title: "控制台V2", icon: "el-icon-console" },
      },
      {
        path: "detail",
        name: "detail",
        component: () => import("@/views/coinRelease/Coindetail"),
      },
      {
        path: "holdReflectionDetail",
        name: "holdReflectionDetail",
        component: () => import("@/views/coinRelease/holdReflectionDetail"),
      },
      {
        path: "LPinviterDetail",
        name: "LPinviterDetail",
        component: () => import("@/views/coinRelease/LPinviterDetail"),
      },
      {
        path: "LPMineDetail",
        name: "LPMineDetail",
        component: () => import("@/views/coinRelease/LPMineDetail"),
      },
      {
        path: "holdInviterDetail",
        name: "holdInviterDetail",
        component: () => import("@/views/coinRelease/holdInviterDetail"),
      },
      {
        path: "LPBurnDetail",
        name: "LPBurnDetail",
        component: () => import("@/views/coinRelease/LPBurnDetail"),
      },
      {
        path: "holdRefOthersDetail",
        name: "holdRefOthersDetail",
        component: () => import("@/views/coinRelease/holdRefOthersDetail"),
      },
      {
        path: "314Detail",
        name: "314Detail",
        component: () => import("@/views/coinRelease/314Detail"),
      },
      {
        path: "blackHoleDetail",
        name: "blackHoleDetail",
        component: () => import("@/views/coinRelease/blackHoleDetail"),
      },
    ],
  },
  {
    path: "/liquidity",
    component: Layout,
    name: "流动性管理",
    meta: { title: "流动性管理", icon: "el-icon-money" },
    children: [
      {
        path: "/createliquidity",
        name: "创建流动性",
        component: () => import("@/views/liquidity/index"),
        meta: { title: "创建流动性", icon: "el-icon-folder-opened" },
      },
      {
        path: "/createliquiditybuy",
        name: "创建流动性并买入",
        component: () => import("@/views/liquidity/createAdd"),
        meta: { title: "创建流动性并买入", icon: "el-icon-folder-opened" },
      },
      {
        path: "/LPmanage",
        name: "流动性控制台",
        component: () => import("@/views/liquidity/LPmanage"),
        meta: { title: "流动性控制台", icon: "el-icon-console" },
      },
      {
        path: "/LPfixtool",
        name: "流动性修复",
        component: () => import("@/views/tools/index"),
        meta: { title: "流动性修复", icon: "el-icon-magic-stick" },
      },
    ],
  },
  // {
  //   path: "/four",
  //   component: Layout,
  //   name: "Four专区",
  //   meta: { title: "Four专区", icon: "el-icon-coin" },
  //   children: [
  //     {
  //       path: "/createfour",
  //       name: "创建Four",
  //       component: () => import("@/views/four/createfour"),
  //       meta: { title: "创建Four", icon: "el-icon-coin" },
  //     },
  //     {
  //       path: "/sellfour",
  //       name: "一键卖出",
  //       component: () => import("@/views/four/sellfour"),
  //       meta: { title: "一键卖出", icon: "el-icon-coin" },
  //     },
  //   ],
  // },
  {
    path: "https://bridge.pandatool.org/",
    redirect: "https://bridge.pandatool.org/",
    children: [
      {
        path: "https://bridge.pandatool.org/",
        name: "跨链闪兑",
        meta: { title: "跨链闪兑🔥", icon: "el-icon-s-help" },
      },
    ],
  },
  {
    path: "https://solana.pandatool.org/",
    redirect: "https://solana.pandatool.org/",
    children: [
      {
        path: "https://solana.pandatool.org/",
        name: "Solana发币",
        meta: { title: "Solana发币🔥", icon: "el-icon-coin" },
      },
    ],
  },
  {
    path: "/lock",
    component: Layout,
    name: "锁池锁仓",
    meta: { title: "锁池锁仓", icon: "el-icon-lock" },
    children: [
      {
        path: "/createLock",
        name: "创建锁仓",
        component: () => import("@/views/lock/createLock"),
        meta: { title: "创建锁仓", icon: "el-icon-lock" },
      },
      {
        path: "/lockList",
        name: "锁仓控制台",
        component: () => import("@/views/lock/lockList"),
        meta: { title: "锁仓控制台", icon: "el-icon-console" },
      },
    ],
  },
  {
    path: "/accountCreate",
    component: Layout,
    redirect: "/accountCreate/eth",
    name: "批量工具",
    meta: {
      title: "批量工具",
      icon: "el-icon-addwallet",
    },
    children: [
      {
        path: "/multisend",
        name: "批量转账",
        component: () => import("@/views/multisend/bsc/index"),
        meta: { title: "批量转账", icon: "el-icon-transfer" },
      },
      {
        path: "/gather",
        name: "批量归集",
        component: () => import("@/views/gather/index"),
        meta: { title: "批量归集", icon: "el-icon-document" },
      },
      {
        path: "/eth",
        component: () => import("@/views/accountCreate/eth/index"),
        name: "批量生成钱包EVM",
        meta: { title: "批量生成钱包EVM", icon: "el-icon-addwallet" },
      },
      {
        path: "/trx",
        component: () => import("@/views/accountCreate/trx/index"),
        name: "批量生成钱包Tron",
        meta: { title: "批量生成钱包Tron", icon: "el-icon-addwallet" },
      },
      {
        path: "/evmPrettyWallet",
        component: () => import("@/views/prettyWallet/evm/index"),
        name: "evm靓号生成",
        meta: { title: "evm靓号生成", icon: "el-icon-addwallet" },
      },
      {
        path: "/trxPrettyWallet",
        component: () => import("@/views/prettyWallet/trx/index"),
        name: "trx靓号生成",
        meta: { title: "trx靓号生成", icon: "el-icon-addwallet" },
      },
    ],
  },
  {
    path: "/market",
    component: Layout,
    redirect: "/market",
    name: "市值管理1",
    meta: {
      title: "市值管理",
      icon: "el-icon-aim",
    },
    children: [
      {
        path: "management",
        name: "市值管理",
        component: () => import("@/views/market/management/index"),
        meta: { title: "市值管理", icon: "el-icon-sell" },
      },
      {
        path: "https://evmswap.pandatool.org/",
        name: "市值管理V2",
        // component: () => import("@/views/market/management/index"),
        meta: { title: "市值管理", icon: "el-icon-sell" },
      }
    ],
  },
  {
    path: "/contractCheck",
    component: Layout,
    name: "工具箱",
    redirect: "/contractCheck",
    meta: {
      title: "工具箱",
      icon: "el-icon-box",
    },
    children: [
      {
        path: "contractCheck",
        name: "合约检测",
        component: () => import("@/views/contractCheck/index"),
        meta: { title: "合约检测", icon: "el-icon-zoom-out" },
      },
      // {
      //   path: "/LPfixtool",
      //   name: "LP流动性修复",
      //   component: () => import("@/views/tools/index"),
      //   meta: { title: "LP流动性修复", icon: "el-icon-magic-stick" },
      // },
    ],
  },
  {
    path: "/website",
    component: Layout,
    redirect: "/website",
    children: [
      {
        path: "website",
        name: "官网模板",
        component: () => import("@/views/website/index"),
        meta: { title: "官网模板", icon: "el-icon-files" },
      },
    ],
  },
  {
    path: "/manage",
    redirect: "https://help.pandatool.org",
    children: [
      {
        path: "https://help.pandatool.org",
        name: "帮助文档",
        //component: () => import('@/views/test/index'),

        meta: { title: "帮助文档", icon: "el-icon-document" },
      },
    ],
  },
  // {
  //   path: "/manage",
  //   component: Layout,
  //   redirect: "/manage",
  //   children: [
  //     {
  //       path: "manage",
  //       name: "合约管理",
  //       component: () => import("@/views/manage/index"),
  //       meta: { title: "合约管理", icon: "el-icon-home" },
  //     },
  //   ],
  // },
  // 404 page must be placed at the end !!!
  { path: "*", redirect: "/404", hidden: true },
];

const createRouter = () =>
  new Router({
    // mode: 'history', // require service support
    scrollBehavior: () => ({ y: 0 }),
    routes: constantRoutes,
  });

const router = createRouter();

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  const newRouter = createRouter();
  router.matcher = newRouter.matcher; // reset router
}

export default router;
