import axios from "axios";
import { Message } from "element-ui";
export async function reqNonce(accountAddress, networkCode = "BSC") {
  const config = {
    method: "post", // 请求方法为 POST
    url: "https://four.meme/meme-api/v1/private/user/nonce/generate",
    headers: {
      "Content-Type": "application/json", // 设置 JSON 内容类型
    },
    data: {
      // 请求体数据
      accountAddress: accountAddress,
      verifyType: "LOGIN",
      networkCode: networkCode,
    },
    maxRedirects: 5, // 允许重定向（默认已启用）
  };
  try {
    const response = await axios(config);
    console.log("Response:", response.data);
    if (response.data.code != 0) {
      Message({
        message: response.data.message || "Error",
        type: "error",
        duration: 5 * 1000,
      });
      return null
    }
    return response.data; // 返回响应数据
  } catch (error) {
    console.error("Error:", error);
    Message({
      message: "Network Error Please try again later",
      type: "error",
      duration: 5 * 1000,
    });
    return null; // 返回 null 表示请求失败
  }
}
export async function reqLogin(accountAddress, signature, networkCode = "BSC") {
  const config = {
    method: "post", // 请求方法为 POST
    url: "https://four.meme/meme-api/v1/private/user/login/dex",
    headers: {
      "Content-Type": "application/json", // 设置 JSON 内容类型
    },
    data: {
      region: "WEB",
      langType: "EN",
      loginIp: "",
      inviteCode: "",
      verifyInfo: {
        address: accountAddress,
        networkCode: networkCode,
        signature: signature,
        verifyType: "LOGIN",
      },
      walletName: "MetaMask",
    },
    maxRedirects: 5, // 允许重定向（默认已启用）
  };
  try {
    const response = await axios(config);
    if (response.data.code != 0) {
      Message({
        message: response.data.message || "Error",
        type: "error",
        duration: 5 * 1000,
      });
      return null; // 返回 null 表示请求失败
    }
   
    return response.data; // 返回响应数据
  } catch (error) {
    console.error("Error:", error);
    Message({
      message: "Network Error Please try again later",
      type: "error",
      duration: 5 * 1000,
    });
    return null; // 返回 null 表示请求失败
  }
}

export async function reqGetUserInfo(access) {
 // 请求方法为 POST
  const config = {
    method: "get", // 请求方法为 POST
    url: "https://four.meme/meme-api/v1/private/user/info",
    headers: {
      "meme-web-access":access,
    },
    maxRedirects: 5, // 允许重定向（默认已启用）    
  }
  try {
    const response = await axios(config);
    if (response.data.code != 0) {
      Message({
        message: response.data.message || "Error",
        type: "error",
        duration: 5 * 1000,
      });
      return null; // 返回 null 表示请求失败
    }
   
    return response.data; // 返回响应数据
  } catch (error) {
    console.error("Error:", error);
    Message({
      message: "Network Error Please try again later",
      type: "error",
      duration: 5 * 1000,
    });
    return null; // 返回 null 表示请求失败
  }
}