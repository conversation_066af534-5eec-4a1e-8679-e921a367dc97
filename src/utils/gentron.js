import { ethers } from "ethers";
import bs58check  from "bs58check";
export function generateAccount() {
  const wallet = ethers.Wallet.createRandom();
  const privateKey = wallet.privateKey;
  const ethAddress = wallet.address; // 以太坊地址
  const tronAddressHex = "41" + ethAddress.slice(2); // 波场地址以 "41" 开头
  const tronAddressBase58 = bs58check.encode(Buffer.from(tronAddressHex, "hex"));
  console.log("tronm: ", tronAddressBase58);
  console.log("eth: ", ethAddress);
  return {
    address: tronAddressBase58,
    privateKey: privateKey.slice(2),
  };
}
