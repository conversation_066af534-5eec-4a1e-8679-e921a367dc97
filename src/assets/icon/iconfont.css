@font-face {
  font-family: "element-icons"; /* Project id 4001972 */
  src: url('iconfont.woff2?t=1680866542017') format('woff2'),
       url('iconfont.woff?t=1680866542017') format('woff'),
       url('iconfont.ttf?t=1680866542017') format('truetype');
}

.element-icons {
  font-family: "element-icons" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.el-icon-twitter:before {
  content: "\e611";
}

.el-icon-addwallet:before {
  content: "\e604";
}

.el-icon-blockchain:before {
  content: "\e605";
}

.el-icon-community:before {
  content: "\e606";
}

.el-icon-console:before {
  content: "\e607";
}

.el-icon-email:before {
  content: "\e608";
}

.el-icon-help:before {
  content: "\e60a";
}

.el-icon-home:before {
  content: "\e60b";
}

.el-icon-mall:before {
  content: "\e60c";
}

.el-icon-news:before {
  content: "\e60e";
}

.el-icon-transfer:before {
  content: "\e60f";
}

.el-icon-fantom-ftm-logo:before {
  content: "\e610";
}

.el-icon-bnb-bnb-logo:before {
  content: "\e601";
}

.el-icon-polygon-matic-logo:before {
  content: "\e602";
}

.el-icon-ethereum-eth-logo:before {
  content: "\e603";
}

.el-icon-3501shuju:before {
  content: "\e665";
}

.el-icon-github:before {
  content: "\e64c";
}

.el-icon-youtube:before {
  content: "\ea07";
}

.el-icon-GitHub:before {
  content: "\e8c6";
}

.el-icon-BNB:before {
  content: "\e622";
}

.el-icon-bilibili:before {
  content: "\e609";
}

.el-icon-telegram:before {
  content: "\e60d";
}

