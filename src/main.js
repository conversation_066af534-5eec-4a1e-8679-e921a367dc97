import Vue from "vue";
import "normalize.css/normalize.css"; // A modern alternative to CSS resets
import ElementUI from "element-ui";
import "element-ui/lib/theme-chalk/index.css";
import "@/styles/index.scss"; // global css
import App from "./App";
import store from "./store";
import router from "./router";
import "./assets/icon/iconfont.css"; //self-icon
import "@/icons"; // icon
import "@/permission"; // permission control
import i18n from "./i18n"; // internationalization

import ElementLocale from "element-ui/lib/locale";

Vue.use(ElementUI);
ElementLocale.i18n((key, value) => i18n.t(key, value));

// set ElementUI lang to EN
// Vue.use(ElementUI, { locale })
// 如果想要中文版 element-ui，按如下方式声明

// Vue.use(ElementUI)

Vue.config.productionTip = false;
new Vue({
  el: "#app",
  router,
  store,
  i18n,
  beforeCreate() {
    console.log(i18n.locale);
    const { ethereum } = window;
    if (ethereum && ethereum.isMetaMask) {
      console.log("prepareed?");
      store.commit("user/set_account");
      store.dispatch("user/connect");
    }
    // if(signer) {
    //   console.log(signer)
    //   store.commit('init')
    // store.commit('set_account')
    // }
  },

  render: (h) => h(App),
});
