<template>
  <div class="navbar">
    <hamburger
      :is-active="sidebar.opened"
      class="hamburger-container"
      @toggleClick="toggleSideBar"
    />

    <!-- <breadcrumb class="breadcrumb-container" /> -->

    <div class="right-menu">
      <el-dropdown class="right-menu-item" trigger="click" @command="setLang">
        <el-button style="height: 40px" type="info" plain>
          <svg-icon style="width: 16px; height: 16px" icon-class="translate" />
        </el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item
            command="zh-CN"
            :class="this.$i18n.locale === 'zh-CN' ? 'active' : ''"
            >中文</el-dropdown-item
          >
          <el-dropdown-item
            command="en-US"
            :class="this.$i18n.locale === 'en-US' ? 'active' : ''"
            >English</el-dropdown-item
          >
        </el-dropdown-menu>
      </el-dropdown>
      <el-dropdown
        class="right-menu-item"
        trigger="click"
        @command="handleChain"
      >
        <el-button icon="el-icon-connection" type="primary" plain>
          {{ getChain }}<i class="el-icon-arrow-down el-icon--right" />
        </el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item command="ETH">
            <svg-icon style="width: 16px; height: 16px" icon-class="eth" />
            ETH</el-dropdown-item
          >
          <el-dropdown-item command="BSC">
            <svg-icon style="width: 16px; height: 16px" icon-class="bnb" />

            BSC
            <svg-icon style="width: 16px; height: 16px" icon-class="fire" />
          </el-dropdown-item>
          <el-dropdown-item command="Solana">
            <svg-icon style="width: 16px; height: 16px" icon-class="solana" />

            Solana
            <svg-icon style="width: 16px; height: 16px" icon-class="fire" />
          </el-dropdown-item>

          <el-dropdown-item command="BASE">
            <svg-icon style="width: 16px; height: 16px" icon-class="base" />
            BASE</el-dropdown-item
          >
          <el-dropdown-item command="Polygon">
            <svg-icon style="width: 16px; height: 16px" icon-class="matic" />
            Polygon</el-dropdown-item
          >
          <el-dropdown-item command="Ton">
            <svg-icon style="width: 16px; height: 16px" icon-class="ton" />
            Ton</el-dropdown-item
          >
          <el-dropdown-item command="Tron">
            <svg-icon style="width: 16px; height: 16px" icon-class="tron" />
            Tron</el-dropdown-item
          >
          <el-dropdown-item command="Sui">
            <svg-icon style="width: 16px; height: 16px" icon-class="sui" />
            Sui</el-dropdown-item
          >
          <el-dropdown-item command="ARB">
            <svg-icon style="width: 16px; height: 16px" icon-class="arb" />
            ARB</el-dropdown-item
          >
          <el-dropdown-item command="CORE">
            <svg-icon style="width: 16px; height: 16px" icon-class="core" />
            Core</el-dropdown-item
          >
          <el-dropdown-item command="OKT">
            <svg-icon style="width: 16px; height: 16px" icon-class="okt" />
            OKT</el-dropdown-item
          >
          <el-dropdown-item command="Blast">
            <svg-icon style="width: 16px; height: 16px" icon-class="blast" />
            Blast</el-dropdown-item
          >
          <el-dropdown-item icon="el-icon-blockchain" command="BSCT"
            >BSC Testnet</el-dropdown-item
          >
          <!-- <el-dropdown-item icon="el-icon-blockchain" command="BASET"
            >BASE Sepolia Test</el-dropdown-item
          > -->
          <!-- <el-dropdown-item icon="el-icon-blockchain" command="PolygonT"
            >Polygon Test</el-dropdown-item
          >
          <el-dropdown-item icon="el-icon-blockchain" command="BlastT"
            >Blast Sepolia Test</el-dropdown-item
          > -->
          <!-- <el-dropdown-item icon="el-icon-blockchain" command = "FTMT">FTM Testnet</el-dropdown-item>
          <el-dropdown-item icon="el-icon-blockchain" command = "CORET">Core Testnet</el-dropdown-item>
          <el-dropdown-item icon="el-icon-blockchain" command = "ARBT">ARB Goerli Testnet</el-dropdown-item>
          <el-dropdown-item icon="el-icon-blockchain" command = "ETHGT">ETH Goerli Testnet</el-dropdown-item> -->
        </el-dropdown-menu>
      </el-dropdown>

      <el-dropdown v-if="!isMetaMaskConnected" class="right-menu-item">
        <el-button
          icon="el-icon-s-finance"
          type="info"
          plain
          @click="connectWallet"
        >
          {{ $t("nav.连接钱包") }}
        </el-button>
      </el-dropdown>

      <el-dropdown
        v-if="isMetaMaskConnected"
        class="right-menu-item"
        trigger="click"
        @command="handleWallet"
      >
        <el-button icon="el-icon-s-finance" type="info" plain>
          {{ getAccount }}
        </el-button>

        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item command="CopyAddress">{{
            $t("nav.复制地址")
          }}</el-dropdown-item>
          <el-dropdown-item command="Disconnect"
            >{{ $t("nav.断开连接") }}
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      <el-dialog
        :title="$t('nav.请选择钱包')"
        :visible.sync="walletVisible"
        width="25%"
      >
        <el-row :gutter="24">
          <el-col :span="12">
            <el-card
              style="width: 100%; aspect-ratio: 1; border-radius: 12px"
              :body-style="{ padding: '0px' }"
            >
              <el-image
                :src="metamask"
                fit="fill"
                @click="connectWallet"
              ></el-image>
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card
              style="width: 100%; aspect-ratio: 1; border-radius: 12px"
              :body-style="{ padding: '0px' }"
            >
              <el-image :src="okx" fit="fill" @click="connectOKX"></el-image>
            </el-card>
          </el-col>
          <el-col :span="12" style="margin-top: 20px">
            <el-card
              style="width: 100%; aspect-ratio: 1; border-radius: 12px"
              :body-style="{ padding: '0px' }"
            >
              <el-image
                :src="bitkeep"
                fit="fill"
                @click="connectWallet"
              ></el-image>
            </el-card>
          </el-col>
          <el-col :span="12" style="margin-top: 20px">
            <el-card
              style="width: 100%; aspect-ratio: 1; border-radius: 12px"
              :body-style="{ padding: '0px' }"
            >
              <el-image :src="tp" fit="fill" @click="connectWallet"></el-image>
            </el-card>
          </el-col>
        </el-row>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import Chainlist from "@/contracts/chainlist.json";
import Breadcrumb from "@/components/Breadcrumb";
import Hamburger from "@/components/Hamburger";
import store from "@/store";
export default {
  components: {
    Breadcrumb,
    Hamburger,
  },
  data() {
    return {
      Chainlist,
      walletVisible: false,
      okx: require("@/assets/okx-wallet-logo.png"),
      metamask: require("@/assets/metamask-logo.png"),
      bitkeep: require("@/assets/bitkeep.png"),
      tp: require("@/assets/tp.png"),
    };
  },
  computed: {
    ...mapGetters(["sidebar", "avatar"]),
    isMobile() {
      return window.screen.width < 850;
    },
    isMetaMaskInstalled() {
      const { ethereum } = window;
      console.log("metamask", Boolean(ethereum && ethereum.isMetaMask));
      return Boolean(ethereum && ethereum.isMetaMask);
    },
    isMetaMaskConnected() {
      console.log("address", store.state.user.address);
      return store.state.user.address != null;
    },
    getAccount() {
      return (
        store.state.user.address.substring(0, 5) +
        "..." +
        store.state.user.address.substring(37)
      );
    },
    getChain() {
      return store.state.user.chainName;
    },
    checkChain() {
      store.state.user.provider.getNetwork().then((network) => {
        console.log(network.chainId);
        if (network.chainId != store.state.user.chainId) {
          this.$router.replace({ path: "/test" });
        }
      });
    },
  },
  methods: {
    toggleSideBar() {
      this.$store.dispatch("app/toggleSideBar");
    },
    chooseWallet() {
      this.walletVisible = true;
    },
    connectWallet() {
      store.dispatch("user/connect");
      this.walletVisible = false;
    },
    connectOKX() {
      if (typeof window.okxwallet !== "undefined") {
        console.log("欧易钱包插件已安装！");
        this.connectWallet();
      } else {
        this.$message({
          type: "danger",
          message: "未安装欧易钱包插件!",
        });
      }
    },
    handleWallet(command) {
      if (command == "CopyAddress") {
        this.copy_str(store.state.user.address);
        // store.dispatch('user/disconnect')
      } else if (command == "Disconnect") {
        store.dispatch("user/disconnect");
      }
    },
    setLang(command) {
      switch (command) {
        case "zh-CN":
          this.$i18n.locale = "zh-CN";
          document.title = this.$t("webTitle");
          break;
        case "en-US":
          this.$i18n.locale = "en-US";
          document.title = this.$t("webTitle");
          break;
        default:
      }
      this.$router.replace({
        ...this.$route,
        query: {
          ...this.$route.query,
          lang: command, // 设置 lang 参数
        },
      });
    },
    handleChain(command) {
      if (command == "ETH") {
        const chainArgs = this.Chainlist.ChainInfo[0];
        store.dispatch("user/switchChain", chainArgs);
      } else if (command == "BSC") {
        const chainArgs = this.Chainlist.ChainInfo[1];
        store.dispatch("user/switchChain", chainArgs);
      } else if (command == "ARB") {
        const chainArgs = this.Chainlist.ChainInfo[2];
        store.dispatch("user/switchChain", chainArgs);
      } else if (command == "BASE") {
        const chainArgs = this.Chainlist.ChainInfo[3];
        store.dispatch("user/switchChain", chainArgs);
      } else if (command == "Polygon") {
        const chainArgs = this.Chainlist.ChainInfo[4];
        store.dispatch("user/switchChain", chainArgs);
      } else if (command == "CORE") {
        const chainArgs = this.Chainlist.ChainInfo[5];
        store.dispatch("user/switchChain", chainArgs);
      } else if (command == "OKT") {
        const chainArgs = this.Chainlist.ChainInfo[6];
        store.dispatch("user/switchChain", chainArgs);
      } else if (command == "Blast") {
        const chainArgs = this.Chainlist.ChainInfo[7];
        store.dispatch("user/switchChain", chainArgs);
      } else if (command == "BSCT") {
        const chainArgs = this.Chainlist.ChainInfo[8];
        store.dispatch("user/switchChain", chainArgs);
      } else if (command == "PolygonT") {
        const chainArgs = this.Chainlist.ChainInfo[9];
        store.dispatch("user/switchChain", chainArgs);
      } else if (command == "BASET") {
        const chainArgs = this.Chainlist.ChainInfo[10];
        console.log("chainArgs", chainArgs);
        store.dispatch("user/switchChain", chainArgs);
      } else if (command == "Solana") {
        window.open("https://solana.pandatool.org", "_blank");
      } else if (command == "Ton") {
        window.open("https://ton.pandatool.org", "_blank");
      } else if (command == "Sui") {
        window.open("https://sui.pandatool.org", "_blank");
      } else if (command == "Tron") {
        window.open("https://tron.pandatool.org", "_blank");
      } else {
        console.log("wrongChain");
      }
    },
    copy_str(text) {
      if (navigator.clipboard) {
        // clipboard api 复制
        navigator.clipboard.writeText(text);
      } else {
        var textarea = document.createElement("textarea");
        document.body.appendChild(textarea);
        // 隐藏此输入框
        textarea.style.position = "fixed";
        textarea.style.clip = "rect(0 0 0 0)";
        textarea.style.top = "10px";
        // 赋值
        textarea.value = text;
        // 选中
        textarea.select();
        // 复制
        document.execCommand("copy", true);
        // 移除输入框
        document.body.removeChild(textarea);
      }
      this.$message({
        type: "success",
        message: this.$t("nav.已复制"),
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.active {
  background-color: #cfe6d2;
}
@media screen and (max-width: 500px) {
  // .walletDialog{
  //   width: 90% ;
  // }

  .navbar {
    height: 60px;
    overflow: hidden;
    position: relative;
    background: #fff;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  }

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background 0.3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }

  .breadcrumb-container {
    float: left;
  }

  .right-menu {
    float: right;
    height: 100%;
    line-height: 30px;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;

      margin-right: 10px;
      top: 15%;
      height: 100%;
      font-size: 12px;
      color: #5a5e66;
      vertical-align: center;

      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;

        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
      }
    }
  }

  .el-button {
    padding: 10px;
    overflow: hidden;
    max-width: 150px;
  }
}
@media screen and (min-width: 500px) {
  .navbar {
    height: 60px;
    overflow: hidden;
    position: relative;
    background: #fff;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);

    .hamburger-container {
      line-height: 46px;
      height: 100%;
      float: left;
      cursor: pointer;
      transition: background 0.3s;
      -webkit-tap-highlight-color: transparent;

      &:hover {
        background: rgba(0, 0, 0, 0.025);
      }
    }

    .breadcrumb-container {
      float: left;
    }

    .right-menu {
      float: right;
      height: 100%;
      line-height: 40px;

      &:focus {
        outline: none;
      }

      .right-menu-item {
        display: inline-block;
        padding: 0 10px;
        height: 100%;
        font-size: 18px;
        color: #5a5e66;
        vertical-align: text-bottom;

        top: 15%;

        &.hover-effect {
          cursor: pointer;
          transition: background 0.3s;

          &:hover {
            background: rgba(0, 0, 0, 0.025);
          }
        }
      }
    }
  }
}
</style>
