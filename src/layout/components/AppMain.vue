<template>
  <section class="app-main">
    <transition name="fade-transform" mode="out-in">
      <router-view :key="key" />
    </transition>
    <div
      style="
        background: #eeeeee;
        display: flex;
        height: max-content;
        margin-top: 0px;
        height: 100px;
      "
    >
      <el-divider
        >{{ $t("footer.有任何问题请加入") }}
        <el-link
          icon="el-icon-telegram"
          :href="$i18n.locale == 'zh-CN' ? `https://t.me/pandatool` :`https://t.me/pandatool_en`"
          target="_blank"
          >PandaTool</el-link
        >

        {{ $t("footer.交流群进行反馈") }}</el-divider
      >
    </div>
  </section>
</template>

<script>
export default {
  name: "AppMain",
  computed: {
    key() {
      return this.$route.path;
    },
  },
};
</script>

<style scoped>
.app-main {
  /*50 = navbar  */
  min-height: calc(100vh - 50px);
  width: 100%;
  position: relative;
  overflow: hidden;
}
.fixed-header + .app-main {
  padding-top: 50px;
}
</style>

<style lang="scss">
// fix css style bug in open el-dialog
.el-popup-parent--hidden {
  .fixed-header {
    padding-right: 15px;
  }
}
</style>
