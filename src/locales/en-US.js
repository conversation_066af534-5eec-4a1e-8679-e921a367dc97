import { prettyWalletEn } from "./prettyWallet";

export default {
  webTitle: "PandaTool | The best tool to create coin platform on the net",
  nav: {
    连接钱包: "Connect Wallet",
    复制地址: "Copy Address",
    断开连接: "Disconnect",
    请选择钱包: "Select wallet",
    已复制: "Copied !",
  },
  router: {
    批量生成钱包EVM: "Create  Wallets EVMs",
    批量生成钱包Tron: "Create  Wallets Tron",
    evm靓号生成: "prettyWallet EVMS",
    trx靓号生成: "prettyWallet Tron",
    批量工具: "Batch Tools",
    帮助文档: "Help & Support",
    官网模板: "Website Templates",
    流动性修复: "Liquidity Repair",
    合约检测: "Token Detection",
    工具箱: "Toolbox",
    批量归集: "Gather",
    市值管理: "Swap bot",
    市值管理V2: "Swap bot v2",
    市值管理1: "Swap bot",
    批量转账: "Batch Transfer",
    Solana发币: "Solana Tools 🔥",
    跨链闪兑: "Bridge Bridge 🔥",
    控制台V2: "Console V2",
    控制台V1: "Console V1",
    "LP挖矿+推荐奖励": " LP Mining",
    "Mint+底池燃烧": "Reserve Burn",
    "Mint+暴力分红": "Holders Reflection",
    "持币复利+推荐奖励": "Holders Interest",
    "314协议": "314 Protocol",
    黑洞分红: "Burning Reflection",
    "LP分红+推荐奖励": "LP Reflection Pro",
    LP分红: "LP Reflection",
    分红本币: "Holders Dividend",
    标准代币: "Standard",
    发行代币: "Create Token",
    预售控制台: "Pre-Sale Console",
    Mint捐赠: "Mint & Donation",
    Mint加池: "Mint & Liquidity",
    标准Mint: "Simple Mint",
    Four专区: "Four",
    创建Four: "Create Four",
    流动性管理: "Liquidity Management",
    创建流动性: "Create Liquidity",
    流动性控制台: "Liquidity Console",
    创建流动性并买入: "Create Liquidity&Buy",
    发行预售: "Pre-Sale",
    锁池锁仓: "Lock Liquidity/Token",
    创建锁仓: "Create Lock",
    锁仓控制台: "Lock Console",
    首页: "Home",
  },
  footer: {
    有任何问题请加入: "Please join ",
    交流群进行反馈: "if you have any questions",
  },
  accountCreate: {
    批量生产以太坊兼容链钱包: "Create Ethereum Compatible Wallets in Batch",
    请输入生成数量: "Quantity",
    数量不能为空: "Quantity cannot be empty",
    数量必须为数字值: "Quantity must be a number",
    生成中: "Creating...",
    生成: "Create",
    导出为CSV: "Export CSV",
    警告: "Your wallet is generated locally only, PandaTool does not save user data. Please properly back up your private key and mnemonic phrase and ensure they are stored in a secure offline environment. Once the private key or mnemonic phrase is lost or leaked, the assets cannot be recovered and the risk is at your own risk.",
    以太坊地址: "Ethereum Address",
    波场地址: "TRON Address",
    私钥: "Private Key",
    助记词: "Mnemonic Phrase",
    请生成地址: "Please generate wallets!",
    批量生产波场钱包: "Create TRON Wallets in Batch",
  },
  market: {
    日志窗口: "Log window",
    导入刷单钱包私钥: "Import  Private Key",
    池子地址: "Pool address",
    当前价格: "Now Price",
    输入私钥: "Input private key",
    取消: "Cancel",
    导入: "Import",
    错误: "Error",
    钱包格式错误: "Wallet format error",
    请切换至币安链: "Please switch to Binance Chain",
    该工具需开通会员: "This tool requires a membership",
    一天: "1 day ",
    一周: "1 week ",
    一月: "1 month ",
    永久: "forever ",
    购买: "Buy",
    花费代币授权: "Approve",
    代币授权: "Approve",
    开始: "Start",
    停止: "Stop",
    清空日志: "Clear Log",
    未开通VIP: "Not a VIP",
    请设置目标价格: "Please set target price",
    网络错误请检查网络节点: "Network error, please check network node",
    交易停止: "Transaction stopped",
    开始查价格: "Start check price",
    交易金额为跳过: "Transaction amount is 0 skipped",
    开始执行交易: "Start executing transaction",
    交易已发出等待打包确认:
      "Transaction has been sent, waiting for packing confirmation",
    打包确认交易哈希: "Packing confirmation transaction hash",
    交易前价格: "Before price",
    交易后价格: "After price",
    交易需要先授权如果已授权请确保代币余额充足:
      "The transaction requires prior authorization. If already authorized, please ensure sufficient token balance",
    "交易错误，1、钱包确保有足够的代币和手续费(足够请忽略)。2、请检查您的网络":
      "Transaction Error Ensure your wallet has sufficient tokens and transaction fees (if sufficient, you can ignore this). Please check your network connection.",
    继续下一个钱包: "Proceed to the next wallet.",
    交易已暂停: "paused",
    正在停止交易: "pauseing",
    该类型代币无需授权可以交易: "can be traded",
    请导入钱包: "import wallet",
    请刷新钱包: "Please refresh the wallet.",
    授权成功: "Success",
    "请确认钱包手续费充足（续费充足请忽略）":
      "Please ensure the wallet has sufficient transaction fees (if sufficient, you can ignore this).",
    错误类型: "Error Type:",
    池子查询完成: "Pool query completed.",
    出现错误: "Error ",
    池子错误请确认您已经添加好了池子:
      "Pool error. Please confirm that you have added the pool correctly.",
    正在等待矿工打包: "Waiting for the miner to package the transaction.",
    授权完成: "Approve completed",
    达到目标价格程序即将停止:
      "Target price reached. The program will stop soon.",
    买入数量: "Purchase quantity",
    卖出数量: "Sell quantity",
    先查池子: "Check the pool first.",
    按钱包持有量的: "Based on the wallet's holdings.",
    卖出: "Sell ",
    卖出价值: "Sell value",
    注意如果持有的代币价值不足兑换可能会失败:
      "Note that if the value of the tokens held is insufficient, the exchange may fail.",
    请输入正确的卖出数量: "Please enter the correct sell quantity.",
    请输入正确的目标价格: "Please enter the correct target price.",
    拉盘的目标价格不能小于等于当前价格:
      "The target price for price manipulation cannot be less than or equal to the current price.",
    砸盘的目标价格不能大于当前价格:
      "The target price for price dumping cannot be greater than the current price.",
    成功: "success ",
    到期时间: "Expiration time:",
    错误404: "Error 404",
    买入金额: "Purchase amount",
    交易哈希: "Transaction hash:",
    Log1: "Wallet #{count}: {address} has started the transaction.",
    Log2: "Wallet #{count}: {address} has completed the transaction.",
    Log3: "Pause for {sleepTime} seconds before executing the next wallet.",
    Log4: "Round {round} has ended. The next round will start in 1 second",
    Log5: "Appvove hash for wallet #{count}: {hash}",
    model: {
      钱包使用方式: "Wallet Usage Method",
      顺序: "Sequential",
      随机: "Random",
      模式: "Mode",
      拉盘: "Pump",
      砸盘: "Dump",
      代币合约地址: "Token Contract Address",
      池子类型: "Pool Type",
      请选择池子类型: "Please Select Pool Type",
      获得代币: "Obtain Token",
      花费代币: "Spend Token",
      请选择花费代币: "Please Select Spend Token",
      目标价格: "Target Price",
      卖出计算方式: "Sell Calculation Method",
      买入计算方式: "Buy Calculation Method",
      数量: "Quantity",
      百分比: "Percentage",
      时间间隔: "Time Interval",
      查池子: "Check Pool",
      金额范围: "Amount Range (e.g., set both sides to 1 for fixed amount 1)",
      数量范围:
        "Quantity Range (e.g., set both sides to 1 for fixed quantity 1)",
      您还不是VIP: "You are not a VIP yet, please subscribe to VIP",
    },
    点击查看手把手教程: "Tutorial",
    选择公链: "Select Chain",
    选择交易所: "Select Dexs",
    请选择: "Please Select",
    网络节点: "Network Node",
    walletList: {
      刷新钱包: "Refresh Wallet",
      钱包地址: "Wallet Address",
      原生代币: "Native Token",
      余额: "Balance",
      代币余额: "Token Balance",
      花费代币授权: "Spend Approve",
      代币授权: "Token Appvove",
      原生代币总余额: "Total Native Token Balance",
      总余额: "Total Balance",
      提示: "Tip",
      m1: "Please import the wallet first, check the pool, then refresh the wallet",
      m2: "Please check the pool first, then refresh the wallet",
    },
  },
  multisend: {
    暂不支持此链: "This chain is not supported yet",
    如有需要请联系管理员定制:
      "If needed, please contact the administrator for customization",
    批量转账: "Batch Transfer",
    点击查看手把手教程: "Tutorial",
    手续费提示: "Fee",
    "如果代币导致转账异常，请在小狐狸钱包增加gas 每次批量转账手续费":
      "If the token causes transaction issues, please increase the gas in MetaMask for each batch transfer fee",
    发送代币: "Select your Token",
    请输入代币合约地址: "Please enter the token contract address",
    地址数量提示:
      "Receiving addresses and quantities (up to 200 addresses, format as shown below)",
    随机地址: "Random Address",
    请输入内容: "Please enter content",
    注意: "Note: One address per line, no spaces on either side, commas should be English commas. It is recommended to prepare the content in a txt file and then copy it to the input box",
    下一步: "Next Step",
    接收地址: "Receiving Address",
    数量: "Quantity",
    摘要: "Summary",
    转出地址: "Out address",
    代币余额: "Token balance",
    代币地址: "Token Address",
    授权金额: "Approve amount",
    发送数量: "Send amount",
    全部授权: "All approve",
    转账金额: "Send amount",
    授权: "Approve",
    返回: "Back",
    转账: "Transfer",
    输入随机地址数量: "Enter the number of random addresses",
    警告一:
      "Please enter the number and amount of addresses you want to randomly generate. It is recommended to generate up to 200 addresses",
    警告二: "Warning! Randomly generated addresses cannot be recovered",
    警告三:
      "This function is intended for airdrop token holder addresses. It is not recommended to use this function for valuable tokens",
    地址数: "Address Num",
    最小金额: "Min Amount",
    最大金额: "Max Amount",
    取消: "Cancel",
    确定: "Ok",
    提示: "Tip",
    暂不支持此链: "This chain is not supported, please switch chain",
    地址错误: "Error address",
    格式错误: "Format Error",
    转账成功: "Transfer Successful",
    生产地址中: "Generating address, please wait...",
    转账失败余额不足: "Transfer failed, insufficient balance",
    转账失败: "Failed to transfer",
    无需授权可以直接转账: "No authorization needed, can transfer directly",
    授权额度充足可以直接转账:
      "Authorization limit sufficient, can transfer directly",
    完成授权: "Authorization complete! Transaction hash:",
    授权失败: "Authorization failed",
    请选择token: "Please select token",
    message1:
      "The address on line {hang} is invalid, please check your address",
    message2:
      "Format error!! One address per line, no spaces on either side, commas should be English commas",
    message3: "Insufficient authorization amount, please authorize",
    message4: "Insufficient token balance",
  },
  contractCheck: {
    tips: "Tips: We try to identify contract information as accurately as possible, but we cannot guarantee 100% accuracy! For reference only, and not investment advice.",
    合约检测: "Token Security Detection",
    请输入合约地址: "Please enter the contract address",
    请选择: "Please Select",
    基本信息: "Base Info",
    已放弃所有权: "No Owner",
    未丢弃所有权: "Have Owner",
    未放弃所有权: "Have Owner",
    风险分析: "Risk Analysis",
    持币信息: "Token Info: (holder:{hold} Top 10:{p}%)",
    错误: "Error",
    请输入正确的合约地址: "Please enter a valid contract address",
    代币名称: "Token Name",
    代币符号: "Token Symbol",
    发行量: "Total Supply",
    合约创建者: "Contract Creator",
    合约所有权: "Contract Ownership",
    合约是否开源: "Is the contract open-source?",
    合约未开源: "Not Open Source",
    合约已开源: "Open Source",
    买入费率: "Buy Fee",
    卖出费率: "Sell Fee",
    安全: "Safe",
    是貔貅: "Is Pi Xiu",
    是否貔貅: "Is Pi Xiu",
    可增发: "Mintable",
    是否可增发: "Is Mintable",
    是代理合约: "Is Proxy Contract",
    是否是代理合约: "Is it a Proxy Contract?",
    可冷却: "Can Cool Down",
    是否交易冷却: "Is Trading Cooldown Enabled?",
    可暂停: "Can Pause",
    是否暂停交易: "Is Trading Paused?",
    存在白名单: "Whitelist Exists",
    是否有交易白名单: "Is there a Trading Whitelist?",
    存在黑名单: "Blacklist Exists",
    是否有交易黑名单: "Is there a Trading Blacklist?",
    合约可以自毁: "Contract Can Self-Destruct",
    合约能否自毁: "Can the Contract Self-Destruct?",
    能找回权限: "Can Recover Permissions",
    是否能找回权限: "Can Permissions Be Recovered?",
  },
  other: {
    p1: "More official website templates, stay tuned!",
    p2: "If the existing templates do not meet your needs, please contact our",
    p3: "Staff",
    p4: "for custom official website development. You can also join our",
    p5: "discussion group",
    p6: "to provide feedback:",
  },
  website: {
    需要定制官网请联系我们商务: "Contact us",
  },
  dashboard: {
    简介: "About Panda Tool ",
    描述: "PandaTool is an industry-leading Web3.0 multi-chain tool platform, offering products such as small cross-chain tools, token minting tools, Batch transfer tools, Batch aggregation tools, and trading tools.",
    实用工具: "Non-EVM Tools",
    跨链闪兑: "PandaBridge",
    Solana发币: "Solana Tools",
    Tron发币: "Tron Tools",
    Ton发币: "Ton Tools",
    Sui发币: "Sui Tools",
    第三方链接: "Third-Party platforms",
    粉红预售: "PinkSale",
    AVE数据: "Ave.ai",
    币售商城: "Bisell",
    区块周刊: "BlockWeeks",
    加密驱动社区: "Crypto-Driven Community",

    帮助文档: "Help Documentation",
    帮助文档1: "Help & Support",
    Youtube演示视频: "YouTube ",
    Dailymotion演示视频: "Dailymotion",
    Tiktok演示视频: "TikTok ",
    开放与合作: "Open & Collaboration",
    p1: "PandaTool embraces an open philosophy, welcoming collaboration with talented individuals and institutions. If you're passionate about Web3 and have project or technical needs, reach out to us—let's build the future of Web3 together!",
    p2: "",
    联系我们: "Contact Us",
    商务咨询: "Business Consultation",
    官方电报交流群: "Telegram ",
    官方推特: "Twitter / X",
    Github合约库: "Github",
  },
  tools: {
    暂不支持此链: "This chain is not supported!",
    联系定制: "If needed, please contact the administrator for customization",
    流动性修复工具: "Liquidity Fix Tool",
    说明: "English, please",
    选择交易所: "Choose Exchange",
    请选择: "Please Select",
    V2池子地址: "V2 Pool Address",
    请输入池子地址: "Please enter the pool address",
    查询池子: "Check Pool",
    池子代币数量: "Pool Token Amount",
    池子代币数量说明: "Explanation of Pool Token Amount",
    请先查询池子获取池子内代币信息:
      "Please check the pool first to get token info in the pool",
    目标价格: "Target Price",
    目标价格说明: "Target Price Explanation",
    需添加超过池子内的代币数量才可自由修改价格:
      "You must add more tokens than the current amount in the pool to modify the price freely",
    核算加池数量: "Calculate Add Pool Amount",
    加池数量: "Add Pool Amount",
    加池数量说明: "Explanation of Add Pool Amount",
    加池数量包括转入池子的数量和真正添加LP的数量:
      "Add Pool Amount includes the amount transferred to the pool and the actual amount added as LP",
    转入池子的代币无法获得LP通证:
      "Tokens transferred to the pool cannot earn LP tokens and cannot be retrieved",
    默认的加池数量为0: "The default add pool amount is 0.0000000000001",
    超过的部分将转入池子用于调整价格:
      "The excess will be transferred to the pool to adjust the price",
    授权: "Approve",
    授权说明: "Authorization Explanation",
    修复方法需使用合约完成授权选择默认值即可:
      "The fix method requires using a contract to complete, select the default authorization",
    需先将代币打入合约再由合约在一次交易中完成转入池子和加池的所有步奏:
      "You must first deposit tokens into the contract, which will complete all steps of transferring to the pool and adding to the pool in one transaction",
    否则攻击者可能提取出手动打入的代币:
      "Otherwise, attackers may withdraw the manually deposited tokens",
    造成资金损失: "This may result in financial loss",
    正在授权: "Approving",
    已授权: "Approved",
    有限制交易功能的代币:
      "Tokens with restricted trading features, such as manually enabling trading or adding to pools, require the repair contract address to be added to the whitelist in advance",
    修复合约: "Repair Contract",
    复制: "Copy",
    请稍后: "Please wait",
    确认修复: "Confirm Repair",
    池子地址不正确: "Incorrect pool address",
    获取token0简称错误: "Error getting token0 abbreviation",
    获取池子内代币数量错误: "Error getting token amount in pool",
    请输入正确的数字格式: "Please enter a valid number format",
    数量错误请提高第一个代币的数量:
      "Quantity error, please increase the amount of the first token!",
    数量错误请提高第二个代币的数量:
      "Quantity error, please increase the amount of the second token!",
    授权失败请重试: "Authorization failed, please try again",
    已提交等待区块确认: "Submitted, awaiting block confirmation",
    token0未授权: "token0 not authorized",
    token1未授权: "token1 not authorized",
    修复成功: "Repair successful",
    修复失败: "Repair failed, please try again!",
    已复制: "Copied",
  },
  liquidity: {
    暂不支持此链: "This chain is not supported!",
    联系定制: "If needed, please contact the administrator for customization",
    创建流动池: "Create Liquidity Pool",
    创建并买入流动池: "Create & Bundle Buy",
    创建说明: "Create a liquidity pool for your token so users can trade it",
    捆绑说明:
      "When creating a liquidity pool, bundle buy to gain an early advantage.",
    管理说明:
      "Check and manage your liquidity pool easily, add or remove liquidity.",
    加池类型: "Liquidity Pool Type",
    PancakeV2: "PancakeV2",
    PancakeV3稳定池: "PancakeV3",
    选择底池代币: "Select Base Token",
    请选择: "Please Select",
    其他代币: "Other Tokens",

    加池代币地址: "Token Address for Liquidity",
    请输入代币地址: "Please enter token address",
    查询代币: "Search Token",
    加池数量: "Token Amount for Liquidity",
    加池数量说明: "Amount Details",
    请先查询代币获取代币信息:
      "Search the token first to load token and pool info",
    全部: "All",
    余额: "Balance",
    预估价格: "Estimated Price",
    授权: "Approve",
    授权说明: "Approval Instructions",
    修复方法需使用合约完成授权选择默认值即可:
      "Approval must be done via contract, use default value",
    正在授权: "Approving",
    已授权: "Approved",
    有限制交易功能的代币:
      "Token has transfer restrictions. Please whitelist the router contract.",
    加池合约: "Router Contract",
    复制: "Copy",
    请稍后: "Please wait...",
    立即加池: "Add Liquidity Now",
    代币地址地址不正确: "Invalid Token Address",
    请选择底池代币: "Please select a base token",
    获取代币简称错误: "Failed to fetch token symbol",
    获取代币余额错误: "Failed to fetch token balance",
    获取代币精度错误: "Failed to fetch token decimals",
    获取底池代币余额错误: "Failed to fetch base token balance",
    获取底池代币简称错误: "Failed to fetch base token symbol",
    获取底池代币精度错误: "Failed to fetch base token decimals",
    加池代币数量超过余额: "Token amount exceeds balance",
    加池代币数量不能为0: "Token amount cannot be 0",
    加池底池代币数量超过余额: "Base token amount exceeds balance",
    加池底池代币数量不能为0: "Base token amount cannot be 0",
    流动性池已存在: "Liquidity pool already exists. Please add liquidity.",
    代币正常: "Token valid. Please input liquidity amount.",
    授权成功: "Approval Successful",
    授权失败请重试: "Approval failed. Please try again.",
    已提交等待区块确认: "Submitted. Awaiting block confirmation...",
    加池代币未授权: "Liquidity token not approved",
    底池代币未授权: "Base token not approved",
    加池成功: "Liquidity Added Successfully",
    加池失败: "Add Liquidity Failed",
    预估GAS失败: "Failed to estimate gas. Check if router is whitelisted.",
    已复制: "Copied",
    预估消耗: "Estimated Cost",
    池子地址: "Pool Address",
    未创建: "Not Created",
    请确保钱包余额充足余额不足将:
      "Ensure sufficient balance. Insufficient funds may cause failure.",
    前往管理流动性: "Go Manage Liquidity",
    加池完成: "Liquidity Added",
    创建钱包私钥: "Create Wallet Private Key",
    请输入私钥: "Enter Private Key",
    私钥格式错误: "Private key format error at line {index}",
    创建钱包: "Create Wallet",
    创建钱包说明:
      "Private keys are processed locally and never uploaded. Transfer assets after use.",
    钱包地址: "Wallet Address",
    捆绑买入地址: "Bundle Buy Address",
    批量导入私钥: "Batch Import Private Keys",
    每行一个私钥: "One private key per line, no symbols or spaces",
    点击导入将自动移除错误重复私钥:
      "Click import to remove invalid or duplicate keys automatically",
    导入: "Import",
    私钥: "Private Key",
    买入金额: "Buy Amount",
    操作: "Action",
    请输入买入数量: "Enter Purchase Amount",
    请先刷新余额: "Please refresh balance first",
    余额不足: "Insufficient Balance",
    有限制交易功能的代币捆绑:
      "This token has restrictions. Please whitelist router and bundle buy address.",
    捆绑买入地址说明:
      "Bundle buy only supports payment in {chainSymbol}. If no liquidity exists, purchase will fail.",
    此功能最多支持:
      "Supports up to 25 addresses per bundled purchase. Fees paid by liquidity wallet.",
    第id个地址买入数量不正确: "Invalid amount for address {id}",
    购买金额不能大于钱包余额:
      "Amount + 0.005 gas must not exceed wallet balance",
    购买最小金额: "Minimum purchase amount is 0.005",
    请移除不正确的捆绑买入地址:
      "At least one valid bundle buy address required. Remove invalid ones.",
    捆绑交易失败:
      "Bundled transaction failed. Check token restrictions and try again.",
    预计获取数量: "Expected Amount",
    管理流动性: "Manage Liquidity",
    流动性类型: "Liquidity Type",
    未找到您的池子: "No Liquidity Found",
    查找流动性: "Find Liquidity",
    查询流动性: "Query Liquidity",
    当前地址未发现任何流动性池:
      "No V2 liquidity found. Check wallet or create a pool.",
    代币对: "Token Pair",
    池子地址: "Pool Address",
    LP数量: "LP Amount",
    LP说明: "LP Amount explanation",
    LP是流动性的权益凭证:
      "LP is the proof of ownership you receive after depositing tokens into the liquidity pool. It represents your share of the pool, not the exact amount of tokens you deposited.",
    锁池: "Lock Pool",
    添加: "Add",
    移除: "Remove",
    添加流动性: "Add Liquidity",
    移除流动性: "Remove Liquidity",
    移除百分比: "Remove Percentage",
    移除数量: "Remove LP Amount",
    预计获取数量: "Estimated Return Token Amount",
    接收钱包: "Receiving Wallet",
    LP代币授权: "Approve LP Token",
    税费代币的预估获取数量可能出现偏差:
      "Estimates may vary for tax tokens. Refer to actual received amount.",
    有限制加池功能的代币:
      "Token has add Liquidity restrictions. Please whitelist the user and router addresses.",
    有限制撤池功能的代币:
      "Token has withdrawal restrictions. Please whitelist withdrawal and router addresses.",
    撤池完成: "Liquidity Removed",
    确认撤池: "Confirm Removal",
    当前预估价格: "Current Estimated Price",
    转换价格: "Convert Price",
    加池代币需按照当前价格比例添加:
      "Tokens must be added according to current price ratio",
    查询失败检查网络: "Query failed. Please check your network.",
    请先创建流动性: "Please create liquidity first",
    流动性池正常: "Liquidity pool found. Awaiting data...",
    获取代币资料错误: "Failed to fetch token data. Please retry.",
    收币地址不正确: "Receiving wallet address is incorrect",
    撤池成功: "Liquidity Removal Success",
    撤池失败: "Liquidity Removal Failed. Please try again",
    正在加载: "Loading···",
    请先授权LP: "Please approve LP token first",
    添加或移除流动性时: "Add / Remove Liquidity, charge {fee} for service fee",
    请确保钱包内有不少于:
      "Before operate,please make sure your wallet has at least ",
    前往创建流动性: "Go Create Liquidity",

    锁池百分比: "Lock Percentage",
    锁池数量: "Lock LP Amount",
    解锁日期: "Unlock Time",
    请选择解锁日期: "Please select unlock time",
    锁池标题: "Lock Title",
    请输入锁池标题: "Optional,Please input your lock title",
    解锁时间不正确: "Please select unlock time",
    锁池完成: "Lock Completed",
    确认锁池: "Confirm Lock",
    锁池成功: "Lock Liquidity Success",
    锁池失败: "Lock Liquidity Failed, please retry",
  },
  lock: {
    暂不支持此链: "This chain is not supported",
    联系定制: "If needed, please contact the administrator for customization",
    创建锁仓: "Create Lock",
    锁仓说明:
      "Lock Tokens, Reduce Circulating Supply, Support Price Appreciation",
    锁仓代币地址: "Lock Token Address",
    请输入代币地址: "Please enter token address",
    请稍后: "Please wait",
    查询代币: "Search Token",
    锁仓数量: "Lock Amount",
    全部: "All",
    余额: "Balance",
    解锁日期: "Unlock Date",
    请选择解锁日期: "Please select unlock date",
    锁仓标题: "Lock Title",
    请输入锁仓标题: "Optional, please input lock title",
    授权: "Approve",
    正在授权: "Approving",
    已授权: "Approved",
    有限制功能的代币:
      "Lock function not supported for tokens with max‑holding limits or reset balance features",
    锁仓完成: "Lock Completed",
    立即锁仓: "Lock Now",
    代币地址地址不正确: "Invalid Token Address",
    获取代币简称错误: "Failed to fetch token symbol",
    获取代币余额错误: "Failed to fetch token balance",
    获取代币精度错误: "Failed to fetch token decimals",
    代币正常: "Token valid, please enter lock amount",
    锁仓数量超过余额: "Lock amount exceeds balance",
    锁仓数量不能为0: "Lock amount cannot be 0",
    请先查询代币: "Please search token first",
    授权失败请重试: "Approval failed, please try again",
    已提交等待区块确认: "Submitted. Awaiting block confirmation",
    授权成功: "Approval Successful",
    锁仓代币未授权: "Lock token not approved",
    锁仓成功: "Lock Successful",
    锁仓失败: "Lock Failed, please retry",
    复制: "Copy",
    已复制: "Copied",
    锁仓控制台: "Lock Console",
    锁仓控制台说明:
      "View all lock info in the console, unlock your token directly",
    锁仓类型: "Lock Type",
    锁池: "Liquidity",
    锁币: "Token",
    查找锁仓: "Find Lock",
    代币地址: "Token Address",
    请输入代币地址: "Please enter token address",
    前往锁仓: "Go Lock",
    查询锁仓: "Query Lock",
    当前地址未发现锁仓记录:
      "No lock records found for this address, please go create a lock",
    代币简称: "Token Symbol",
    解锁: "Unlock",
    弃权: "Renounce",
    正在加载: "Loading···",
    查询失败检查网络: "Query lock failed, please check network",
    获取代币资料失败:
      "Failed to fetch token info, please check network and retry",
    代币地址地址不正确: "Invalid Token Address",
    未发现锁仓记录: "No lock records found",
    解锁成功: "Unlock Successful, please check your wallet for received tokens",
    解锁失败: "Unlock Failed, please retry",
    弃权成功:
      "Renounce Successful, tokens permanently burned and unrecoverable",
    弃权失败: "Renounce Failed, please retry",
    弃权说明: "Renounce Ownership description",
    弃权将永久锁仓:
      "Renounce ownership of the lock will lock the tokens forever, they cannot be recovered",
  },
  gather: {
    请切换至币安链: "Please switch to Binance Chain",
    该工具需开通会员: "This tool requires a membership",
    一天: "1 day ",
    一周: "1 week ",
    一月: "1 month ",
    永久: "forever ",
    购买: "Buy",
    未开通VIP: "VIP not activated",
    到期时间: "Expiration time",
    错误404: "Error 404",
    出现错误请检查您的网络或者余额:
      "An error occurred, please check your network or balance",
  },
  preSale: {
    暂不支持此链: "This chain is not supported",
    如有需要请联系管理员定制:
      "If needed, please contact the administrator for customization",
    console: {
      预售列表: "Presale List",
      合约地址: "Token Address",
      预售名称: "Presale Name",
      所有者: "Owner",
      创建时间: "Creation Time",
      操作: "Actions",
      进入控制台: "Enter Console",
      获取mint预售错误请检查网络:
        "Error fetching mint presale, please check the network!",
      已复制: "Copied",
    },
    mintAddSale: {
      Mint加池: "Mint & Liquidity",
      预售即加池:
        "Lets pairs tokens with BNB to form a Pancake liquidity pool, giving LP tokens to users.",
      教程: "Tutorial",
      "预售即加池、自动分发LP、人人都是做市商":
        "Presale with automatic liquidity addition, LP distribution, everyone is a market maker",
      预售名称: "Presale Name",
      预售代币地址: "Presale Token Address",
      每份价格: "price",
      每份数量: "amountPerUnits",
      总份数: "mintLimit",
      单次预售最大份数: "accEachLimit",
      单钱包预售最大份数: "accMintLimit",
      选择交易所: "Select dexs",
      请选择: "Please select",
      添加流动性时需要添加代币与底池代币之间的交易对:
        "When adding liquidity, you need to add a trading pair between the token and the base pool token, otherwise dividends and backflow cannot be processed properly",
      加池比例: "addPart",
      加池预售用户参与预售的同时会按照该比例自动添加流动性:
        "In liquidity addition presale, users automatically add liquidity based on this ratio while participating in the presale",
      创建合约: "Create Contract",
      费用: "Fees",
      合约地址: "Contract Address",
      预计生成地址: "Estimated Generated Address",
      预估手续费: "Estimated Fee",
      请确保钱包余额充足余额不足将:
        "Please ensure sufficient wallet balance, insufficient balance will",
      创建失败: "Creation Failed",
      开源参数: "Open Source Parameters",
      浏览器查看: "View in Browser",
      开源教程: "Open Source Tutorial",
      复制源代码: "Copy Source Code",
      复制构造参数: "Copy Constructor Parameters",
      构造参数无法找回若不立即开源请复制后保存到本地文档:
        "Constructor parameters cannot be retrieved, if not open-sourced immediately, please copy and save to a local document",
      进入控制台: "Enter Console",
      预售代币地址不正确: "Incorrect Presale Token Address",
      价格格式不正确: "Incorrect Price Format",
      每份数量格式不正确: "Incorrect Quantity per Share Format",
      交易所路由地址不正确: "Incorrect Exchange Router Address",
      公链错误请确保钱包与选择的公链一致:
        "Blockchain error: Please ensure the wallet matches the selected blockchain",
      创建失败请重试: "Creation Failed, Please Retry",
      创建成功: "Creation Successful",
      已复制: "Copied",
    },
    mintAddSaleDetail: {
      预售控制台: "Presale Console",
      复制链接: "Copy Link",
      基本信息: "Basic Info",
      预售名称: "Presale Name",
      预售地址: "Presale Address",
      预售代币简称: "Presale Token Symbol",
      预售代币地址: "Presale Token Address",
      预售模版: "Presale Template",
      所有者: "Owner",
      预售情况: "Presale Status",
      已售份数: "Sold Shares",
      合约内代币数量: "Tokens in Contract",
      预售参数: "Presale Parameters",
      价格: "Price",
      每份数量: "amountPerUnits",
      总份数: "mintLimit",
      单次Mint最大份数: "Max Shares per Mint",
      单钱包最大份数: "accMintLimit",
      加池比例: "setAddPart",
      交易所: "Dex",
      池子地址: "Pool Address",
      营销钱包: "fundAddress",
      捐赠代币比例: "Donation Token Ratio",
      接收代币地址: "donateTokenAddr",
      预售控制: "Presale Control",
      转让所有权: "transferOwnership",
      转让地址: "Transfer Address",
      取消: "Cancel",
      确定: "Confirm",
      开启预售: "Start Presale",
      提取合约内代币: "Claims token",
      预售代币: "Presale Token",
      数量: "Quantity",
      参数控制: "Parameter Control",
      修改每份价格: "Modify setPrice",
      修改Mint每份价格: "Modify Mint Price per Share",
      每份价格: "setPrice",
      修改每份数量: "setAmountPerUnitse",
      修改总份数: "setMintLimit",
      修改单次Mint最大份数: "Modify setAccEachLimit",
      修改单钱包最大份数: "Modify setAccMintLimit",
      加池控制: "Liquidity Control",
      修改营销钱包: "setFundAddress",
      营销钱包地址: "FundAddress",
      修改加池比例: "setAddPart",
      捐赠控制: "Donation Control",
      修改接收钱包: "setDonateAddress",
      修改接收钱包1: "Modify DonateAddress",
      修改捐赠比例: "Modify setDonatePart {chainSymbol} Ratio",
      修改捐赠比例1: "Modify setDonatePart Ratio",
      修改接收代币钱包: "Modify setDonateTokenAddress {symbol} T",
      修改接收代币钱包1: "Modify setDonateTokenAddress ",
      修改捐赠代币比例: "Modify Donation {symbol}  Ratio",
      修改捐赠代币比例1: "Modify Donation  Ratio",
      捐赠比例: "Donation Ratio",
      预售官网示例: "Presale Website Example",
      需要定制官网请联系商务: "Need a custom website? Contact Business",
      捐赠: "Donate",
      比例: "Ratio",
      接收: "Receive",
      地址: "Address",
      合约内: "In Contract",
      ETHBalanceLabel: "{eth} In Contract",
      donatePartLabel: "Donation {eth} ratio",
      donateAddrLabel: "Receive {eth} address",
      mint加池: "Mint & Liquidity",
      mint捐赠: "Mint & Donate",
      标准mint: "Simple Mint",
      关闭: "Close",
      开启: "Open",
      获取合约拥有者错误请检查网络:
        "Failed to fetch contract owner, please check the network!",
      获取名称错误请检查网络: "Failed to fetch name, please check the network!",
      获取代币地址错误请检查网络:
        "Failed to fetch token address, please check the network!",
      获取代币精度错误请检查网络:
        "Failed to fetch token precision, please check the network!",
      获取代币简称错误请检查网络:
        "Failed to fetch token symbol, please check the network!",
      获取代币数量错误请检查网络:
        "Failed to fetch token quantity, please check the network!",
      获取开启预售失败请检查网络:
        "Failed to start presale, please check the network!",
      获取捐赠模式失败请检查网络:
        "Failed to fetch donation mode, please check the network!",
      获取捐赠ETH比例失败请检查网络:
        "Failed to fetch donation ETH ratio, please check the network!",
      获取捐赠代币比例失败请检查网络:
        "Failed to fetch donation token ratio, please check the network!",
      获取接收ETH地址失败请检查网络:
        "Failed to fetch receive ETH address, please check the network!",
      获取接收代币地址失败请检查网络:
        "Failed to fetch receive token address, please check the network!",
      获取加池模式失败请检查网络:
        "Failed to fetch liquidity mode, please check the network!",
      获取交易所错误请检查网络:
        "Failed to fetch exchange, please check the network!",
      获取池子地址错误请检查网络:
        "Failed to fetch pool address, please check the network!",
      获取每份价格失败请检查网络:
        "Failed to fetch price per share, please check the network!",
      获取每份数量失败请检查网络:
        "Failed to fetch quantity per share, please check the network!",
      获取最大MINT数量失败请检查网络:
        "Failed to fetch max MINT quantity, please check the network!",
      获取单次最大MINT数量失败请检查网络:
        "Failed to fetch max MINT quantity per transaction, please check the network!",
      获取单钱包最大MINT数量失败检查网络:
        "Failed to fetch max MINT quantity per wallet, please check the network!",
      获取营销钱包错误请检查网络:
        "Failed to fetch marketing wallet, please check the network!",
      已提交等待区块确认: "Submitted, waiting for block confirmation!",
      修改成功: "Successfully modified!",
      确认失败请前往浏览器查看:
        "Confirmation failed! Please check the browser!",
      出错了: "An error occurred!",
      地址格式不正确: "Incorrect address format",
      修改失败: "Modification failed",
      合约内余额不足: "Insufficient balance in contract",
      请输入正确的数字格式: "Please enter a valid numeric format",
      单钱包Mint最大份数不可设置为0:
        "Max Mint shares per wallet cannot be set to 0",
      单次Mint最大份数不可设置为0:
        "Max Mint shares per transaction cannot be set to 0",
      添加比例需在之间: "Addition ratio must be between 1 and 100",
      捐赠比例需在之间: "Donation ratio must be between 0 and 100",
      已复制: "Copied",
      总份数不可设置为0: "Total shares cannot be set to 0",
      获取合约ETH数量错误请检查网络:
        "Failed to fetch contract ETH quantity, please check the network!",
      正在授权请稍等: "Approving, please wait!",
      授权完成请确认开启预售:
        "Authorization completed, please confirm to start presale!",
      开启预售成功: "Presale started successfully",
      开启预售失败: "Failed to start presale",
      授权失败: "Approve Error",
    },
    mintDonate: {
      Mint捐赠: "Mint & Donate",
      教程: "Tutorial",
      公平做慈善捐赠V神来炒作打造新叙事:
        "Donate a portion of the presale tokens or BNB to celebrity wallets (e.g., Vitalik's) for future promotion.",
      预售名称: "Presale Name",
      预售代币地址: "Presale Token Address",
      每份价格: "price",
      每份数量: "amountPerUnits",
      总份数: "mintLimit",
      单次预售最大份数: "accEachLimit",
      单钱包预售最大份数: "accMintLimit",
      捐赠代币比例: " donateTokenPart",
      接收代币地址: "Receive Token Address",
      创建合约: "Create Contract",
      费用: "Fees",
      合约地址: "Contract Address",
      预计生成地址: "Estimated Generated Address",
      预估手续费: "Estimated Transaction Fees",
      请确保钱包余额充足余额不足将:
        "Please ensure your wallet has sufficient balance, insufficient balance will",
      创建失败: "Creation Failed",
      开源参数: "Open Source Parameters",
      浏览器查看: "View in Browser",
      开源教程: "Open Source Tutorial",
      复制源代码: "Copy Source Code",
      复制构造参数: "Copy Constructor Parameters",
      构造参数无法找回若不立即开源请复制后保存到本地文档:
        "Constructor parameters cannot be retrieved, if not open-sourced immediately, please copy and save them locally",
      进入控制台: "Enter Console",
      donatePartLabel: "Donation {eth} ratio",
      donateAddrLabel: "Accept {eth} address",
      // donateAddrLabel:"接收{eth}地址",
      捐赠: "Donate",
      比例: " donateTokenPart",
      接收: " donateTokenAddr",
      地址: "Address",
      预售代币地址不正确: "Incorrect presale token address",
      价格格式不正确: "Incorrect price format",
      每份数量格式不正确: "Incorrect quantity per share format",
      交易所路由地址不正确: "Incorrect exchange router address",
      创建失败请重试: "Creation failed, please try again!",
      公链错误请确保钱包与选择的公链一致:
        "Blockchain error: Please ensure your wallet matches the selected blockchain!",
      创建成功: "Creation Successful",
      已复制: "Copied",
    },
    simpleMint: {
      教程: "Tutorial",
      标准Mint: "Simple Mint",
      转账即预售链上可查去中心化:
        "Lets users send BNB to a presale contract address and receive tokens automatically at a set ratio.",
      预售名称: "Presale Name",
      预售代币地址: "Presale Token Address",
      每份价格: "price",
      每份数量: "amountPerUnits",
      总份数: "mintLimit",
      单次预售最大份数: "accEachLimit",
      单钱包预售最大份数: "accMintLimit",
      创建合约: "Create Contract",
      费用: "Fees",
      合约地址: "Contract Address",
      预计生成地址: "Estimated Generated Address",
      预估手续费: "Estimated Transaction Fees",
      请确保钱包余额充足余额不足将:
        "Please ensure your wallet has sufficient balance, insufficient balance will",
      创建失败: "Creation Failed",
      开源参数: "Open Source Parameters",
      浏览器查看: "View in Browser",
      开源教程: "Open Source Tutorial",
      复制源代码: "Copy Source Code",
      复制构造参数: "Copy Constructor Parameters",
      构造参数无法找回若不立即开源请复制后保存到本地文档:
        "Constructor parameters cannot be retrieved, if not open-sourced immediately, please copy and save them locally",
      进入控制台: "Enter Console",
      预售代币地址不正确: "Incorrect presale token address",
      价格格式不正确: "Incorrect price format",
      每份数量格式不正确: "Incorrect quantity per share format",
      交易所路由地址不正确: "Incorrect exchange router address",
      创建失败请重试: "Creation failed, please try again!",
      公链错误请确保钱包与选择的公链一致:
        "Blockchain error: Please ensure your wallet matches the selected blockchain!",
      创建成功: "Creation Successful",
      已复制: "Copied",
    },
  },
  coinRelease: {
    modeType: {
      黑洞分红: "Burning Reflection",
      标准模版: "Standard",
      三14协议: "314 Protocol",
      LP分红: "LP Reflection",
      LP分红推荐奖励: "LP Reflection Pro",
      LP挖矿推荐奖励: "LP Mining",
      Mint暴力分红: "Holders Reflection",
      Mint燃烧底池: "Reserve Burn",
      分红本币: " Holders Dividend",
      持币复利推荐奖励: "Holders Interest",
    },
    common: {
      暂不支持此链: "This chain is not supported at the moment!",
      如有需要请联系管理员定制:
        "If needed, please contact the administrator for customization.",
      代币全称: "Name ",
      全称: "Name",
      代币简称: "Symbol",
      代币模版: "Token template",
      经济模型: "Economic model",
      简称: "Name",
      底池代币: "currency",
      单钱包持币上限: "maxWalletAmount",
      权限控制: "Control",
      丢弃权限: "renounceOwnership",
      所有者: "Owner",
      天: "day",
      发行量: "TotalSupply",
      总量: "TotalSupply",
      精度: "Decimals",
      买入税率: "Buy tax",
      买入营销税率: "buyFundFee",
      买入分红税率: "buyRewardFee",
      买入回流税率: "buyLPFee",
      买入销毁税率: "buy_burnFee",
      营销税率: "sellFundFee",
      销毁税率: "sell_burnFee",
      营销钱包: "fundAddress",
      分红税率: "sellRewardFee",
      回流税率: "sellLPFee",
      推荐税率: "inviterFee",
      营销: "fund",
      销毁: "Burn",
      回流: "Reflow",
      使用当前钱包: "Use current wallet",
      请选择: "Please select",
      创建合约: "Create contract",
      合约地址: "Contract address",
      转让地址: "Transfer address",
      费用: "Fee",
      确定: "Confirm",
      取消: "Cancel",
      预计生成地址: "Expected address generation",
      预估手续费: "Estimated fee",
      创建失败: "Creation failed",
      请确保钱包余额充足余额不足将:
        "Please ensure sufficient wallet balance, insufficient balance will result in",
      浏览器查看: "View in browser",
      开源教程: "Open-source tutorial",
      开源参数: "Open-source value",
      复制源代码: "Copy source code",
      复制构造参数: "Copy constructor parameters",
      构造参数无法找回若不立即开源请复制后保存到本地文档:
        "Constructor parameters cannot be retrieved. If not open-sourced immediately, please copy and save them to local documents.",
      进入控制台: "Enter console",
      已复制: "Copied!",
      创建成功: "Creation successful!",
      修改成功: "Modification successful!",
      已提交等待区块确认: "Submitted, awaiting block confirmation!",
      创建失败请重试: "Creation failed, please try again!",
      确认失败请前往浏览器查看:
        "Confirmation failed! Please check in the browser!",
      出错了: "An error occurred!",
      地址格式不正确: "Address format is incorrect!",
      公链错误请确保钱包与选择的公链一致:
        "Blockchain error: Please ensure the wallet matches the selected blockchain!",
      最大持币金额格式不正确: "Maximum holding amount format is incorrect!",
      初始供应量格式不正确: "Initial supply format is incorrect!",
      营销钱包地址不正确: "Marketing wallet address is incorrect!",
      营销钱包地址: "Marketing wallet address",
      代币详情: "Token details",
      复制链接: "Copy link",
      教程: "Tutorial",
      税率说明: "Tax rate explanation",
      修改税率: "Modify tax ",
      选择交易所: "Select Dexs",
      卖出税率: "Sell tax ",
      转让所有权: "transferOwnership",
      请输入正确的数字格式: "Please enter a valid numeric format",
      可设置单个钱包持有的最大代币数量此开关关闭后无法再次开启:
        "You can set the maximum number of tokens a single wallet can hold. This switch cannot be turned on again once turned off.",
      最大持币量: "enableWalletLimit",
      基本信息: "Basic information",
      合约地址: "Contract address",
      流动性控制: "Liquidity control",
      修改营销钱包: "setFundAddress",
      获取中: "Fetching...",
      无上限: "No limit",
      交易所: "Exchange",
      获取合约拥有者错误请检查网络:
        "Error fetching contract owner, please check the network!",
      获取名称错误请检查网络: "Error fetching name, please check the network!",
      获取简称错误请检查网络:
        "Error fetching abbreviation, please check the network!",
      获取精度错误请检查网络:
        "Error fetching precision, please check the network!",
      获取供应量错误请检查网络:
        "Error fetching supply, please check the network!",
      获取买入营销费率错误请检查网络:
        "Error fetching buy marketing fee rate, please check the network!",
      获取买入销毁费率错误请检查网络:
        "Error fetching buy burn fee rate, please check the network!",
      获取卖出营销费率错误请检查网络:
        "Error fetching sell marketing fee rate, please check the network!",
      获取卖出销毁费率错误请检查网络:
        "Error fetching sell burn fee rate, please check the network!",
      获取营销钱包错误请检查网络:
        "Error fetching marketing wallet, please check the network!",
      获取冷却时间错误请检查网络:
        "Error fetching cooldown time, please check the network!",
      获取持币限制开关错误请检查网络:
        "Error fetching holding limit switch, please check the network!",
      交易中指定额度的代币将会自动添加到流动池内保证交易始终存在流动性:
        "Tokens specified in the transaction will automatically be added to the liquidity pool to ensure constant liquidity in the transaction.",
      交易中指定额度的代币将会自动转入营销钱包中用于项目方做其他营销:
        "Tokens specified in the transaction will automatically be transferred to the marketing wallet for the project team to use for other marketing activities.",
      交易中指定额度的代币将会被打入黑洞地址变相实现通缩机制:
        "Tokens specified in the transaction will be sent to the black hole address to indirectly implement a deflationary mechanism.",
      交易中指定额度的代币用来购买分红代币并发送给LP持有者:
        "Tokens specified in the transaction will be used to purchase dividend tokens and sent to LP holders.",
      买入总税率不能超过25交易总税率不能超过50:
        "The total buy tax rate cannot exceed 25%, and the total transaction tax rate cannot exceed 50%.",
      选择底池代币: "Select Currency",
      添加流动性时需要添加代币与底池代币之间的交易对否则无法进行正常分红与回流:
        "When adding liquidity, you need to add the trading pair between the token and the liquidity pool token, otherwise, normal dividends and reflow cannot be performed.",
      分红代币: "selectReward",
      其他代币: "otherReward",
      要分红的代币此代币必须存在公链原生币池子如BNBETH等:
        "The token for dividends must exist in the native coin pool of the blockchain, such as BNB, ETH, etc.",
      手动开启交易: "enableOffTrade",
      税率开关: "enableChangeTax",
      可在创建代币后手动调整税率买卖税率各不能超过25此开关关闭后无法再次开启:
        "After creating the token, you can manually adjust the tax rates, with buy and sell tax rates not exceeding 25%. This switch cannot be turned on again once turned off.",
      可拉黑部分钱包地址令其无法交易此开关关闭后无法再次开启:
        "You can blacklist certain wallet addresses to prevent them from trading. This switch cannot be turned on again once turned off.",
      交易中指定额度的代币将会转换为底池代币并自动转入营销钱包中用于项目方做其他营销:
        "Tokens specified in the transaction will be converted to liquidity pool tokens and automatically transferred to the marketing wallet for the project team to use for other marketing activities.",
      交易中指定额度的代币做为推荐奖励并分发给其上级:
        "Tokens specified in the transaction will be used as referral rewards and distributed to the referrer.",
      分红最小持币量不能为0:
        "The minimum holding amount for dividends （minHoldRefAmount） cannot be 0!",
      买卖营销税之和必须大于0:
        "The sum of buy and sell marketing taxes must be greater than 0!",
      交易控制: "Transaction control",
      开启交易: "launch",
      修改持币上限: "changeWalletLimit",
      持币上限: "MAXHold",
      关闭持币限制: "disableWalletLimit",
      设置持币白名单: "setisMaxEatExempt",
      添加白名单: "setisMaxEatExempt",
      移除白名单: "Remove setisMaxEatExempt",
      白名单地址: "Whitelist address",
      请输入地址每行一个地址不要添加任何符号:
        "Please enter the address, one per line, without adding any symbols.",
      设置税率白名单: "setFeeWhiteList",
      添加税率白名单: "setFeeWhiteList",
      移除税率白名单: "removeFeeWhiteList",
      税率白名单地址: "setFeeWhiteList",
      分红控制: "Dividend control",
      提取合约内代币: "setClaims",
      本币: "Current token",
      数量: "Amount",
      设置分红黑名单: "setExcludeHolder",
      分红黑名单地址: "setExcludeHolder address",
      获取交易所错误请检查网络:
        "Error fetching exchange, please check the network!",
      获取池子地址错误请检查网络:
        "Error fetching pool address, please check the network!",
      获取底池代币错误请检查网络:
        "Error fetching liquidity pool token, please check the network!",
      获取分红代币错误请检查网络:
        "Error fetching dividend token, please check the network!",
      获取买入回流费率错误请检查网络:
        "Error fetching buy reflow fee rate, please check the network!",
      获取买入分红费率错误请检查网络:
        "Error fetching buy dividend fee rate, please check the network!",
      获取卖出回流费率错误请检查网络:
        "Error fetching sell reflow fee rate, please check the network!",
      获取卖出分红费率错误请检查网络:
        "Error fetching sell dividend fee rate, please check the network!",
      获取跟卖比例错误请检查网络:
        "Error fetching follow-sell ratio, please check the network!",
      获取最大持币量错误请检查网络:
        "Error fetching maximum holding amount, please check the network!",
      获取手动交易开关错误请检查网络:
        "Error fetching manual trading switch, please check the network!",
      获取startTradeBlock错误请检查网络:
        "Error fetching startTradeBlock, please check the network!",
      获取黑名单开关错误请检查网络:
        "Error fetching blacklist switch, please check the network!",
      获取最大持币开关错误请检查网络:
        "Error fetching maximum holding switch, please check the network!",
      获取税率开关错误请检查网络:
        "Error fetching tax rate switch, please check the network!",
      修改失败: "修改失败!",
      合约内余额不足: "Insufficient balance in the contract!",
      池子地址: "Pool address",
      设置黑名单: "Set blacklist",
      添加黑名单: "Add blacklist",
      移除黑名单: "Remove blacklist",
      黑名单地址: "Blacklist address",
      税率控制: "Tax rate control",
      买入税率最大25: "Buy tax rate: Maximum 25%",
      卖出税率最大25: "Sell tax rate: Maximum 25%",
      加池税率最大为25: "Add pool tax rate: Maximum 25%",
      分红: "rewardFee",
      买入分红: "rewardFee",
      加池税率: "addLiquidityFee",
      撤池税率: "removeLiquidityFee",
      跟卖比例: "numTokensSellRate",
      杀抢跑机器人区块: "Kill bots block (kb)",
      交易空投地址数: "airdropNumbs",
      设置加池税率: "setAddLiquidityFee",
      设置撤池税率: "setRemoveLiquidityFee",
      杀开盘抢跑机器人: "Kill bots",
      抢跑区块: "setKb",
      注需手动开启交易开启交易的前X个区块内交易的地址被视为抢跑机器人自动加入黑名单:
        "Note: Trading must be manually enabled. Addresses trading in the first X blocks after trading is enabled will be considered front-running bots and automatically added to the blacklist.",
      该地址将不会收到LP分红: "This address will not receive LP dividends.",
      设置分红阈值: "setHolderRewardCondition",
      分红阈值说明:
        "When the amount of dividend tokens in the dividend wallet exceeds this value, dividends will start. The default is 0.1 dividend tokens, meaning dividends will only start when the value of the dividend tax reaches this amount. Setting it too high may result in long periods without dividends. It is strongly recommended that new users do not modify this option. (Summary: For USDT dividends, the default is fine; for WBNB dividends, it can be set lower, for example, 0.0001. Do not set this value too high, as any issues arising from doing so are not the platform's responsibility.)",
      获取加池费率错误请检查网络:
        "Error fetching add pool fee rate, please check the network!",
      获取撤池费率错误请检查网络:
        "Error fetching remove pool fee rate, please check the network!",
      获取空投数量错误请检查网络:
        "Error fetching airdrop quantity, please check the network!",
      获取杀机器人区块错误请检查网络:
        "Error fetching kill bot block, please check the network!",
      获取空投开关错误请检查网络:
        "Error fetching airdrop switch, please check the network!",
      推荐奖励: "generations rewards",
      推荐奖励说明: "generations rewards explanation",
      分红代数: "generations ",
      一代比例: "fristRate ",
      二代比例: "secondRate ",
      三代比例: "thirdRate ",
      剩余每代: "leftRate ",
      核算比例: "Calcula ratio",
      绑定推荐关系需要上级向下级转账一定数额的代币当下级回转后视为绑定成功:
        "To bind a referral relationship, the superior needs to transfer a certain amount of tokens to the inferior. Once the inferior transfers back, the binding is considered successful.",
      例默认最小转账金额为0上级可转账任意给下级下级回转任意数量最小转账金额可在控制台设置:
        "For example, the default minimum transfer amount is 0. The superior can transfer any amount to the inferior, and the inferior can transfer any amount back. The minimum transfer amount can be set in the console.",
      添加池子后的首次交易需要在控制台手动开启如关闭则添加流动性后立即可以进行交易交易开启后无法关闭:
        "The first transaction after adding liquidity needs to be manually enabled in the console. If disabled, transactions can begin immediately after adding liquidity. Once trading is enabled, it cannot be disabled.",
      区块数量: "Number of blocks",
      将对开启交易后在n个区块内交易的地址全部拉入黑名单用于防止机器人抢跑买入必须手动开启交易:
        "Addresses that trade within n blocks after enabling trading will be added to the blacklist to prevent bot front-running. Buying must be manually enabled.",
      自动空投: "airdropEnable",
      开启开关后用户交易时将会自动向随机地址空投小额代币以增加持币地址单次最多空投5个:
        "After enabling the switch, users will automatically airdrop small amounts of tokens to random addresses during transactions to increase the number of token holders. A maximum of 5 tokens can be airdropped at a time.",
      邀请奖励比例错误: "Referral reward ratio error",
      推荐: "Referral ",
      代奖励: " generation dividend",
      其他各代比例: " Other generation ratios",
      最小转账数量: " minTransAmount",
      注绑定推荐关系的最小数量回转确认后绑定成功默认为0:
        "Note: The minimum amount for binding a referral relationship, binding is confirmed after a transfer and return. Default is 0.",
      撤池税率最大为25: "Remove pool tax rate: maximum 25%",
      推荐奖励控制: "changeInviterFee control",
      修改推荐税率: "changeInviterFee",
      推荐税率买卖相同需保证买入总税率不能超过25卖出总税率不能超过25:
        "Referral tax rate for both buy and sell must ensure the buy total tax rate does not exceed 25% and the sell total tax rate does not exceed 25%.",
      设置推荐奖励比例: "Set referral reward ratio",
      奖励代数: "Reward generations",
      总比例: "Total ratio",
      确认前请先核算确认总比例为100:
        "Please calculate and confirm that the total ratio is 100% before confirming.",
      确定修改: "Confirm modification",
      设置最小转账金额: "Set minimum transfer amount",
      最小转账金额: "Minimum transfer amount",
      用于设定绑定推荐关系的最小转账金额如希望任意转账金额均可绑定推荐关系设置0即可:
        "Used to set the minimum transfer amount for binding referral relationships. If you want any transfer amount to bind the relationship, set it to 0.",
      例设定最小转账金额为上级需转账至少给下级下级至少回转给上级转账成功即绑定:
        "For example, set the minimum transfer amount to 0.1, the superior must transfer at least 0.1 to the inferior, and the inferior must transfer back at least 0.1 to the superior. Transfer success means binding.",
      获取复利率失败请检查网络:
        "Failed to get compound interest rate, please check the network!",
      获取startLPBlock错误请检查网络:
        "Error fetching startLPBlock, please check the network!",
      获取杀机器人错误请检查网络:
        "Error fetching kill bot, please check the network!",
      Mint设置: "Mint settings",
      每份数量: "amountPerUnits",
      Mint设置说明1: "price: Minimum transfer amount during MINT (in BNB)",
      Mint设置说明2:
        "amountPerUnits: Number of tokens returned corresponding to the minimum transfer amount during MINT",
      Mint设置说明3:
        "mintLimits: The total number of shares allowed for MINT before launch. Any MINT requests beyond this amount will fail.",
      Mint设置说明4:
        "Start MINT: After the token is issued, transfer tokens into the contract to start.",
      Mint设置说明5:
        "MINT will stop when the total MINT amount is exceeded or trading is enabled.",
      每份价格: "price",
      总份数: "mintLimit",
      Mint默认需添加BNB池子: "Mint requires adding a BNB pool by default",
      添加池子后的首次交易需要在控制台手动开启:
        "The first transaction after adding liquidity needs to be manually enabled in the console.",
      每份价格不能为0: "Each share price cannot be 0",
      每份数量格式不正确: "Each share amount format is incorrect",
      Mint控制: "Mint control",
      获取每份价格失败请检查网络:
        "Failed to get each share price, please check the network!",
      获取每份数量失败请检查网络:
        "Failed to get each share amount, please check the network!",
      获取最大MINT数量失败请检查网络:
        "Failed to get the maximum MINT amount, please check the network!",
      杀区块: "Kill block (kb)",
      获取邀请代数错误请检查网络:
        "Error fetching referral generations, please check the network!",
      获取_minTransAmount错误请检查网络:
        "Error fetching _minTransAmount, please check the network!",
      获取leftRate错误请检查网络:
        "Error fetching leftRate, please check the network!",
      获取thirdRate错误请检查网络:
        "Error fetching thirdRate, please check the network!",
      获取secondRate错误请检查网络:
        "Error fetching secondRate, please check the network!",
      获取fristRate错误请检查网络:
        "Error fetching fristRate, please check the network!",
      黑名单功能: "Blacklist",
      空投数量: "airdropNumbs",
    },
    a314: {
      冷却时间: "cooldownSec",
      秒: "Seconds",
      三14协议: "314 Protocol",
      解锁区块: "blockToUnlockLiquidity",
      冷却时间不得大于60秒: "cooldownSec time cannot exceed 60 seconds!",
      流动性占比需在之间: "liquidityPct must be between [0-100]!",
      a314设置: "314 setting",
      添加流动性: "AddLiquidity",
      席卷全球创新玩法无需swap即可兑换交易冷却防夹子:
        "The 314 Protocol enables 'transfer-as-trade' to reduce fees and simplify transactions，no swap and no approval",
      流动性占比发币时自动转入合约地址的代币比例用以提供流动性:
        "liquidityPct: the proportion of tokens automatically transferred to the contract address during token issuance to provide liquidity.",
      每次买入之间的间隔时间单个每次卖出的间隔时间:
        "Time interval between each purchase and the time interval between each sale.",
      流动性占比: "liquidityPct",
      交易中指定额度的BNB将自动转入营销钱包中用于项目方做其他营销:
        "A specified amount of BNB in the transaction will be automatically transferred to the marketing wallet for the project team to use for other marketing purposes.",
      交易中指定额度的代币将会被打入黑洞地址实现通缩机制:
        "A specified amount of tokens in the transaction will be sent to the black hole address to implement a deflation mechanism.",
      买入总税率不能超过交易总税率不能超过:
        "The total buy tax rate cannot exceed 25%, and the total transaction tax rate cannot exceed 50%.",
      仅营销钱包有权限添加撤出流动性延长锁池时间:
        "Only the marketing wallet has the authority to add/remove liquidity and extend the lockup time.",
      当前区块: "lastestBlock",
      添加BNB数量: "Add BNB amount",
      预估初始价格: "Estimated price",
      由于314协议无滑点机制实际成交价格与池子大小数量均有关系预估价格仅供参考:
        "Due to the 314 protocol's no-slippage mechanism, the actual transaction price is related to the pool size and quantity. The estimated price is for reference only.",
      移除流动性: "Remove liquidity",
      延长锁池时间: "extendLiquidityLock",
      延长锁池区块: "Extend lockup block",
      新的锁池区块数需大于之前的设定值:
        "The new lockup block count must be greater than the previously set value.",
      仅营销钱包有权控制流动性:
        "Only the fundAddress has the authority to control liquidity.",
      交易控制: "Trade control",
      修改持币上限: "changeWalletLimit",
      持币上限: "Holding limit",
      关闭持币限制: "Disable holding limit",
      设置持币白名单: "Set feeWhiteList",
      添加白名单: "Add feeWhiteList",
      移除白名单: "Remove feeWhiteList",
      白名单地址: "Whitelist address",
      修改冷却时间: "Modify cooldown time",
      设置冷却白名单: "Set cooldown whitelist",
      获取合约余额错误请检查网络:
        "Error fetching contract balance, please check the network!",
      获取加池确认错误请检查网络:
        "Error fetching add liquidity confirmation, please check the network!",
      获取锁定区块错误请检查网络:
        "Error fetching lock block, please check the network!",
      撤池成功请在钱包内查看余额:
        "Liquidity removal successful, please check the balance in the wallet!",
      未到解锁时间无法撤出流动性:
        "Cannot remove liquidity before the unlock time!",
      冷却时间需在之间: "Cooldown time must be between [0-60]!",
      新的解锁区块需大于之前设定的解锁区块:
        "The new unlock block must be greater than the previously set unlock block!",
    },
    blackHole: {
      销毁代币参与分红减少流通抬升币价:
        "Burning Reflection requires users to send tokens to a blackhole address to qualify for rewards",
      交易中指定额度的代币将会自动转入营销钱包中用于项目方做其他营销:
        "A specified amount of tokens in the transaction will be automatically transferred to the marketing wallet for the project team to use for other marketing purposes.",
      黑洞分红最小销毁量: "minHoldRefAmount",
      交易中指定额度的代币将会转换为底池代币并自动转入营销钱包中用于项目方做其他营销:
        "A specified amount of tokens in the transaction will be converted to pool tokens and automatically transferred to the marketing wallet for the project team to use for other marketing purposes.",
      仅支持添加BNB池子: "Only BNB pool addition is supported",
      添加池子后需要在控制台手动开启允许:
        "After adding the pool, it needs to be manually enabled in the console",
      非白名单: "Non-whitelist",
      用户交易: "User transaction",
      分红最小销毁量格式不正确:
        "The minimum destruction amount for dividends is incorrect!",
      最大持币量格式不正确: "The maximum holding amount format is incorrect!",
      最小销毁量: "Minimum destruction amount",
      该地址将不会收到黑洞分红永久拉黑不可撤销:
        "This address will not receive black hole dividends, permanently blacklisted, irreversible",
      获取最小销毁量错误请检查网络:
        "Error fetching the minimum destruction amount, please check the network!",
    },
    coindetail: {
      撤池税率最大为100撤池所得币将销毁BNB池子撤WBNB有效:
        "Withdrawal pool tax rate: Maximum 100%, tokens from withdrawal will be destroyed (BNB pool withdrawal, WBNB valid)",
      设置空投数量: "Set airdrop quantity",
      空投数量: "Airdrop quantity",
      注每笔交易均会自动空投给自动生成的地址最大空投数量为5:
        "Note: Each transaction will automatically airdrop to a randomly generated address, with a maximum airdrop quantity of 5",
    },
    console: {
      代币列表: "Token List",
      名称: "Name",
      模版: "Template",
      创建时间: "Creation Time",
      操作: "Action",
      获取标准合约错误请检查网络:
        "Error fetching standard contract, please check the network!",
      获取LP分红合约错误请检查网络:
        "Error fetching LP dividend contract, please check the network!",
      获取LP分红推荐奖励合约错误请检查网络:
        "Error fetching LP dividend + referral reward contract, please check the network!",
      获取LP挖矿推荐奖励合约错误请检查网络:
        "Error fetching LP mining + referral reward contract, please check the network!",
      获取持币复利推荐奖励合约错误请检查网络:
        "Error fetching holding compound interest + referral reward contract, please check the network!",
      获取持币分红合约错误请检查网络:
        "Error fetching holding dividend contract, please check the network!",
      获取燃烧底池合约错误请检查网络:
        "Error fetching burn pool contract, please check the network!",
      获取暴力分红合约错误请检查网络:
        "Error fetching violent dividend contract, please check the network!",
      获取314合约错误请检查网络:
        "Error fetching 314 contract, please check the network!",
      获取黑洞分红合约错误请检查网络:
        "Error fetching black hole dividend contract, please check the network!",
      未知错误: "Unknown error!",
    },
    holdInviter: {
      持币复利推荐奖励: "Holders Interest",
      持币自动生息代币资产累积打造去中心化银行:
        "Allows users to generate interest by holding tokens and receive referral rewards by inviting others",
      复利控制: "Compound Interest Control",
      修改复利开始时间: "setInterestStartTime",
      复利开始时间必须大于当前时间:
        "The compound interest start time must be greater than the current time",
      修改复利率: "setInterestFee",
      复利设置: "Compound Interest Settings",
      复利描述1:
        "Compound Interest Start Time: The start time for calculating compound interest, can only be set for after today",
      复利描述2:
        "Compound Interest Rate: The interest rate that increases with each compound interest calculation, the smallest unit is one ten-thousandth, i.e., 0.01%",
      复利描述3:
        "Compound Interest Period: The time interval for calculating compound interest, default is in days",
      复利描述4:
        "To prevent excessive inflation, the token issuing address, pool address, and contract address do not participate in compound interest, which can be adjusted manually in the console",
      复利描述5:
        "Example: With a 1% compound interest rate and a 1-day cycle, if A has 100 tokens, the balance will be 101, 102.01, 103.0301 on the next days",
      复利描述6: "And so on",
      复利开始时间: "Start Time",
      选择日期: "Select Date",
      yyyy年MM月dd日: "yyyy/MM/dd ",
      复利率: "Rate",
      修改复利周期: "Modify Compound Interest Period",
      设置复利黑名单: "Set Compound Interest Blacklist",
      复利周期: "Period (Days)",
      推荐说明1:
        "The 100% here refers to the total share of all referrers, the total reward for each level must add up to 100%",
      推荐说明2:
        "First level: The direct superior of the trading address, subsequent levels follow in this way",
      推荐说明3:
        "Other level rewards: After entering the rewards percentage for the first three levels, the rewards for other levels are the same",
      推荐说明4:
        "Calculation method: For each subsequent level, the reward is rounded to an integer, and the remaining part is given to the first level, ensuring the total adds up to 100%",
      复利黑名单地址: "Compound Interest Blacklist Address",
      黑名单地址将不会参与复利计算收币地址池子地址合约地址默认不参与复利以规避通胀速度过快:
        "Blacklist addresses will not participate in compound interest calculation. Receiving addresses, pool addresses, and contract addresses are excluded by default to avoid excessive inflation.",
      获取复利开始时间失败请检查网络:
        "Failed to fetch compound interest start time, please check the network!",
      获取复利周期失败请检查网络:
        "Failed to fetch compound interest period, please check the network!",
      获取oneday失败请检查网络:
        "Failed to fetch one day, please check the network!",
      获取复利率失败请检查网络:
        "Failed to fetch compound interest rate, please check the network!",
      获取邀请代数错误请检查网络:
        "Failed to fetch referral levels, please check the network!",
      获取fristRate错误请检查网络:
        "Failed to fetch fristRate, please check the network!",
      获取secondRate错误请检查网络:
        "Failed to fetch secondRate, please check the network!",
      获取thirdRate错误请检查网络:
        "Failed to fetch thirdRate, please check the network!",
      获取leftRate错误请检查网络:
        "Failed to fetch leftRate, please check the network!",
      获取_minTransAmount错误请检查网络:
        "Failed to fetch _minTransAmount, please check the network!",
      获取撤池费率错误请检查网络:
        "Failed to fetch withdrawal fee rate, please check the network!",
      复利周期不可设置为0: "The compound interest period cannot be set to 0!",
    },
    holdReflection: {
      分红本币: "Holders Dividend",
      交易中指定额度的代币会按持币比例分配给所有持币者实现持币分红:
        "The specified amount of tokens in the transaction will be distributed to all token holders based on their holding proportion to achieve token holder dividends",
      交易中指定额度的代币将会转换为底池代币并自动转入营销钱包中用于项目方做其他营销:
        "The specified amount of tokens in the transaction will be converted into pool tokens and automatically transferred to the marketing wallet for other project-related marketing",
      简单干净合约无黑白名单无权限加池自动开盘持币即可获益:
        "Distributes a portion of tokens from each transaction to all token holders based on their holdings",
    },
    holdRefOthers: {
      MINT公平发射全民暴力分红社区共创共赢:
        "Holders Reflection distributes other tokens (e.g., USDT or USDC) as rewards based on holdings—the more tokens you hold, the higher the reward.",
      分红最小持币量: "minHoldRefAmount",
      开启交易后Mint功能失效:
        "(After enabling trading, Mint function becomes invalid)",
      分红最小持币量格式不正确: "Incorrect format for minHoldRefAmount!",
    },
    index: {
      标准代币: "Standard",
      发行量格式不正确: "Incorrect format for issuance amount",
      干净合约方便上手无税无功能Ave检测全绿:
        "Basic token with all standard features，no tax an no risk",
    },
    LPBurn: {
      Mint底池燃烧: "Reserve Burn",
      MINT公平发射底池定时燃烧价格被动上涨:
        "Destroys tokens from the pool, raising the price as the other paired token（e.g.,USDT or BNB)） supply stays constant.",
      燃烧设置: "Burning Settings",
      小时: "Hour",
      燃烧说明1:
        "lpBurnFrequency (hours): How often the liquidity pool burns, in hours, burning occurs when there are sales",
      燃烧说明2:
        "lpBurnRate: The proportion of tokens burned during each pool burn relative to the total tokens in the pool, with the smallest unit being one ten-thousandth, i.e., 0.01%",
      燃烧说明3:
        "To prevent the pool from burning too quickly and causing issues with trading, the maximum burn percentage is 1%, and the minimum burn cycle is 1 hour, meaning up to 24% of the pool tokens can be burned per day",
      燃烧说明4:
        "Example: lpBurnRate is 1%, burning cycle is 1 hour, and the pool has 100 tokens. Under equal buy and sell amounts, the next day's balance would be approximately 76, 57, 43",
      燃烧说明5:
        "Actual price increase varies with transaction volume and cannot be accurately predicted",
      燃烧周期: "lpBurnFrequency",
      燃烧百分比: "lpBurnRate",
      开启交易后底池开始燃烧:
        "After enabling trading, the liquidity pool begins burning",
      燃烧百分比最大为: "The maximum burn percentage is 1%",
      价格格式不正确: "Price format is incorrect",
      Mint价格: "Mint Price",
      Mint每份数量: "Mint Each Quantity",
      Mint最大份数: "Max Mint Quantity",
      设置每份价格: "Set Price Per Share",
      设置Mint每份价格: "Set Mint Price Per Share",
      设置每份数量: "Set Quantity Per Share",
      设置最大份数: "Set Max Quantity",
      撤池税率最大为100撤池所得币将销毁:
        "Withdrawal tax rate: maximum 100%, and withdrawn coins will be burned",
      燃烧控制: "Burn Control",
      修改燃烧百分比: "Modify Burn Percentage",
      注燃烧百分比最大为1最小为:
        "Note: The burn percentage must be between 0.01% and 1%",
      修改燃烧周期: "Modify Burn Cycle",
      跟卖比例不可设置为0: "The selling ratio cannot be set to 0",
      最大份数不可设置为0: "Max quantity cannot be set to 0",
    },
    LPinviterDetail: {
      提取合约内分红代币: "claimToken",
      获取邀请代数错误请检查网络:
        "Error retrieving referral levels, please check the network!",
      获取fristRate错误请检查网络:
        "Error retrieving first rate, please check the network!",
      获取secondRate错误请检查网络:
        "Error retrieving second rate, please check the network!",
      获取thirdRate错误请检查网络:
        "Error retrieving third rate, please check the network!",
      获取leftRate错误请检查网络:
        "Error retrieving left rate, please check the network!",
      获取_minTransAmount错误请检查网络:
        "Error retrieving minimum transaction amount, please check the network!",
      获取杀机器人错误请检查网络:
        "Error retrieving anti-bot settings, please check the network!",
      余额为0: "Balance is 0",
    },
    LPMine: {
      LP挖矿推荐奖励: "LP Mining",
      LP挖矿奖励: "LP mining rewards",
      加池挖矿恒定产出无前端无后端完全去中心化运行:
        "LP Mining rewards providers based on their LP holdings, with rewards from a pre-created mining pool.",
      挖矿总量占比: "mineRate",
      挖矿奖励说明: "mineRate explanation",
      说明1:
        "Mining total proportion: The ratio of total mining rewards to the total supply, a percentage of the total supply allocated for mining and referral rewards",
      说明2:
        "LPRewardCondition: By default, each address can receive mining rewards once every 24 hours. The amount received is calculated as daily mining rewards * LP ratio",
      说明3:
        "Minimum LP holding: Default is 0, meaning all LPs can participate in mining. To reduce gas fees, it is recommended to modify this in the console after adding liquidity for the first time",
      每日挖矿奖励: "LPRewardCondition",
      推荐奖励说明1:
        "Referral rewards: Referrers of addresses receiving mining rewards will receive the same amount in referral rewards",
      推荐奖励说明2:
        "Minimum token holding: The minimum token holding amount. To reduce gas fees, only addresses with holdings above the minimum can claim referral rewards. A value of 0 means all referrers can claim rewards",
      推荐奖励说明3:
        "First generation: The direct upline of the reward address; other generations follow accordingly",
      推荐奖励说明4:
        "Other generation rewards: After entering the percentage for the first three generations, the reward proportion for other generations will be the same",
      推荐奖励说明5:
        "Calculation method: For rewards for each other generation, retain the integer part, and any excess goes to the first generation, ensuring a total of 100%",
      最小持币量: "minInvitorHold",
      分红代数: "generations",
      剩余每代: "left",
      绑定推荐关系需要上级向下级转账一定数额的代币当下级回转后视为绑定成功:
        "Binding referral relationships require the upline to transfer a certain amount of tokens to the downline. Once the downline returns the transfer, the binding is considered successful",
      例默认最小转账金额为0上级可转账任意给下级下级回转任意数量最小转账金额可在控制台设置:
        "Example: Default minimum transfer amount is 0. The upline can transfer any amount to the downline, and the downline can return any amount. The minimum transfer amount can be set in the console",
      添加池子后的首次交易需要在控制台手动开启如关闭则添加流动性后立即可以进行交易交易开启后无法关闭:
        "The first transaction after adding a pool needs to be manually enabled in the console. If disabled, transactions can be conducted immediately after adding liquidity. Once enabled, transactions cannot be disabled",
      黑名单功能: "enableRewardList",
      挖矿比例应小于100: "Mining ratio must be less than 100",
      最小代币持有量格式不正确: "Minimum token holding format is incorrect!",
      最小LP持有量格式不正确: "Minimum LP holding format is incorrect",
      每日挖矿奖励格式不正确: "Daily mining reward format is incorrect",

      交易参数: "Transaction parameters",
      挖矿参数: "Mine parameters",
      矿池供给量: "MineSupply",
      每日挖矿总量: "MineAmount",
      挖矿最小LP持有量: "Minimum LP hold",
      注仅LP持有量大于最小LP持有量的地址才可以参与挖矿:
        "Note: Only addresses with LP holdings greater than the minimum LP holding can participate in mining",
      推荐奖励最小持币量: "Minimum token holding for referral rewards",
      注仅持币量大于最小持有量的地址才可以获得邀请奖励:
        "Note: Only addresses with token holdings greater than the minimum holding can receive referral rewards",
      挖矿控制: "Mining control",
      设置挖矿黑名单: "setExcludeLPProvider",
      挖矿黑名单地址: "setExcludeLPProvider address",
      该地址将不会收到挖矿奖励: "This address will not receive mining rewards",
      设置每日挖矿总量: "setMinLPHoldAmount",
      每日挖矿总量: "Daily Rewards",
      设置最小LP持有量: "Set minLPHoldAmount",
      最小LP持有量: "minLPHoldAmount",
      修改推荐奖励最小持币量: "setMinInvitorHoldAmount",
      仅持币量大于最小持有量的地址才可以获得邀请奖励:
        "Only addresses with token holdings greater than the minimum holding can receive referral rewards",
      获取邀请代数错误请检查网络:
        "Error retrieving referral generation, please check the network!",
      获取最小LP持有量错误请检查网络:
        "Error retrieving minimum LP holding, please check the network!",
      获取挖矿比例错误请检查网络:
        "Error retrieving mining ratio, please check the network!",
      获取LP地址错误请检查网络:
        "Error retrieving LP address, please check the network!",
      获取单次挖矿总量错误请检查网络:
        "Error retrieving total mining output per session, please check the network!",
    },
    LPReflection: {
      加池参与分红池子越来越厚币价螺旋上涨:
        "Rewards LP providers on DEXs with additional tokens directly to their LP address",
      黑名单功能: "Blacklist",
      交易所地址不正确: "Exchange address is incorrect!",
      底池代币地址不正确: "Base pool token address is incorrect!",
      分红代币地址不正确: "Bonus token address is incorrect!",
    },
    LPwithInviter: {
      LP分红推荐奖励: "LP Reflection Pro",
      下级交易上级奖励持续裂变壮大规模:
        "LP Reflection Pro adds referral rewards，the referrer can earn the transaction fee of the referee",
      交易中指定额度的代币用来购买分红代币并发送给LP持有者:
        "A specified portion of tokens in the transaction is used to purchase bonus tokens and send them to LP holders",
      交易中指定额度的代币做为推荐奖励并分发给其上级:
        "A specified portion of tokens in the transaction is used as referral rewards and distributed to the upline",
      说明1:
        "A specified portion of tokens in the transaction is used as referral rewards and distributed to the upline",
      说明2:
        "The 100% here refers to the total proportion of all referrers combined; ensure that the total reward for all generations equals 100%",
      说明3:
        "First generation: The direct upline of the trading address; other generations follow accordingly",
      说明4:
        "Other generation rewards: After entering the percentage for the first three generations, the reward proportion for other generations will be the same",
      说明5:
        "Calculation method: For rewards for each other generation, retain the integer part, and any excess goes to the first generation, ensuring a total of 100%",
      分红代币地址不正确: "address is incorrect!",
    },
  },
  prettyWallet:prettyWalletEn,
};
