import { prettyWalletZh } from "./prettyWallet";
export default {
  webTitle: "PandaTool 全网最强的一键发币平台",
  nav: {
    连接钱包: "连接钱包",
    复制地址: "复制地址",
    断开连接: "断开连接",
    请选择钱包: "请选择钱包",
    已复制: "已复制",
  },
  router: {
    批量生成钱包EVM: "批量生成钱包EVM",
    批量生成钱包Tron: "批量生成钱包Tron",
    evm靓号生成:"靓号生成EVM",
    trx靓号生成:"靓号生成Tron",
    批量工具: "批量工具",
    帮助文档: "帮助文档",
    官网模板: "官网模板",
    流动性修复: "流动性修复",
    合约检测: "合约检测",
    工具箱: "工具箱",
    批量归集: "批量归集",
    市值管理: "市值管理",
    市值管理V2: "市值管理v2",
    市值管理1: "市值管理",
    批量转账: "批量转账",
    Solana发币: "Solana发币🔥",
    跨链闪兑: "跨链闪兑🔥",
    控制台V2: "控制台V2",
    控制台V1: "控制台V1",
    "LP挖矿+推荐奖励": "LP挖矿+推荐奖励",
    "Mint+底池燃烧": "Mint+底池燃烧",
    "Mint+暴力分红": "Mint+暴力分红",
    "持币复利+推荐奖励": "持币复利+推荐奖励",
    "314协议": "314协议",
    黑洞分红: "黑洞分红",
    "LP分红+推荐奖励": "LP分红+推荐奖励",
    LP分红: "LP分红",
    分红本币: "分红本币",
    标准代币: "标准代币",
    发行代币: "发行代币",
    预售控制台: "预售控制台",
    Mint捐赠: "Mint捐赠",
    Mint加池: "Mint加池",
    标准Mint: "标准Mint",
    发行预售: "发行预售",
    Four专区: "Four专区",
    创建Four: "创建Four",
    流动性管理: "流动性管理",
    创建流动性: "创建流动性",
    创建流动性并买入: "创建流动性并买入",
    流动性控制台: "流动性控制台",
    一键卖出: "一键卖出",
    锁池锁仓: "锁池/锁仓",
    创建锁仓: "创建锁仓",
    锁仓控制台: "锁仓控制台",
    首页: "首页",
  },
  footer: {
    有任何问题请加入: "有任何问题请加入",
    交流群进行反馈: "交流群进行反馈",
  },
  accountCreate: {
    批量生产以太坊兼容链钱包: "批量生产以太坊兼容链钱包",
    请输入生成数量: "请输入生成数量",
    数量不能为空: "数量不能为空",
    数量必须为数字值: "数量必须为数字值",
    生成中: "生成中：",
    生成: "生成：",
    导出为CSV: "导出为CSV",
    警告: "您的钱包仅在本地生成，PandaTool不会保存用户数据。请妥善备份私钥和助记词，确保存储在安全的离线环境。私钥或助记词一旦丢失或泄露，资产将无法找回，风险自担",
    以太坊地址: "以太坊地址",
    波场地址: "波场地址",
    私钥: "私钥",
    助记词: "助记词",
    请生成地址: "请生成地址!",
    批量生产波场钱包: "批量生产波场钱包",
  },
  market: {
    日志窗口: "日志窗口",
    导入刷单钱包私钥: "导入刷单钱包私钥",
    池子地址: "池子地址",
    当前价格: "当前价格",
    输入私钥: "输入私钥(每行一个)",
    取消: "取 消",
    导入: "导 入",
    错误: "错误",
    钱包格式错误: "钱包格式错误！！！",
    请切换至币安链: "请切换至币安链",
    该工具需开通会员: "该工具需开通会员",
    一天: "一天",
    一周: "一周",
    一月: "一月",
    永久: "永久",
    购买: "购买",
    花费代币授权: "花费代币授权",
    代币授权: "代币授权",
    开始: "开始",
    停止: "停止",
    清空日志: "清空日志",
    未开通VIP: "未开通VIP",
    请设置目标价格: "请设置目标价格",
    网络错误请检查网络节点: "网络错误，请检查网络节点",
    交易停止: "～～～交易停止～～～",
    开始查价格: "开始查价格",
    交易金额为跳过: "交易金额为0，跳过",
    开始执行交易: "开始执行交易",
    交易已发出等待打包确认: "交易已发出，等待打包确认",
    打包确认交易哈希: "打包确认，交易哈希",
    交易前价格: "交易前价格",
    交易后价格: "交易后价格",
    交易需要先授权如果已授权请确保代币余额充足:
      "交易需要先授权，如果已授权请确保代币余额充足",
    "交易错误，1、钱包确保有足够的代币和手续费(足够请忽略)。2、请检查您的网络":
      "交易错误，1、钱包确保有足够的代币和手续费(足够请忽略)。2、请检查您的网络。错误信息：",
    继续下一个钱包: "继续下一个钱包",
    交易已暂停: "交易已暂停",
    正在停止交易: "正在停止交易",
    该类型代币无需授权可以交易: "该类型代币无需授权，可以交易",
    请导入钱包: "请导入钱包",
    请刷新钱包: "请刷新钱包",
    授权成功: "授权成功",
    "请确认钱包手续费充足（续费充足请忽略）":
      "请确认钱包手续费充足（续费充足请忽略）",
    错误类型: "错误类型:",
    池子查询完成: "池子查询完成",
    出现错误: "出现错误",
    池子错误请确认您已经添加好了池子: "池子错误请确认您已经添加好了池子",
    正在等待矿工打包: "正在等待矿工打包",
    授权完成: "授权完成",
    达到目标价格程序即将停止: "达到目标价格。程序即将停止。",
    买入数量: "买入数量",
    卖出数量: "卖出数量",
    先查池子: "先查池子",
    按钱包持有量的: "按钱包持有量的",
    卖出: "卖出",
    卖出价值: "卖出价值",
    注意如果持有的代币价值不足兑换可能会失败:
      "注意如果持有的代币价值不足兑换可能会失败",
    请输入正确的卖出数量: "请输入正确的卖出数量",
    请输入正确的目标价格: "请输入正确的目标价格",
    拉盘的目标价格不能小于等于当前价格: "拉盘的目标价格不能小于等于当前价格",
    砸盘的目标价格不能大于当前价格: "砸盘的目标价格不能大于当前价格",
    成功: "成功",
    到期时间: "到期时间:",
    错误404: "错误404",
    买入金额: "买入金额",
    交易哈希: "交易哈希:",
    Log1: "第{count}个钱包:{address}开始交易",
    Log2: "第{count}个钱包:{address}开始完成",
    Log3: "暂停{sleepTime}秒执行下一个钱包",
    Log4: "第{round}轮结束 1秒后进行下一轮",
    Log5: "第{count}个地址授权哈希为:",
    model: {
      钱包使用方式: "钱包使用方式",
      顺序: "顺序",
      随机: "随机",
      模式: "模式",
      拉盘: "拉盘",
      砸盘: "砸盘",
      代币合约地址: "代币合约地址",
      池子类型: "池子类型",
      请选择池子类型: "请选择池子类型",
      获得代币: "获得代币",
      花费代币: "花费代币",
      请选择花费代币: "请选择花费代币",
      目标价格: "目标价格",
      卖出计算方式: "卖出计算方式",
      买入计算方式: "买入计算方式",
      数量: "数量",
      百分比: "百分比",
      时间间隔: "时间间隔",
      查池子: "查池子",
      金额范围: "金额（范围 exp:两边填1则是固定数量1）",
      数量范围: "数量（范围 exp:两边填1则是固定数量1）",
      您还不是VIP: "您还不是VIP ，请开通VIP",
    },
    点击查看手把手教程: "点击查看手把手教程",
    选择公链: "选择公链",
    选择交易所: "选择交易所",
    请选择: "请选择",
    网络节点: "网络节点",
    walletList: {
      刷新钱包: "刷新钱包",
      钱包地址: "钱包地址",
      原生代币: "原生代币",
      余额: "余额",
      代币余额: "代币余额",
      花费代币授权: "花费代币授权",
      代币授权: "代币授权",
      原生代币总余额: "原生代币总余额",
      总余额: "总余额",
      总余额: "总余额",
      提示: "提示",
      m1: "请先导入钱包，查池子，再刷新钱包",
      m2: "请先查池子，再刷新钱包",
    },
  },
  multisend: {
    暂不支持此链: "暂不支持此链",
    如有需要请联系管理员定制: "如有需要请联系管理员定制",
    批量转账: "批量转账",
    点击查看手把手教程: "点击查看手把手教程",
    手续费提示:
      "如果代币导致转账异常，请在小狐狸钱包增加gas 每次批量转账手续费",
    发送代币: "发送代币:",
    请输入代币合约地址: "请输入代币合约地址",
    地址数量提示: "收款地址和数量(最多200个地址，格式如下所示)",
    随机地址: "随机地址",
    请输入内容: "请输入内容",
    注意: "注意：一行一个，两边不要有空格，逗号是英文逗号。建议在txt文本编辑好后再复制到输入框",
    下一步: "下一步",
    接收地址: "接收地址",
    数量: "数量",
    摘要: "摘要",
    转出地址: "转出地址",
    代币余额: "代币余额",
    代币地址: "代币地址",
    授权金额: "授权金额",
    发送数量: "发送数量",
    全部授权: "全部授权",
    转账金额: "转账金额",
    授权: "授权",
    返回: "返回",
    转账: "转账",
    输入随机地址数量: "输入随机地址数量",
    警告一:
      "请输入你要随机生成的数量和金额，金额最小0.01保留2位小数， 最建议地址数最大200个，等待时间可能较长(一个地址大约1秒)",
    警告二: "注意！随机生成的地址不能找回",
    警告三: "此功能用于空投持币地址数量。不建议价值币使用此功能",
    地址数: "地址数：",
    最小金额: "最小金额",
    最大金额: "最大金额",
    取消: "取消",
    确定: "确定",
    提示: "提示",
    暂不支持此链: "暂不支持此链，请换链",
    地址错误: "地址错误",
    格式错误: "格式错误",
    转账成功: "转账成功!",
    生产地址中: "正在生成地址，请稍后...",
    转账失败余额不足: "转账失败,余额不足",
    转账失败: "转账失败",
    无需授权可以直接转账: "无需授权可以直接转账",
    授权额度充足可以直接转账: "授权额度充足可以直接转账",
    完成授权: "完成授权!交易哈希:",
    授权失败: "授权失败",
    请选择token: "请选择token",
    message1: "第{hang}行的地址不合法，请检查您的地址",
    message2: "格式错误!!一行一个，两边不要有空格，逗号是英文逗号",
    message3: "授权金额不足，请授权",
    message4: "余额不足，请检查代币余额",
  },
  contractCheck: {
    tips: "Tips：我们尽可能的识别合约信息，但无法保证数据100%正确! 仅供参考，不构成任何投资建议。",
    合约检测: "合约检测",
    请输入合约地址: "请输入合约地址",
    请选择: "请选择",
    持币信息: "持币信息:(人数:{hold} 前10占比:{p}%)",
    基本信息: "基本信息",
    已放弃所有权: "已放弃所有权",
    未放弃所有权: "未放弃所有权",
    未丢弃所有权: "未丢弃所有权",
    风险分析: "风险分析",
    错误: "错误",
    请输入正确的合约地址: "请输入正确的合约地址",
    代币名称: "代币名称",
    代币符号: "代币符号",
    发行量: "发行量",
    合约创建者: "合约创建者",
    合约所有权: "合约所有权",
    未丢弃所有权: "未丢弃所有权",
    合约是否开源: "合约是否开源",
    合约未开源: "合约未开源",
    合约已开源: "合约已开源",
    买入费率: "买入费率",
    卖出费率: "卖出费率",
    安全: "安全",
    是貔貅: "是貔貅",
    是否貔貅: "是否貔貅",
    可增发: "可增发",
    是否可增发: "是否可增发",
    是代理合约: "是代理合约",
    是否是代理合约: "是否是代理合约",
    可冷却: "可冷却",
    是否交易冷却: "是否交易冷却",
    可暂停: "可暂停",
    是否暂停交易: "是否暂停交易",
    存在白名单: "存在白名单",
    是否有交易白名单: "是否有交易白名单",
    存在黑名单: "存在黑名单",
    是否有交易黑名单: "是否有交易黑名单",
    合约可以自毁: "合约可以自毁",
    合约能否自毁: "合约能否自毁",
    能找回权限: "能找回权限",
    是否能找回权限: "是否能找回权限",
  },
  other: {
    p1: "更多官网模板,敬请期待！",
    p2: "如现有模版无法满足您的需求, 请联系我们的",
    p3: "工作人员",
    p4: "进行官网定制开发, 也可加入我们的",
    p5: "交流群组",
    p6: "进行反馈:",
  },
  website: {
    需要定制官网请联系我们商务: "需要定制官网请联系我们商务",
  },
  dashboard: {
    简介: "Panda Tool 介绍",
    描述: "PandaTool是业内领先的Web3.0多链工具平台，旗下拥有小额跨链工具、代币铸造工具、批量转账工具、批量归集工具、交易工具等产品。即便不懂编程的小白用户，也能在这里体会到区块链的魅力。",
    实用工具: "实用工具",
    跨链闪兑: "跨链闪兑",
    Solana发币: "Solana发币",
    Tron发币: "Tron发币",
    Ton发币: "Ton发币",
    Sui发币: "Sui发币",
    第三方链接: "第三方链接",
    粉红预售: "粉红预售",
    AVE数据: "AVE数据",
    币售商城: "币售商城",
    区块周刊: "区块周刊",
    加密驱动社区: "加密驱动社区",
    帮助文档: "帮助文档",
    帮助文档1: "帮助文档",
    Youtube演示视频: "Youtube演示视频",
    Dailymotion演示视频: "Dailymotion演示视频",
    Tiktok演示视频: "Tiktok演示视频",
    开放与合作: "开放与合作",
    p1: "PandaTool秉承着开放的初心,希望与业内的优秀人才、机构建立合作。",
    p2: "无论您是有项目需求、技术需求,还是Web3领域的小白,只要有投身Web3的决心,都可以联系我们。PandaTool期待与每位热爱Web3的人携手共进。",
    联系我们: "联系我们",
    商务咨询: "商务咨询",
    官方电报交流群: "官方电报交流群",
    官方推特: "官方推特",
    Github合约库: "Github合约库",
  },
  tools: {
    暂不支持此链: "暂不支持此链!",
    联系定制: "如有需要,请联系管理员定制",
    流动性修复工具: "流动性修复工具",
    说明: "针对往池子里转账少量WBNB或USDT,使池子的价格计算机制失效,导致的无法添加流动性的问题,可以使用本工具修复",
    选择交易所: "选择交易所",
    请选择: "请选择",
    V2池子地址: "V2池子地址",
    请输入池子地址: "请输入池子地址",
    查询池子: "查询池子",
    池子代币数量: "池子代币数量",
    池子代币数量说明: "池子代币数量说明",
    请先查询池子获取池子内代币信息: "请先查询池子,获取池子内代币信息",
    目标价格: "目标价格",
    目标价格说明: "目标价格说明",
    需添加超过池子内的代币数量才可自由修改价格:
      "需添加超过池子内的代币数量才可自由修改价格",
    核算加池数量: "核算加池数量",
    加池数量: "加池数量",
    加池数量说明: "加池数量说明",
    加池数量包括转入池子的数量和真正添加LP的数量:
      "加池数量包括转入池子的数量和真正添加LP的数量",
    转入池子的代币无法获得LP通证: "转入池子的代币无法获得LP通证,无法取回",
    默认的加池数量为0: "默认的加池数量为0.0000000000001",
    超过的部分将转入池子用于调整价格: "超过的部分将转入池子,用于调整价格",
    授权: "授权",
    授权说明: "授权说明",
    修复方法需使用合约完成授权选择默认值即可:
      "修复方法需使用合约完成,授权选择默认值即可",
    需先将代币打入合约再由合约在一次交易中完成转入池子和加池的所有步奏:
      "需先将代币打入合约,再由合约在一次交易中完成转入池子和加池的所有步奏",
    否则攻击者可能提取出手动打入的代币: "否则攻击者可能提取出手动打入的代币",
    造成资金损失: "造成资金损失",
    正在授权: "正在授权",
    已授权: "已授权",
    有限制交易功能的代币:
      "有限制交易功能的代币,例如手动开启交易,手动开启加池子的合约,需提前将修复合约地址添加到白名单",
    修复合约: "修复合约",
    复制: "复制",
    请稍后: "请稍后",
    确认修复: "确认修复",
    池子地址不正确: "池子地址不正确",
    获取token0简称错误: "获取token0简称错误",
    获取池子内代币数量错误: "获取池子内代币数量错误",
    请输入正确的数字格式: "请输入正确的数字格式",
    数量错误请提高第一个代币的数量: "数量错误,请提高第一个代币的数量!",
    数量错误请提高第二个代币的数量: "数量错误,请提高第二个代币的数量!",
    授权失败请重试: "授权失败,请重试",
    已提交等待区块确认: "已提交,等待区块确认",
    token0未授权: "token0未授权",
    token1未授权: "token1未授权",
    修复成功: "修复成功",
    修复失败: "修复失败,请重试!",
    已复制: "已复制",
  },
  liquidity: {
    暂不支持此链: "暂不支持此链!",
    联系定制: "如有需要,请联系管理员定制",
    创建流动池: "创建流动性池",
    创建并买入流动池: "创建流动性池 & 捆绑买入",
    创建说明: "为你的代币创建流动性池,使全部用户都可以交易你的代币",
    捆绑说明: "为你的代币创建流动性池时,捆绑买入,抢先一步获取筹码",
    管理说明: "管理流动性池,可以添加或移除流动性,查看流动性池信息",
    加池类型: "加池类型",
    PancakeV2: "PancakeV2",
    PancakeV3稳定池: "PancakeV3",
    选择底池代币: "选择底池代币",
    请选择: "请选择",
    其他代币: "其他代币",

    加池代币地址: "加池代币地址",
    请输入代币地址: "请输入代币地址",
    查询代币: "查询代币",
    加池数量: "加池数量",
    加池数量说明: "加池数量说明",
    请先查询代币获取代币信息: "请先查询代币,获取代币及池子信息",
    全部: "全部",
    余额: "余额",
    预估价格: "预估价格",
    授权: "授权",
    授权说明: "授权说明",
    修复方法需使用合约完成授权选择默认值即可:
      "加池需使用路由合约完成,授权选择默认值即可",
    正在授权: "正在授权",
    已授权: "已授权",

    有限制交易功能的代币:
      "有限制交易功能的代币,例如手动开启交易,手动开启加池子的合约,需提前将路由合约地址添加到白名单",
    加池合约: "路由合约",
    复制: "复制",
    请稍后: "请稍后",
    立即加池: "立即加池",
    代币地址地址不正确: "代币地址地址不正确",
    请选择底池代币: "请选择底池代币",
    获取代币简称错误: "获取代币简称错误",
    获取代币余额错误: "获取代币余额错误",
    获取代币精度错误: "获取代币精度错误!",
    获取底池代币余额错误: "获取底池代币余额错误!",
    获取底池代币简称错误: "获取底池代币简称错误!",
    获取底池代币精度错误: "获取底池代币精度错误!",
    加池代币数量超过余额: "加池代币数量超过余额",
    加池代币数量不能为0: "加池代币数量不能为0",
    加池底池代币数量超过余额: "加池底池代币数量超过余额",
    加池底池代币数量不能为0: "加池底池代币数量不能为0",

    请先查询代币: "请先查询代币",
    流动性池已存在: "流动性池已存在,请前往加池",
    代币正常: "代币正常,请填写加池数量",
    授权成功: "授权成功",
    授权失败请重试: "授权失败,请重试!",
    已提交等待区块确认: "交易已提交,等待区块确认",
    加池代币未授权: "加池代币未授权",
    底池代币未授权: "底池代币未授权",
    加池成功: "加池成功",
    加池失败: "加池失败",
    预估GAS失败:
      "预估GAS费失败,请检查限制交易功能的代币是否添加了路由合约地址到白名单",
    已复制: "已复制",
    预估消耗: "预估消耗",
    池子地址: "池子地址",
    未创建: "未创建",
    请确保钱包余额充足余额不足将: "请确保钱包余额充足,余额不足将",
    前往管理流动性: "前往管理流动性",
    加池完成: "加池完成",
    加池并买入成功: "加池并捆绑买入成功",
    加池并买入确认失败: "加池并买入确认失败,请自行查看余额变化确认",
    创建钱包私钥: "创建钱包私钥",
    请输入私钥: "请输入私钥",
    私钥格式错误: "第{index}行的私钥格式错误",
    创建钱包: "创建钱包",
    创建钱包说明:
      "私钥仅在本地运算,不会上传到服务器，建议使用后把资金转移，以防私钥泄露",
    钱包地址: "钱包地址",
    捆绑买入地址: "捆绑买入地址",
    批量导入私钥: "批量导入私钥",
    每行一个私钥: "每行一个私钥,请勿添加任何符号和空格",
    点击导入将自动移除错误重复私钥: "点击导入将自动移除错误、重复私钥",
    导入: "导入",
    私钥: "私钥",
    买入金额: "买入金额",
    操作: "操作",
    请输入买入数量: "请输入买入数量",
    请先刷新余额: "请先刷新余额",
    余额不足: "余额不足",
    有限制交易功能的代币捆绑:
      "有限制交易功能的代币,例如手动开启交易,手动开启加池子的合约,需提前将加池地址和捆绑买入地址添加到白名单",
    捆绑买入地址说明:
      "捆绑买入地址仅支持使用{chainSymbol}支付买入金额,若底池代币没有对应资金池,则无法买入,例如USDT为底池代币,工具将先用BNB买入USDT,再买入加池代币",
    此功能最多支持:
      "此功能最多支持同时捆绑买入 25 个地址。所有服务费将由创建流动性的钱包承担，请确保您的钱包余额充足",
    第id个地址买入数量不正确: "第{id}个地址买入数量不正确",
    购买金额不能大于钱包余额: "买入金额+预留gas费(0.005)不能大于钱包余额",
    购买最小金额: "购买最小金额为0.005",
    请移除不正确的捆绑买入地址:
      "最少1个捆绑买入地址,请移除不正确的捆绑买入地址",
    捆绑交易失败: "捆绑交易失败,请检查代币是否有限制交易功能,并重试",
    预计获取数量: "预计获取",
    管理流动性: "管理流动性",
    流动性类型: "流动性类型",
    未找到您的池子: "未找到您的池子",
    查找流动性: "查找流动性",
    查询流动性: "查询流动性",
    当前地址未发现任何流动性池:
      "当前地址未发现V2流动性,请检查连接地址,或前往创建流动性",
    代币对: "代币对",
    池子地址: "池子地址",
    LP数量: "LP数量",
    LP说明: "LP数量说明",
    LP是流动性的权益凭证:
      "LP是您向流动性池存入代币后获得的权益凭证,代表您在该池中的所有权份额,而非存入的具体代币数量。",
    锁池: "锁池",
    添加: "添加流动性",
    移除: "移除流动性",
    添加流动性: "添加流动性",
    移除流动性: "移除流动性",
    移除百分比: "移除百分比",
    移除数量: "移除LP数量",
    预计获取数量: "预计获取数量",
    接收钱包: "接收钱包",
    LP代币授权: "LP代币授权",
    税费代币的预估获取数量可能出现偏差:
      "税费代币的预估获取数量可能出现偏差，请以实际获取为准。",
    有限制加池功能的代币:
      "有限制加池功能的代币,需提前将加池地址和路由地址添加到白名单",
    有限制撤池功能的代币:
      "有限制撤池功能的代币,需提前将撤池地址和路由地址添加到白名单",
    撤池完成: "撤池完成",
    确认撤池: "确认撤池",
    当前预估价格: "当前预估价格",
    转换价格: "转换价格",
    加池代币需按照当前价格比例添加: "加池代币需按照当前价格比例添加",
    查询失败检查网络: "查询流动性失败,请检查网络",
    请先创建流动性: "请先创建流动性",
    流动性池正常: " 流动性池正常,请等待查询参数",
    获取代币资料错误: "获取代币资料错误,请检查网络后重试",
    收币地址不正确: " 收币地址不正确",
    撤池成功: "撤池成功",
    撤池失败: "撤池失败,请重试",
    正在加载: "正在加载中···",
    请先授权LP: "请先授权LP",
    添加或移除流动性时: "添加或移除流动性时,需收取{fee}的服务费",
    请确保钱包内有不少于: "在操作之前，请确保钱包内有不少于",
    前往创建流动性: "前往创建流动性",
    锁池百分比: "锁池百分比",
    锁池数量: "锁池数量",
    解锁日期: "解锁日期",
    请选择解锁日期: "请选择解锁日期",
    锁池标题: "锁池标题",
    请输入锁池标题: "选填,请输入锁池标题",
    解锁时间不正确: "请选择解锁时间",
    锁池完成: "锁池完成",
    确认锁池: "确认锁池",
    锁池成功: "锁池成功",
    锁池失败: "锁池失败,请重试",
  },
  lock: {
    暂不支持此链: "暂不支持此链",
    联系定制: "如有需要,请联系管理员定制",
    创建锁仓: "创建锁仓",
    锁仓说明: "代币一键锁仓，减少市场流通，支撑币价上行",
    锁仓代币地址: "锁仓代币地址",
    请输入代币地址: "请输入代币地址",
    请稍后: "请稍后",
    查询代币: "查询代币",
    锁仓数量: "锁仓数量",
    全部: "全部",
    余额: "余额",
    解锁日期: "解锁日期",
    请选择解锁日期: "请选择解锁日期",
    锁仓标题: "锁仓标题",
    请输入锁仓标题: "选填,请输入锁仓标题",
    授权: "授权",
    正在授权: "正在授权",
    已授权: "已授权",
    有限制功能的代币:
      "锁仓功能不支持“最大持仓限制”“修改钱包余额”等功能的代币合约",
    锁仓完成: "锁仓完成",
    立即锁仓: "立即锁仓",
    代币地址地址不正确: "代币地址地址不正确",
    获取代币简称错误: "获取代币简称错误",
    获取代币余额错误: "获取代币余额错误",
    获取代币精度错误: "获取代币精度错误",
    代币正常: "代币正常,请填写锁仓数量",
    锁仓数量超过余额: "锁仓数量超过余额",
    锁仓数量不能为0: "锁仓数量不能为0",
    请先查询代币: "请先查询代币",
    授权失败请重试: "授权失败,请重试",
    已提交等待区块确认: "交易已提交,等待区块确认",
    授权成功: "授权成功",
    锁仓代币未授权: "锁仓代币未授权",
    锁仓成功: "锁仓成功",
    锁仓失败: "锁仓失败,请重试",
    复制: "复制",
    已复制: "已复制",
    锁仓控制台: "锁仓控制台",
    锁仓控制台说明: "控制台可以查看您的锁池与锁币信息，也能进行解锁",
    锁仓类型: "锁仓类型",
    锁池: "锁池",
    锁币: "锁币",
    查找锁仓: "查找锁仓",
    代币地址: "代币地址",
    请输入代币地址: "请输入代币地址",
    前往锁仓: "前往锁仓",
    查询锁仓: "查询锁仓",
    当前地址未发现锁仓记录: "当前地址未发现锁仓记录,请前往创建锁仓",
    代币简称: "代币简称",
    解锁: "解锁",
    弃权: "弃权",
    正在加载: "正在加载中···",
    查询失败检查网络: "查询锁仓失败,请检查网络",
    获取代币资料失败: "获取代币资料失败,请检查网络后重试",
    代币地址地址不正确: "代币地址地址不正确",
    未发现锁仓记录: "未发现锁仓记录",
    解锁成功: "解锁成功,请在钱包中查看代币到账情况",
    解锁失败: "解锁失败,请重试",
    弃权成功: "弃权成功,代币已永久丢弃,无法找回",
    弃权失败: "弃权失败,请重试",
    弃权说明: "弃权说明",
    弃权将永久锁仓: "丢弃锁仓权限,将永久锁仓,无法找回",
  },
  gather: {
    请切换至币安链: "请切换至币安链",
    该工具需开通会员: "该工具需开通会员",
    一天: "一天",
    一周: "一周",
    一月: "一月",
    永久: "永久",
    购买: "购买",
    未开通VIP: "未开通VIP",
    到期时间: "到期时间",
    错误404: "错误404",
    出现错误请检查您的网络或者余额: "出现错误，请检查您的网络或者余额",
  },
  preSale: {
    暂不支持此链: "暂不支持此链",
    如有需要请联系管理员定制: "如有需要,请联系管理员定制",
    console: {
      预售列表: "预售列表",
      合约地址: "合约地址",
      预售名称: "预售名称",
      名称: "名称",
      所有者: "所有者",
      创建时间: "创建时间",
      操作: "操作",
      进入控制台: "进入控制台",
      获取mint预售错误请检查网络: "获取mint预售错误,请检查网络!",
      已复制: "已复制",
    },
    mintAddSale: {
      Mint加池: "Mint加池",
      教程: "教程",
      预售即加池: "预售即加池、自动分发LP、人人都是做市商",
      预售名称: "预售名称",
      预售代币地址: "预售代币地址",
      每份价格: "每份价格",
      每份数量: "每份数量",
      总份数: "总份数",
      单次预售最大份数: "单次预售最大份数",
      单钱包预售最大份数: "单钱包预售最大份数",
      选择交易所: "选择交易所",
      请选择: "请选择",
      添加流动性时需要添加代币与底池代币之间的交易对:
        "添加流动性时需要添加代币与底池代币之间的交易对,否则无法进行正常分红与回流",
      加池比例: "加池比例",
      加池预售用户参与预售的同时会按照该比例自动添加流动性:
        "加池预售,用户参与预售的同时会按照该比例自动添加流动性",
      创建合约: "创建合约",
      费用: "费用",
      合约地址: "合约地址",
      预计生成地址: "预计生成地址",
      预估手续费: "预估手续费",
      请确保钱包余额充足余额不足将: "请确保钱包余额充足,余额不足将",
      创建失败: "创建失败",
      开源参数: "开源参数",
      浏览器查看: "浏览器查看",
      开源教程: "开源教程",
      复制源代码: "复制源代码",
      复制构造参数: "复制构造参数",
      构造参数无法找回若不立即开源请复制后保存到本地文档:
        "构造参数无法找回,若不立即开源,请复制后保存到本地文档",
      进入控制台: "进入控制台",
      预售代币地址不正确: "预售代币地址不正确",
      价格格式不正确: "价格格式不正确",
      每份数量格式不正确: "每份数量格式不正确",
      交易所路由地址不正确: "交易所路由地址不正确",
      公链错误请确保钱包与选择的公链一致: "公链错误:请确保钱包与选择的公链一致",
      创建失败: "创建失败,请重试",
      创建成功: "创建成功",
      已复制: "已复制",
    },
    mintAddSaleDetail: {
      预售控制台: "预售控制台",
      复制链接: "复制链接",
      基本信息: "基本信息",
      预售名称: "预售名称",
      预售地址: "预售地址",
      预售代币简称: "预售代币简称",
      预售代币地址: "预售代币地址",
      预售模版: "预售模版",
      所有者: "所有者",
      预售情况: "预售情况",
      已售份数: "已售份数",
      合约内代币数量: "合约内代币数量",
      预售参数: "预售参数",
      价格: "价格",
      每份数量: "每份数量",
      总份数: "总份数",
      单次Mint最大份数: "单次Mint最大份数",
      单钱包最大份数: "单钱包最大份数",
      加池比例: "加池比例",
      交易所: "交易所",
      池子地址: "池子地址",
      营销钱包: "营销钱包",
      捐赠代币比例: "捐赠代币比例",
      接收代币地址: "接收代币地址",
      预售控制: "预售控制",
      转让所有权: "转让所有权",
      转让地址: "转让地址",
      取消: "取消",
      确定: "确定",
      开启预售: "开启预售",
      提取合约内代币: "提取合约内代币",
      预售代币: "预售代币",
      数量: "数量",
      参数控制: "参数控制",
      修改每份价格: "修改每份价格",
      修改Mint每份价格: "修改Mint每份价格",
      每份价格: "每份价格",
      修改每份数量: "修改每份数量",
      修改总份数: "修改总份数",
      修改单次Mint最大份数: "修改单次Mint最大份数",
      修改单钱包最大份数: "修改单钱包最大份数",
      加池控制: "加池控制",
      修改营销钱包: "修改营销钱包",
      营销钱包地址: "营销钱包地址",
      修改加池比例: "修改加池比例",
      捐赠控制: "捐赠控制",
      修改接收钱包: "修改接收{chainSymbol}钱包",
      修改接收钱包1: "修改接收钱包",
      修改捐赠比例: "修改捐赠{chainSymbol}比例",
      修改捐赠比例1: "修改捐赠比例",
      修改接收代币钱包: "修改接收{symbol}钱包",
      修改接收代币钱包1: "修改接收代币钱包",
      修改捐赠代币比例: "修改捐赠{symbol}代币比例",
      修改捐赠代币比例1: "修改捐赠代币比例",
      捐赠比例: "捐赠比例",
      预售官网示例: "预售官网示例",
      需要定制官网请联系商务: "需要定制官网?请联系商务",
      捐赠: "捐赠",
      比例: "比例",
      接收: "接收",
      地址: "地址",
      合约内: "合约内",
      ETHBalanceLabel: "合约内{eth}数量",
      donatePartLabel: "捐赠{eth}比例",
      donateAddrLabel: "接收{eth}地址",
      mint加池: "mint加池",
      mint捐赠: "mint捐赠",
      标准mint: "标准mint",
      关闭: "关闭",
      开启: "开启",
      获取合约拥有者错误请检查网络: "获取合约拥有者错误,请检查网络!",
      获取名称错误请检查网络: "获取名称错误,请检查网络!",
      获取代币地址错误请检查网络: "获取代币地址错误,请检查网络!",
      获取代币精度错误请检查网络: "获取代币精度错误,请检查网络!",
      获取代币简称错误请检查网络: "获取代币简称错误,请检查网络!",
      获取代币数量错误请检查网络: "获取代币数量错误,请检查网络!",
      获取开启预售失败请检查网络: "获取开启预售失败,请检查网络!",
      获取捐赠模式失败请检查网络: "获取捐赠模式失败,请检查网络!",
      获取捐赠ETH比例失败请检查网络: "获取捐赠ETH比例失败,请检查网络!",
      获取捐赠代币比例失败请检查网络: "获取捐赠代币比例失败,请检查网络!",
      获取接收ETH地址失败请检查网络: "获取接收ETH地址失败,请检查网络!",
      获取接收代币地址失败请检查网络: "获取接收代币地址失败,请检查网络!",
      获取加池模式失败请检查网络: "获取加池模式失败,请检查网络!",
      获取交易所错误请检查网络: "获取交易所错误,请检查网络!",
      获取池子地址错误请检查网络: "获取池子地址错误,请检查网络!",
      获取每份价格失败请检查网络: "获取每份价格失败,请检查网络!",
      获取每份数量失败请检查网络: "获取每份数量失败,请检查网络!",
      获取最大MINT数量失败请检查网络: "获取最大MINT数量失败,请检查网络!",
      获取单次最大MINT数量失败请检查网络:
        "获取单次最大MINT数量失败,请检查网络!",
      获取单钱包最大MINT数量失败检查网络:
        "获取单钱包最大MINT数量失败,请检查网络",
      获取营销钱包错误请检查网络: "获取营销钱包错误,请检查网络!",
      已提交等待区块确认: "已提交,等待区块确认!",
      修改成功: "修改成功!",
      确认失败请前往浏览器查看: "确认失败!请前往浏览器查看!",
      出错了: "出错了!",
      地址格式不正确: "地址格式不正确",
      修改失败: "修改失败",
      合约内余额不足: "合约内余额不足",
      请输入正确的数字格式: "请输入正确的数字格式",
      单钱包Mint最大份数不可设置为0: "单钱包Mint最大份数不可设置为0",
      单次Mint最大份数不可设置为0: "单次Mint最大份数不可设置为0",
      添加比例需在之间: "添加比例需在1~100之间",
      捐赠比例需在之间: "捐赠比例需在0~100之间",
      已复制: "已复制",
      总份数不可设置为0: "总份数不可设置为0",
      获取合约ETH数量错误请检查网络: "获取合约ETH数量错误,请检查网络!",
      正在授权请稍等: "正在授权,请稍等!",
      授权完成请确认开启预售: "授权完成,请确认开启预售!",
      开启预售成功: "开启预售成功",
      开启预售失败: "开启预售失败",
      授权失败: "授权失败",
    },
    mintDonate: {
      Mint捐赠: "Mint捐赠",
      教程: "教程",
      公平做慈善捐赠V神来炒作打造新叙事: "公平做慈善、捐赠V神来炒作,打造新叙事",
      预售名称: "预售名称",
      预售代币地址: "预售代币地址",
      每份价格: "每份价格",
      每份数量: "每份数量",
      总份数: "总份数",
      单次预售最大份数: "单次预售最大份数",
      单钱包预售最大份数: "单钱包预售最大份数",
      捐赠代币比例: "捐赠代币比例",
      接收代币地址: "接收代币地址",
      创建合约: "创建合约",
      费用: "费用",
      合约地址: "合约地址",
      预计生成地址: "预计生成地址",
      预估手续费: "预估手续费",
      请确保钱包余额充足余额不足将: "请确保钱包余额充足,余额不足将",
      创建失败: "创建失败",
      开源参数: "开源参数",
      浏览器查看: "浏览器查看",
      开源教程: "开源教程",
      复制源代码: "复制源代码",
      复制构造参数: "复制构造参数",
      构造参数无法找回若不立即开源请复制后保存到本地文档:
        "构造参数无法找回,若不立即开源,请复制后保存到本地文档",
      进入控制台: "进入控制台",
      捐赠: "捐赠",
      donatePartLabel: "捐赠{eth}比例",
      donateAddrLabel: "接受{eth}地址",
      比例: "比例",
      接收: "接收",
      地址: "地址",
      预售代币地址不正确: "预售代币地址不正确",
      价格格式不正确: "价格格式不正确",
      每份数量格式不正确: "每份数量格式不正确",
      交易所路由地址不正确: "交易所路由地址不正确",
      创建失败请重试: "创建失败,请重试!",
      公链错误请确保钱包与选择的公链一致:
        "公链错误:请确保钱包与选择的公链一致!",
      创建成功: "创建成功",
      已复制: "已复制",
    },
    simpleMint: {
      教程: "教程",
      标准Mint: "标准Mint",
      转账即预售链上可查去中心化: "转账即预售、链上可查、100%去中心化",
      预售名称: "预售名称",
      预售代币地址: "预售代币地址",
      每份价格: "每份价格",
      每份数量: "每份数量",
      总份数: "总份数",
      单次预售最大份数: "单次预售最大份数",
      单钱包预售最大份数: "单钱包预售最大份数",
      创建合约: "创建合约",
      费用: "费用",
      合约地址: "合约地址",
      预计生成地址: "预计生成地址",

      预估手续费: "预估手续费",
      请确保钱包余额充足余额不足将: "请确保钱包余额充足,余额不足将",
      创建失败: "创建失败",
      开源参数: "开源参数",
      浏览器查看: "浏览器查看",
      开源教程: "开源教程",
      复制源代码: "复制源代码",
      复制构造参数: "复制构造参数",
      构造参数无法找回若不立即开源请复制后保存到本地文档:
        "构造参数无法找回,若不立即开源,请复制后保存到本地文档",
      进入控制台: "进入控制台",
      预售代币地址不正确: "预售代币地址不正确",
      价格格式不正确: "价格格式不正确",
      每份数量格式不正确: "每份数量格式不正确",
      交易所路由地址不正确: "交易所路由地址不正确",
      创建失败请重试: "创建失败,请重试!",
      公链错误请确保钱包与选择的公链一致:
        "公链错误:请确保钱包与选择的公链一致!",

      创建成功: "创建成功",
      已复制: "已复制",
    },
  },
  coinRelease: {
    modeType: {
      黑洞分红: "黑洞分红",
      标准模版: "标准模版",
      三14协议: "314协议",
      LP分红: "LP分红",
      LP分红推荐奖励: "LP分红+推荐奖励",
      LP挖矿推荐奖励: "LP挖矿+推荐奖励",
      LP挖矿推荐奖励: "LP挖矿+推荐奖励",
      Mint暴力分红: "Mint+暴力分红",
      Mint燃烧底池: "Mint+燃烧底池",
      分红本币: "分红本币",
      持币复利推荐奖励: "持币复利+推荐奖励",
      LP挖矿推荐奖励: "LP挖矿+推荐奖励",
    },
    common: {
      暂不支持此链: "暂不支持此链!",
      如有需要请联系管理员定制: "如有需要,请联系管理员定制",
      代币全称: "代币全称",
      全称: "全称",
      代币简称: "代币简称",
      代币模版: "代币模版",
      经济模型: "经济模型",
      简称: "简称",
      底池代币: "底池代币",
      单钱包持币上限: "单钱包持币上限",
      权限控制: "权限控制",
      丢弃权限: "丢弃权限",
      所有者: "所有者",
      发行量: "发行量",
      总量: "总量",
      精度: "精度",
      买入营销税率: "营销税率",
      买入分红税率: "分红税率",
      买入回流税率: "回流税率",
      买入销毁税率: "销毁税率",
      买入税率: "买入税率",
      营销税率: "营销税率",
      销毁税率: "销毁税率",
      营销钱包: "营销钱包",
      分红税率: "分红税率",
      回流税率: "回流税率",
      推荐税率: "推荐税率",
      营销: "营销",
      销毁: "销毁",
      回流: "回流",
      使用当前钱包: "使用当前钱包",
      请选择: "请选择",
      创建合约: "创建合约",
      合约地址: "合约地址",
      转让地址: "转让地址",
      费用: "费用",
      确定: "确定",
      取消: "取消",
      预计生成地址: "预计生成地址",
      预估手续费: "预估手续费",
      创建失败: "创建失败",
      请确保钱包余额充足余额不足将: "请确保钱包余额充足,余额不足将",
      浏览器查看: "浏览器查看",
      开源教程: "开源教程",
      开源参数: "开源参数",
      复制源代码: "复制源代码",
      复制构造参数: "复制构造参数",
      构造参数无法找回若不立即开源请复制后保存到本地文档:
        "构造参数无法找回,若不立即开源,请复制后保存到本地文档",
      进入控制台: "进入控制台",
      已复制: "已复制!",
      创建成功: "创建成功!",
      修改成功: "修改成功!",
      已提交等待区块确认: "已提交,等待区块确认!",
      创建失败请重试: "创建失败,请重试!",
      确认失败请前往浏览器查看: "确认失败!请前往浏览器查看!",
      出错了: "出错了!",
      地址格式不正确: "地址格式不正确!",
      公链错误请确保钱包与选择的公链一致:
        "公链错误:请确保钱包与选择的公链一致!",
      最大持币金额格式不正确: "最大持币金额格式不正确!",
      初始供应量格式不正确: "初始供应量格式不正确!",
      营销钱包地址不正确: "营销钱包地址不正确!",
      营销钱包地址: "营销钱包地址",
      代币详情: "代币详情",
      复制链接: "复制链接",
      教程: "教程",
      税率说明: "税率说明",
      修改税率: "修改税率",
      选择交易所: "选择交易所",
      卖出税率: "卖出税率",
      转让所有权: "转让所有权",
      请输入正确的数字格式: "请输入正确的数字格式",
      可设置单个钱包持有的最大代币数量此开关关闭后无法再次开启:
        "可设置单个钱包持有的最大代币数量, 此开关关闭后无法再次开启",
      最大持币量: "最大持币量",
      基本信息: "基本信息",
      合约地址: "合约地址",
      流动性控制: "流动性控制",
      修改营销钱包: "修改营销钱包",
      获取中: "获取中...",
      无上限: "无上限",
      交易所: "交易所",
      获取合约拥有者错误请检查网络: "获取合约拥有者错误,请检查网络!",
      获取名称错误请检查网络: "获取名称错误,请检查网络!",
      获取简称错误请检查网络: "获取简称错误,请检查网络!",
      获取精度错误请检查网络: "获取精度错误,请检查网络!",
      获取供应量错误请检查网络: "获取供应量错误,请检查网络!",
      获取买入营销费率错误请检查网络: "获取买入营销费率错误,请检查网络!",
      获取买入销毁费率错误请检查网络: "获取买入销毁费率错误,请检查网络!",
      获取卖出营销费率错误请检查网络: "获取卖出营销费率错误,请检查网络!",
      获取卖出销毁费率错误请检查网络: "获取卖出销毁费率错误,请检查网络!",
      获取营销钱包错误请检查网络: "获取营销钱包错误,请检查网络!",
      获取冷却时间错误请检查网络: "获取冷却时间错误,请检查网络!",
      获取持币限制开关错误请检查网络: "获取持币限制开关错误,请检查网络!",
      交易中指定额度的代币将会自动添加到流动池内保证交易始终存在流动性:
        "交易中指定额度的代币将会自动添加到流动池内,保证交易始终存在流动性",
      交易中指定额度的代币将会自动转入营销钱包中用于项目方做其他营销:
        "交易中指定额度的代币将会自动转入营销钱包中,用于项目方做其他营销",
      交易中指定额度的代币将会被打入黑洞地址变相实现通缩机制:
        "交易中指定额度的代币将会被打入黑洞地址,变相实现通缩机制",
      交易中指定额度的代币用来购买分红代币并发送给LP持有者:
        "交易中指定额度的代币,用来购买分红代币,并发送给LP持有者",
      买入总税率不能超过25交易总税率不能超过50:
        "买入总税率不能超过25%, 交易总税率不能超过50%",
      选择底池代币: "选择底池代币",
      添加流动性时需要添加代币与底池代币之间的交易对否则无法进行正常分红与回流:
        "添加流动性时需要添加代币与底池代币之间的交易对,否则无法进行正常分红与回流",
      分红代币: "分红代币",
      其他代币: "其他代币",
      要分红的代币此代币必须存在公链原生币池子如BNBETH等:
        "要分红的代币 (此代币必须存在公链原生币池子,如BNB,ETH等)",
      手动开启交易: "手动开启交易",
      税率开关: "税率开关",
      可在创建代币后手动调整税率买卖税率各不能超过25此开关关闭后无法再次开启:
        "可在创建代币后手动调整税率, 买卖税率各不能超过25%,此开关关闭后无法再次开启",
      可拉黑部分钱包地址令其无法交易此开关关闭后无法再次开启:
        "可拉黑部分钱包地址令其无法交易, 此开关关闭后无法再次开启",
      交易中指定额度的代币将会转换为底池代币并自动转入营销钱包中用于项目方做其他营销:
        "交易中指定额度的代币将会转换为底池代币并自动转入营销钱包中,用于项目方做其他营销",
      交易中指定额度的代币做为推荐奖励并分发给其上级:
        "交易中指定额度的代币做为推荐奖励, 并分发给其上级",
      分红最小持币量不能为0: "分红最小持币量不能为0!",
      买卖营销税之和必须大于0: "买卖营销税之和必须大于0!",
      交易控制: "交易控制",
      开启交易: "开启交易",
      修改持币上限: "修改持币上限",
      持币上限: "持币上限",
      关闭持币限制: "关闭持币限制",
      设置持币白名单: "设置持币白名单",
      添加白名单: "添加白名单",
      移除白名单: "移除白名单",
      白名单地址: "白名单地址",
      请输入地址每行一个地址不要添加任何符号:
        "请输入地址,每行一个地址,不要添加任何符号",
      设置税率白名单: "设置税率白名单",
      添加税率白名单: "添加税率白名单",
      移除税率白名单: "移除税率白名单",
      税率白名单地址: "税率白名单地址",
      分红控制: "分红控制",
      提取合约内代币: "提取合约内代币",
      本币: "本币",
      数量: "数量",
      设置分红黑名单: "设置分红黑名单",
      分红黑名单地址: "分红黑名单地址",
      获取交易所错误请检查网络: "获取交易所错误,请检查网络!",
      获取池子地址错误请检查网络: "获取池子地址错误,请检查网络!",
      获取底池代币错误请检查网络: "获取底池代币错误,请检查网络!",
      获取分红代币错误请检查网络: "获取分红代币错误,请检查网络!",
      获取买入回流费率错误请检查网络: "获取买入回流费率错误,请检查网络!",
      获取买入分红费率错误请检查网络: "获取买入分红费率错误,请检查网络!",
      获取卖出回流费率错误请检查网络: "获取卖出回流费率错误,请检查网络!",
      获取卖出分红费率错误请检查网络: "获取卖出分红费率错误,请检查网络!",
      获取跟卖比例错误请检查网络: "获取跟卖比例错误,请检查网络!",
      获取最大持币量错误请检查网络: "获取最大持币量错误,请检查网络!",
      获取手动交易开关错误请检查网络: "获取手动交易开关错误,请检查网络!",
      获取startTradeBlock错误请检查网络: "获取startTradeBlock错误,请检查网络!",
      获取黑名单开关错误请检查网络: "获取黑名单开关错误,请检查网络!",
      获取最大持币开关错误请检查网络: "获取最大持币开关错误,请检查网络!",
      获取税率开关错误请检查网络: "获取税率开关错误,请检查网络!",
      修改失败: "修改失败!",
      合约内余额不足: "合约内余额不足!",
      池子地址: "池子地址",
      设置黑名单: "设置黑名单",
      添加黑名单: "添加黑名单",
      移除黑名单: "移除黑名单",
      黑名单地址: "黑名单地址",
      税率控制: "税率控制",
      买入税率最大25: "买入税率:最大25%",
      卖出税率最大25: "卖出税率:最大25%",
      加池税率最大为25: "加池税率:最大为25%",
      天: "天",
      分红: "分红",
      买入分红: "分红",
      加池税率: "加池税率",
      撤池税率: "撤池税率",
      跟卖比例: "跟卖比例",
      杀抢跑机器人区块: "杀抢跑机器人区块",
      交易空投地址数: "交易空投地址数",
      设置加池税率: "设置加池税率",
      设置撤池税率: "设置撤池税率",
      杀开盘抢跑机器人: "杀开盘抢跑机器人",
      抢跑区块: "抢跑区块",
      注需手动开启交易开启交易的前X个区块内交易的地址被视为抢跑机器人自动加入黑名单:
        "注:需手动开启交易,开启交易的前X个区块内交易的地址被视为抢跑机器人,自动加入黑名单",
      该地址将不会收到LP分红: "该地址将不会收到LP分红",
      设置分红阈值: "设置分红阈值",
      分红阈值说明:
        " 当分红钱包内的分红代币数量超过该值时, 开始分红,默认为0.1个分红代币, 即分红税的价值达到该值是才会开始分红,设置的过高可能会导致长时间无分红,强烈建议新用户不要修改此选项 (总结: 分红USDT默认就好,分红WBNB可适当调低, 例如0.0001, 不要随意调高该值,引发的任何问题与平台无关)",
      获取加池费率错误请检查网络: "获取加池费率错误,请检查网络!",
      获取撤池费率错误请检查网络: "获取撤池费率错误,请检查网络!",
      获取空投数量错误请检查网络: "获取空投数量错误,请检查网络!",
      获取杀机器人区块错误请检查网络: "获取杀机器人区块错误,请检查网络!",
      获取空投开关错误请检查网络: "获取空投开关错误,请检查网络!",
      推荐奖励: "推荐奖励",
      推荐奖励说明: "推荐奖励说明",
      分红代数: "分红代数",
      一代比例: "一代比例",
      二代比例: "二代比例",
      三代比例: "三代比例",
      剩余每代: "剩余每代",
      核算比例: "核算比例",
      绑定推荐关系需要上级向下级转账一定数额的代币当下级回转后视为绑定成功:
        "绑定推荐关系需要上级向下级转账一定数额的代币,当下级回转后,视为绑定成功",
      例默认最小转账金额为0上级可转账任意给下级下级回转任意数量最小转账金额可在控制台设置:
        "例:默认最小转账金额为0,上级可转账任意给下级,下级回转任意数量,最小转账金额可在控制台设置",
      添加池子后的首次交易需要在控制台手动开启如关闭则添加流动性后立即可以进行交易交易开启后无法关闭:
        "添加池子后的首次交易需要在控制台手动开启(如关闭,则添加流动性后立即可以进行交易), 交易开启后无法关闭",
      区块数量: "区块数量",
      将对开启交易后在n个区块内交易的地址全部拉入黑名单用于防止机器人抢跑买入必须手动开启交易:
        "将对开启交易后在n个区块内交易的地址全部拉入黑名单,用于防止机器人抢跑买入,必须手动开启交易",
      自动空投: "自动空投",
      开启开关后用户交易时将会自动向随机地址空投小额代币以增加持币地址单次最多空投5个:
        "开启开关后, 用户交易时,将会自动向随机地址空投小额代币,以增加持币地址,单次最多空投5个",
      邀请奖励比例错误: "邀请奖励比例错误!",
      推荐: "推荐",
      代奖励: "代奖励",
      其他各代比例: "其他各代比例",
      最小转账数量: "最小转账数量",
      注绑定推荐关系的最小数量回转确认后绑定成功默认为0:
        "注:绑定推荐关系的最小数量,回转确认后绑定成功;默认为0",
      撤池税率最大为25: "撤池税率:最大为25%",
      推荐奖励控制: "推荐奖励控制",
      修改推荐税率: "修改推荐税率",
      推荐税率买卖相同需保证买入总税率不能超过25卖出总税率不能超过25:
        "推荐税率买卖相同,需保证买入总税率不能超过25%,卖出总税率不能超过25%",
      设置推荐奖励比例: "设置推荐奖励比例",
      奖励代数: "奖励代数",
      总比例: "总比例",
      确认前请先核算确认总比例为100: "确认前请先核算,确认总比例为100%",
      确定修改: "确定修改",
      设置最小转账金额: "设置最小转账金额",
      最小转账金额: "最小转账金额",
      用于设定绑定推荐关系的最小转账金额如希望任意转账金额均可绑定推荐关系设置0即可:
        "用于设定绑定推荐关系的最小转账金额,如希望任意转账金额均可绑定推荐关系,设置0即可",
      例设定最小转账金额为上级需转账至少给下级下级至少回转给上级转账成功即绑定:
        "例:设定最小转账金额为0.1,上级需转账至少0.1给下级,下级至少回转0.1给上级,转账成功即绑定",
      获取复利率失败请检查网络: "获取复利率失败,请检查网络!",
      获取startLPBlock错误请检查网络: "获取startLPBlock错误,请检查网络!",
      获取杀机器人错误请检查网络: "获取杀机器人错误,请检查网络!",
      Mint设置: "Mint设置",
      每份数量: "每份数量",
      Mint设置说明1: "每份价格:MINT时,最小的转账金额(BNB)",
      Mint设置说明2: "每份数量:MINT时最小转账金额对应的返回的代币数量",
      Mint设置说明3:
        "总份数:在开盘前,允许MINT的全部份数,超过此数量的MINT将会失败",
      Mint设置说明4: "开始MINT:代币发行后,往合约里转入代币即可",
      Mint设置说明5: "当超过MINT总份数,或者开启交易,停止MINT",
      每份价格: "每份价格",
      总份数: "总份数",
      Mint默认需添加BNB池子: "Mint默认需添加BNB池子",
      添加池子后的首次交易需要在控制台手动开启:
        "添加池子后的首次交易需要在控制台手动开启",
      每份价格不能为0: "每份价格不能为0",
      每份数量格式不正确: "每份数量格式不正确",
      Mint控制: "Mint控制",
      获取每份价格失败请检查网络: "获取每份价格失败,请检查网络!",
      获取每份数量失败请检查网络: "获取每份数量失败,请检查网络!",
      获取最大MINT数量失败请检查网络: "获取最大MINT数量失败,请检查网络!",
      杀区块: "杀区块",
      获取邀请代数错误请检查网络: "获取邀请代数错误,请检查网络!",
      获取_minTransAmount错误请检查网络: "获取_minTransAmount错误,请检查网络!",
      获取leftRate错误请检查网络: "获取leftRate错误,请检查网络!",
      获取thirdRate错误请检查网络: "获取thirdRate错误,请检查网络!",
      获取secondRate错误请检查网络: "获取secondRate错误,请检查网络!",
      获取fristRate错误请检查网络: "获取fristRate错误,请检查网络!",
      黑名单功能: "黑名单功能",
      空投数量: "空投数量",
    },
    a314: {
      冷却时间: "冷却时间(秒)",
      秒: "秒",
      三14协议: "314协议",
      a314设置: "314设置",
      解锁区块: "解锁区块",
      冷却时间不得大于60秒: "冷却时间不得大于60秒!",
      流动性占比需在之间: "流动性占比需在[0-100]之间!",
      添加流动性: "添加流动性",
      席卷全球创新玩法无需swap即可兑换交易冷却防夹子:
        "席卷全球创新玩法、无需swap即可兑换、交易冷却防夹子!",
      流动性占比发币时自动转入合约地址的代币比例用以提供流动性:
        "流动性占比:发币时,自动转入合约地址的代币比例,用以提供流动性",
      每次买入之间的间隔时间单个每次卖出的间隔时间:
        "每次买入之间的间隔时间,单个每次卖出的间隔时间",
      流动性占比: "流动性占比",
      交易中指定额度的BNB将自动转入营销钱包中用于项目方做其他营销:
        "交易中指定额度的BNB将自动转入营销钱包中,用于项目方做其他营销",
      交易中指定额度的代币将会被打入黑洞地址实现通缩机制:
        "交易中指定额度的代币将会被打入黑洞地址, 实现通缩机制",
      买入总税率不能超过交易总税率不能超过:
        "买入总税率不能超过25%, 交易总税率不能超过50%",
      仅营销钱包有权限添加撤出流动性延长锁池时间:
        "仅营销钱包有权限添加/撤出流动性,延长锁池时间",
      当前区块: "当前区块",
      添加BNB数量: "添加BNB数量",
      预估初始价格: "预估初始价格",
      由于314协议无滑点机制实际成交价格与池子大小数量均有关系预估价格仅供参考:
        "由于314协议无滑点机制,实际成交价格与池子大小,数量均有关系,预估价格仅供参考",
      移除流动性: "移除流动性",
      延长锁池时间: "延长锁池时间",
      延长锁池区块: "延长锁池区块",
      新的锁池区块数需大于之前的设定值: "新的锁池区块数需大于之前的设定值",
      仅营销钱包有权控制流动性: "仅营销钱包有权控制流动性",
      交易控制: "交易控制",
      修改持币上限: "修改持币上限",
      持币上限: "持币上限",
      关闭持币限制: "关闭持币限制",
      设置持币白名单: "设置持币白名单",
      添加白名单: "添加白名单",
      移除白名单: "移除白名单",
      白名单地址: "白名单地址",
      修改冷却时间: "修改冷却时间",
      设置冷却白名单: "设置冷却白名单",
      获取合约余额错误请检查网络: "获取合约余额错误,请检查网络!",
      获取加池确认错误请检查网络: "获取加池确认错误,请检查网络!",
      获取锁定区块错误请检查网络: "获取锁定区块错误,请检查网络!",
      撤池成功请在钱包内查看余额: "撤池成功,请在钱包内查看余额!",
      未到解锁时间无法撤出流动性: "未到解锁时间,无法撤出流动性!",
      冷却时间需在之间: "冷却时间需在[0-60]之间!",
      新的解锁区块需大于之前设定的解锁区块:
        "新的解锁区块需大于之前设定的解锁区块!",
    },
    blackHole: {
      销毁代币参与分红减少流通抬升币价: "销毁代币参与分红、减少流通抬升币价",
      交易中指定额度的代币将会自动转入营销钱包中用于项目方做其他营销:
        "交易中指定额度的代币将会自动转入营销钱包中,用于项目方做其他营销",
      交易中指定额度的代币将会转换为底池代币并自动转入营销钱包中用于项目方做其他营销:
        "交易中指定额度的代币将会转换为底池代币并自动转入营销钱包中,用于项目方做其他营销",
      仅支持添加BNB池子: "仅支持添加BNB池子",
      添加池子后需要在控制台手动开启允许: "添加池子后,需要在控制台手动开启允许",
      非白名单: "非白名单",
      用户交易: "用户交易",
      黑洞分红最小销毁量: "黑洞分红最小销毁量",
      分红最小销毁量格式不正确: "分红最小销毁量格式不正确!",
      最大持币量格式不正确: "最大持币量格式不正确!",
      最小销毁量: "最小销毁量",
      该地址将不会收到黑洞分红永久拉黑不可撤销:
        "该地址将不会收到黑洞分红,永久拉黑,不可撤销",
      获取最小销毁量错误请检查网络: "获取最小销毁量错误,请检查网络!",
    },
    coindetail: {
      撤池税率最大为100撤池所得币将销毁BNB池子撤WBNB有效:
        "撤池税率:最大为100%,撤池所得币将销毁(BNB池子撤WBNB有效)",
      设置空投数量: "设置空投数量",
      空投数量: "空投数量",
      注每笔交易均会自动空投给自动生成的地址最大空投数量为5:
        "注:每笔交易均会自动空投给自动生成的地址,最大空投数量为5",
      黑名单功能: "黑名单功能",
    },
    console: {
      代币列表: "代币列表",
      名称: "名称",
      模版: "模版",
      创建时间: "创建时间",
      操作: "操作",
      获取标准合约错误请检查网络: "获取标准合约错误,请检查网络!",
      获取LP分红合约错误请检查网络: "获取LP分红合约错误,请检查网络!",
      获取LP分红推荐奖励合约错误请检查网络:
        "获取LP分红+推荐奖励合约错误,请检查网络!",
      获取LP挖矿推荐奖励合约错误请检查网络:
        "获取LP挖矿+推荐奖励合约错误,请检查网络!",
      获取持币复利推荐奖励合约错误请检查网络:
        "获取持币复利+推荐奖励合约错误,请检查网络!",
      获取持币分红合约错误请检查网络: "获取持币分红合约错误,请检查网络!",
      获取燃烧底池合约错误请检查网络: "获取燃烧底池合约错误,请检查网络!",
      获取暴力分红合约错误请检查网络: "获取暴力分红合约错误,请检查网络!",
      获取314合约错误请检查网络: "获取314合约错误,请检查网络!",
      获取黑洞分红合约错误请检查网络: "获取黑洞分红合约错误,请检查网络!",
      未知错误: "未知错误!",
    },
    holdInviter: {
      持币复利推荐奖励: "持币复利+推荐奖励",
      持币自动生息代币资产累积打造去中心化银行:
        "持币自动生息、代币资产累积、打造去中心化银行!",
      复利控制: "复利控制",
      修改复利开始时间: "修改复利开始时间",
      复利开始时间必须大于当前时间: "复利开始时间必须大于当前时间",
      修改复利率: "修改复利率",
      复利设置: "复利设置",
      复利描述1: "复利开始时间: 计算复利的开始时间,仅可设置为今天以后",
      复利描述2: "复利率: 每次计算复利时增长的利率,最小单位为万分之一,即0.01%",
      复利描述3: "复利周期: 每隔多久计算一次复利的时间,默认以天为单位",
      复利描述4:
        "为防止通胀过快,发币地址,池子地址,合约地址,不参与复利,可自行在控制台调整",
      复利描述5:
        "举例:复利率为1%,复利周期1天,当A有100枚代币,接下来每天的余额为101,102.01,103.0301",
      复利描述6: "以此类推",
      复利开始时间: "复利开始时间",
      选择日期: "选择日期",
      yyyy年MM月dd日: "yyyy 年 MM 月 dd 日",
      复利率: "复利率",
      修改复利周期: "修改复利周期",
      设置复利黑名单: "设置复利黑名单",
      复利周期: "复利周期(天)",
      推荐说明1: "此处的100%为所有推荐人占比之和,需保证各代奖励总和为100%",
      推荐说明2: "一代: 交易地址的直属上级,其他代数以此类推",
      推荐说明3: "其他代数奖励: 填入前三代奖励百分比后,其他代数奖励比例相同",
      推荐说明4:
        "计算方式:其他每代奖励,保留整数位,多余部分给到第一代,保证总额为100%",
      复利黑名单地址: "复利黑名单地址",
      黑名单地址将不会参与复利计算收币地址池子地址合约地址默认不参与复利以规避通胀速度过快:
        "黑名单地址将不会参与复利计算,收币地址,池子地址,合约地址默认不参与复利,以规避通胀速度过快",
      获取复利开始时间失败请检查网络: "获取复利开始时间失败,请检查网络!",
      获取复利周期失败请检查网络: "获取复利周期失败,请检查网络!",
      获取oneday失败请检查网络: "获取oneday失败,请检查网络!",
      获取复利率失败请检查网络: "获取复利率失败,请检查网络!",
      获取邀请代数错误请检查网络: "获取邀请代数错误,请检查网络!",
      获取fristRate错误请检查网络: "获取fristRate错误,请检查网络!",
      获取secondRate错误请检查网络: "获取secondRate错误,请检查网络!",
      获取thirdRate错误请检查网络: "获取thirdRate错误,请检查网络!",
      获取leftRate错误请检查网络: "获取leftRate错误,请检查网络!",
      获取_minTransAmount错误请检查网络: "获取_minTransAmount错误,请检查网络!",
      获取撤池费率错误请检查网络: "获取撤池费率错误,请检查网络!",
      复利周期不可设置为0: "复利周期不可设置为0!",
    },
    holdReflection: {
      分红本币: "分红本币",
      交易中指定额度的代币会按持币比例分配给所有持币者实现持币分红:
        "交易中指定额度的代币会按持币比例分配给所有持币者,实现持币分红",
      交易中指定额度的代币将会转换为底池代币并自动转入营销钱包中用于项目方做其他营销:
        "交易中指定额度的代币将会转换为底池代币并自动转入营销钱包中,用于项目方做其他营销",
      简单干净合约无黑白名单无权限加池自动开盘持币即可获益:
        "简单干净合约,无黑白名单,无权限,加池自动开盘,持币即可获益!",
    },
    holdRefOthers: {
      MINT公平发射全民暴力分红社区共创共赢:
        "MINT公平发射,全民暴力分红,社区共创共赢!",
      分红最小持币量: "分红最小持币量",
      开启交易后Mint功能失效: "(开启交易后,Mint功能失效)",
      分红最小持币量格式不正确: "分红最小持币量格式不正确!",
    },
    index: {
      标准代币: "标准代币",
      发行量格式不正确: "发行量格式不正确",
      干净合约方便上手无税无功能Ave检测全绿:
        "干净合约、方便上手、无税无功能、Ave检测全绿",
    },
    LPBurn: {
      Mint底池燃烧: "Mint+底池燃烧",
      小时: "小时",
      MINT公平发射底池定时燃烧价格被动上涨:
        "MINT公平发射,底池定时燃烧、价格被动上涨!",
      燃烧设置: "燃烧设置",
      燃烧说明1: "燃烧周期: 每隔多久燃烧一次底池,以小时为单位,有卖盘时燃烧",
      燃烧说明2:
        "燃烧百分比:每次底池燃烧时销毁的代币占池子代币总量的比例,最小单位为万分之一,即0.01%",
      燃烧说明3:
        "为防止池子燃烧速度过快导致无法交易,燃烧百分比最大为1%,最小燃烧周期为1小时,即池子代币每天最多销毁24%",
      燃烧说明4:
        "举例:燃烧百分比为1%,燃烧周期1小时,池子内有100枚代币,在买卖数额相同的情况下,接下来每天的余额约为76,57,43",
      燃烧说明5: "实际涨幅根据交易量实时变化,无法准确预估",
      燃烧周期: "燃烧周期(小时)",
      燃烧百分比: "燃烧百分比",
      开启交易后底池开始燃烧: "(开启交易后,底池开始燃烧)",
      燃烧百分比最大为: "燃烧百分比最大为1%!",
      价格格式不正确: "价格格式不正确",
      Mint价格: "Mint价格",
      Mint每份数量: "Mint每份数量",
      Mint最大份数: "Mint最大份数",
      燃烧百分比: "燃烧百分比",
      设置每份价格: "设置每份价格",
      设置Mint每份价格: "设置Mint每份价格",
      设置每份数量: "设置每份数量",
      设置最大份数: "设置最大份数",
      撤池税率最大为100撤池所得币将销毁: "撤池税率:最大为100%,撤池所得币将销毁",
      燃烧控制: "燃烧控制",
      修改燃烧百分比: "修改燃烧百分比",
      注燃烧百分比最大为1最小为: "注:燃烧百分比最大为1%,最小为0.01%",
      修改燃烧周期: "修改燃烧周期",
      跟卖比例不可设置为0: "跟卖比例不可设置为0",
      最大份数不可设置为0: "最大份数不可设置为0",
    },
    LPinviterDetail: {
      提取合约内分红代币: "提取合约内分红代币",
      获取邀请代数错误请检查网络: "获取邀请代数错误,请检查网络!",
      获取fristRate错误请检查网络: "获取fristRate错误,请检查网络!",
      获取secondRate错误请检查网络: "获取secondRate错误,请检查网络!",
      获取thirdRate错误请检查网络: "获取thirdRate错误,请检查网络!",
      获取leftRate错误请检查网络: "获取leftRate错误,请检查网络!",
      获取_minTransAmount错误请检查网络: "获取_minTransAmount错误,请检查网络!",
      获取杀机器人错误请检查网络: "获取杀机器人错误请检查网络",
      余额为0: "余额为0",
    },
    LPMine: {
      LP挖矿推荐奖励: "LP挖矿+推荐奖励",
      LP挖矿奖励: "LP挖矿奖励",
      加池挖矿恒定产出无前端无后端完全去中心化运行:
        "加池挖矿、恒定产出、无前端无后端、完全去中心化运行",
      挖矿总量占比: "挖矿总量占比",
      挖矿奖励说明: "挖矿奖励说明",
      说明1:
        "挖矿总量占比:挖矿总奖励占供应量的比值,从总供应量中分出 ?%用于挖矿和推荐奖励",
      说明2:
        "每日挖矿奖励:默认每个地址每24小时可获取一次挖矿奖励,每个地址获得的数量为每日挖矿奖励*LP占比",
      说明3:
        "最小LP持有量:默认为0,即所有LP均可参与挖矿,为减少gas费用,建议在首次添加流动性后在控制台修改",
      每日挖矿奖励: "每日挖矿奖励",
      推荐奖励说明1:
        "推荐奖励: 获得挖矿奖励地址的推荐人可获得相同数量的推荐奖励",
      推荐奖励说明2:
        "最小持币量:最小代币持有量,为减少gas费用,仅持币量大于最小持有量的地址才可以领取邀请奖励,0即为所有推荐人均可获得奖励",
      推荐奖励说明3: "一代: 奖励地址的直属上级,其他代数以此类推",
      推荐奖励说明4:
        "其他代数奖励: 填入前三代奖励百分比后,其他代数奖励比例相同",
      推荐奖励说明5:
        "计算方式:其他每代奖励,保留整数位,多余部分给到第一代,保证总额为100%",
      最小持币量: "最小持币量",
      分红代数: "分红代数",
      剩余每代: "剩余每代",
      绑定推荐关系需要上级向下级转账一定数额的代币当下级回转后视为绑定成功:
        "绑定推荐关系需要上级向下级转账一定数额的代币,当下级回转后,视为绑定成功",
      例默认最小转账金额为0上级可转账任意给下级下级回转任意数量最小转账金额可在控制台设置:
        "例:默认最小转账金额为0,上级可转账任意给下级,下级回转任意数量,最小转账金额可在控制台设置",
      添加池子后的首次交易需要在控制台手动开启如关闭则添加流动性后立即可以进行交易交易开启后无法关闭:
        "添加池子后的首次交易需要在控制台手动开启(如关闭,则添加流动性后立即可以进行交易),交易开启后无法关闭",
      黑名单功能: "黑名单功能",
      挖矿比例应小于100: "挖矿比例应小于100",
      最小代币持有量格式不正确: "最小代币持有量格式不正确!",
      最小LP持有量格式不正确: "最小LP持有量格式不正确",
      每日挖矿奖励格式不正确: "每日挖矿奖励格式不正确",
      交易参数: "交易参数",
      挖矿参数: "挖矿参数",
      矿池供给量: "矿池供给量",
      每日挖矿总量: "每日挖矿总量",
      挖矿最小LP持有量: "挖矿最小LP持有量",
      注仅LP持有量大于最小LP持有量的地址才可以参与挖矿:
        "注:仅LP持有量大于最小LP持有量的地址才可以参与挖矿",
      推荐奖励最小持币量: "推荐奖励最小持币量",
      注仅持币量大于最小持有量的地址才可以获得邀请奖励:
        "注:仅持币量大于最小持有量的地址才可以获得邀请奖励",
      挖矿控制: "挖矿控制",
      设置挖矿黑名单: "设置挖矿黑名单",
      挖矿黑名单地址: "挖矿黑名单地址",
      该地址将不会收到挖矿奖励: "该地址将不会收到挖矿奖励",
      设置每日挖矿总量: "设置每日挖矿总量",
      每日挖矿总量: "每日挖矿总量",
      设置最小LP持有量: "设置最小LP持有量",
      最小LP持有量: "最小LP持有量",
      修改推荐奖励最小持币量: "修改推荐奖励最小持币量",
      仅持币量大于最小持有量的地址才可以获得邀请奖励:
        "仅持币量大于最小持有量的地址才可以获得邀请奖励",
      获取邀请代数错误请检查网络: "获取邀请代数错误,请检查网络!",
      获取最小LP持有量错误请检查网络: "获取最小LP持有量错误,请检查网络!",
      获取挖矿比例错误请检查网络: "获取挖矿比例错误,请检查网络!",
      获取LP地址错误请检查网络: "获取LP地址错误,请检查网络!",
      获取单次挖矿总量错误请检查网络: "获取单次挖矿总量错误,请检查网络!",
    },
    LPReflection: {
      加池参与分红池子越来越厚币价螺旋上涨:
        "加池参与分红、池子越来越厚,币价螺旋上涨!",
      黑名单功能: "黑名单功能",
      交易所地址不正确: "交易所地址不正确!",
      底池代币地址不正确: "底池代币地址不正确!",
      分红代币地址不正确: "分红代币地址不正确!",
    },
    LPwithInviter: {
      LP分红推荐奖励: "LP分红+推荐奖励",
      下级交易上级奖励持续裂变壮大规模:
        "下级交易、上级奖励、持续裂变、壮大规模!",
      交易中指定额度的代币用来购买分红代币并发送给LP持有者:
        "交易中指定额度的代币,用来购买分红代币, 并发送给LP持有者",
      交易中指定额度的代币做为推荐奖励并分发给其上级:
        "交易中指定额度的代币做为推荐奖励,并分发给其上级",
      说明1: "交易中指定额度的代币做为推荐奖励, 并分发给其上级",
      说明2: "此处的100%为所有推荐人占比之和,需保证各代奖励总和为100%",
      说明3: "一代: 交易地址的直属上级,其他代数以此类推",
      说明4: "其他代数奖励: 填入前三代奖励百分比后,其他代数奖励比例相同",
      说明5:
        "计算方式:其他每代奖励,保留整数位,多余部分给到第一代,保证总额为100%",
      分红代币地址不正确: "分红代币地址不正确!",
    },
  },
  prettyWallet:prettyWalletZh,
};
