export const prettyWalletEn = {
  tips1Title: "🔒 Security Notice",
  tips1Desc:
    "Wallet generation is executed completely on your local device. The website does not and will never store any private key information.",
  tips2Title: "💡 Usage Recommendation",
  tips2Desc:
    "It is recommended to disconnect from the network (turn off WiFi or unplug the network cable) when generating wallets to ensure maximum security.",
  tips3Title: "🔐 Encryption Standard",
  tips3Desc:
    "Uses AES-128-CTR encryption algorithm with BKDF2-SHA256 derivation function and 65536 hash rounds for key protection.",
  prefix: "Vanity Prefix",
  suffix: "Vanity Suffix",
  eg: "Example",
  caseSensitive: "Case Sensitive",
  yes: "Yes",
  no: "No",
  workers: "Threads",
  workerTips: "(Adjust based on your device performance, max 64)",
  stopGen: "Stop Generation",
  startGen: "Start Generation",
  generateInfo: "Generation Info",
  difficulty: "Difficulty",
  generated: "Generated",
  addresses: "Addresses",
  estimateTime: "Estimated Time",
  seconds: "seconds",
  speed: "Speed",
  addressesPerSec: "addresses / sec",
  status: "Status",
  totalTime: "Total Time",
  generating: "Generating...",
  stopped: "Stopped",
  completed: "Completed",
  prettyWallet: "Vanity Wallet",
  publicKey: "Public Key",
  privateKey: "Private Key",
  copyPublicKey: "Copy Public Key",
  copyPrivateKey: "Copy Private Key",
  copiedToClipboard: " copied to clipboard",
  copyFailed: "Copy failed, please copy manually",
  placeholder: "Input ...",
  minutes: "min",
  evm: {
    title: "EVM Wallet Vanity Address Generator",
    prefixMsg: "Prefix can only contain 0-9, A-F, a-f characters",
    warning:
      "Prefix and suffix can only contain 0-9, A-F, a-f characters. It is recommended that the total length does not exceed 5 characters to avoid excessive time consumption",
    suffixMsg: "Suffix can only contain 0-9, A-F, a-f characters",
  },
  trx: {
    title: "Tron Wallet Vanity Address Generator",
    prefixMsg:
      "The first character of prefix can only be uppercase letters excluding O and I, other characters must be from Base58 character set",
    warning:
      "As shown in the example: The first character of Tron address is fixed as T, so the first character of prefix is the second character of the address. The first character of prefix can only be uppercase letters excluding O and I. For suffix, please enter characters from Base58 character set (excluding 0, O, I, l). It's recommended to keep the total length under 5 characters to avoid excessive time consumption",
    suffixMsg:
      "For suffix, please enter characters from Base58 character set (excluding 0, O, I, l)",
  },
};

export const prettyWalletZh = {
  tips1Title: "🔒安全提醒",
  tips1Desc: "钱包生成完全在您的本地设备执行，网站不会存储任何私钥信息。",
  tips2Title: "💡使用建议",
  tips2Desc:
    "生成钱包时建议断开网络连接（关闭WiFi或拔掉网线）以确保最大安全性。",
  tips3Title: "🔐加密标准",
  tips3Desc:
    "使用AES-128-CTR加密算法，配合BKDF2-SHA256推导函数和65536次散列轮进行密钥保护。",
  prefix: "靓号前缀",
  suffix: "靓号后缀",
  eg: "例",
  caseSensitive: "区分大小写",
  yes: "是",
  no: "否",
  workers: "线程",
  workerTips: "(根据自身设备性能调整，最大64)",
  stopGen: "暂停生成",
  startGen: "开始生成",
  generateInfo: "生成信息",
  difficulty: "难度",
  generated: "已生成",
  addresses: "地址",
  estimateTime: "预计时间",
  seconds: "秒",
  speed: "速度",
  addressesPerSec: "地址 / 秒",
  status: "状态",
  totalTime: "总耗时",
  generating: "生成中...",
  stopped: "已停止",
  completed: "已完成",
  prettyWallet: "靓号钱包",
  publicKey: "公钥",
  privateKey: "私钥",
  copyPublicKey: "复制公钥",
  copyPrivateKey: "复制私钥",
  copiedToClipboard: "已复制到剪贴板",
  copyFailed: "复制失败，请手动复制",
  placeholder: "请输入...",
  minutes: "分",
  evm: {
    title: "EVM钱包靓号地址生成器",
    prefixMsg: "前缀只能包含0-9、A-F、a-f字符",
    warning:
      "前缀和后缀只能包含0-9、A-F、a-f字符。建议总长度不超过5位，避免耗时过久",
    suffixMsg: "后缀只能包含0-9、A-F、a-f字符",
  },
  trx: {
    title: "波场钱包靓号地址生成器",
    prefixMsg: "前缀第一位只能是不包含O和I的大写字母，其余字符必须符合Base58字符集（排除0、O、I、l的数字或字母）",
    warning:
      "如例子所示：波场地址第一位固定是T因此前缀第一位是地址的第二位,前缀第一位只能是不包含O和I的大写字母，后缀请填入Base58字符集中的字符（排除0、O、I、l的数字或字母）。建议总长度不超过5位，避免耗时过久",
    suffixMsg:
      "后缀请填入Base58字符集中的字符（排除0、O、I、l）",
  },
};
