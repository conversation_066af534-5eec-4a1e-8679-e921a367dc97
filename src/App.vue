<template>
  <div id="app">
    <router-view :key="currentRouteKey" />
  </div>
</template>

<script>
export default {
  name: "App",
  data() {
    return {
      currentRouteKey: 0, // 初始值
    };
  },
  watch: {
    "$i18n.locale"() {
      this.updateRouteTitles();
      this.currentRouteKey += 1
    }
  },
  created() {
    this.updateRouteTitles();
    this.currentRouteKey += 1
  },
  methods: {
    updateRouteTitles() {
      this.$router.options.routes.forEach((route) => {
        if (route.meta && route.name) {
          route.meta.title = this.$t("router." + route.name);
        }
        if (route.children) {
          route.children.forEach((child) => {
            if (child.meta && child.name) {
              child.meta.title = this.$t("router." + child.name);
              if(child.name == '帮助文档'  ){
                if(this.$i18n.locale == 'en-US'){
                  child.path = "https://help.pandatool.org/english"
                }else{
                   child.path = "https://help.pandatool.org"
                }
                
              } 
              if(child.name == 'Solana发币'  ){
                if(this.$i18n.locale == 'en-US'){
                  child.path = "https://solana.pandatool.org/en"
                }else{
                   child.path = "https://solana.pandatool.org/"
                }
                
              }
            }
          });
        }
      });
    },
  },
  
};
</script>
