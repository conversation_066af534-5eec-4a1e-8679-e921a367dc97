[{"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"inputs": [], "name": "claimBalance", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "getCoinCost", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address payable[]", "name": "_addresses", "type": "address[]"}, {"internalType": "uint256[]", "name": "_amounts", "type": "uint256[]"}], "name": "multiTransferETH", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "address payable", "name": "_token", "type": "address"}, {"internalType": "address payable[]", "name": "_addresses", "type": "address[]"}, {"internalType": "uint256[]", "name": "_amounts", "type": "uint256[]"}], "name": "multiTransferToken", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "recipient", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "setCoinCost", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address payable", "name": "_recipient", "type": "address"}], "name": "setRecipient", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]