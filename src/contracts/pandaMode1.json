[{"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "Sender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "Value", "type": "uint256"}], "name": "Received", "type": "event"}, {"inputs": [{"internalType": "bytes32", "name": "salt", "type": "bytes32"}, {"internalType": "string[]", "name": "stringParams", "type": "string[]"}, {"internalType": "address[]", "name": "addressParams", "type": "address[]"}, {"internalType": "uint256[]", "name": "numberParams", "type": "uint256[]"}, {"internalType": "bool[]", "name": "boolParams", "type": "bool[]"}], "name": "CreateContract", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "salt", "type": "bytes32"}, {"internalType": "string[]", "name": "stringParams", "type": "string[]"}, {"internalType": "address[]", "name": "addressParams", "type": "address[]"}, {"internalType": "uint256[]", "name": "numberParams", "type": "uint256[]"}], "name": "CreateContractNew", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "_creator", "type": "address"}], "name": "checkCreated<PERSON><PERSON><PERSON>", "outputs": [{"components": [{"internalType": "address", "name": "_tokenAddress", "type": "address"}, {"internalType": "string", "name": "_name", "type": "string"}, {"internalType": "string", "name": "_symbol", "type": "string"}, {"internalType": "uint256", "name": "_createdTime", "type": "uint256"}], "internalType": "struct PandaMode1.Coin[]", "name": "", "type": "tuple[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "coinCost", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "createdCode", "outputs": [{"internalType": "bytes", "name": "", "type": "bytes"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "name": "createdCoin", "outputs": [{"internalType": "address", "name": "_tokenAddress", "type": "address"}, {"internalType": "string", "name": "_name", "type": "string"}, {"internalType": "string", "name": "_symbol", "type": "string"}, {"internalType": "uint256", "name": "_createdTime", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "_coinCost", "type": "uint256"}], "name": "setCost", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address payable", "name": "_recipient", "type": "address"}], "name": "setRecipient", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"stateMutability": "payable", "type": "receive"}]