[{"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "Sender", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "Value", "type": "uint256"}], "name": "Received", "type": "event"}, {"inputs": [], "name": "FeeV2", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "router", "type": "address"}, {"internalType": "address", "name": "token", "type": "address"}, {"internalType": "address", "name": "currency", "type": "address"}, {"internalType": "uint256", "name": "addTokenAmt", "type": "uint256"}, {"internalType": "uint256", "name": "addCurrenyAmt", "type": "uint256"}, {"internalType": "bool", "name": "currencyIsETH", "type": "bool"}], "name": "addLiquidityToken", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}, {"internalType": "address", "name": "spender", "type": "address"}, {"internalType": "address", "name": "to", "type": "address"}], "name": "claimToken", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "lockAddr", "type": "address"}, {"internalType": "address", "name": "tokenAddress", "type": "address"}, {"internalType": "uint256", "name": "lockAmount", "type": "uint256"}, {"internalType": "uint256", "name": "unlockDate", "type": "uint256"}, {"internalType": "bool", "name": "isLpToken", "type": "bool"}, {"internalType": "string", "name": "description", "type": "string"}], "name": "lockToken", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "recipient", "outputs": [{"internalType": "address payable", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "router", "type": "address"}, {"internalType": "address", "name": "lpAddress", "type": "address"}, {"internalType": "address payable", "name": "receiveAddress", "type": "address"}, {"internalType": "address", "name": "token", "type": "address"}, {"internalType": "address", "name": "currency", "type": "address"}, {"internalType": "uint256", "name": "removeAmount", "type": "uint256"}, {"internalType": "bool", "name": "currencyIsETH", "type": "bool"}], "name": "removeLiquidityToken", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"stateMutability": "payable", "type": "receive"}]