{"97": ["https://testnet.bscscan.com/address/", {"ModeAddress": "******************************************", "Fee": "0.1", "chainSymbol": "TBNB"}, {"ModeAddress": "******************************************", "Fee": "0.1", "chainSymbol": "TBNB", "_WETH": "******************************************", "rewardOptions": [{"label": "BNB", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}, {"label": "CAKE", "value": "******************************************"}], "currencyOptions": [{"label": "BNB", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}, {"label": "CAKE", "value": "******************************************"}], "swapOptions": [{"label": "Pancake", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.1", "chainSymbol": "TBNB", "_WETH": "******************************************", "rewardOptions": [{"label": "BNB", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}, {"label": "CAKE", "value": "******************************************"}], "currencyOptions": [{"label": "BNB", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}, {"label": "CAKE", "value": "******************************************"}], "swapOptions": [{"label": "Pancake", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.1", "chainSymbol": "TBNB", "_WETH": "******************************************", "rewardOptions": [{"label": "BNB", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}, {"label": "CAKE", "value": "******************************************"}], "currencyOptions": [{"label": "BNB", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}, {"label": "CAKE", "value": "******************************************"}], "swapOptions": [{"label": "Pancake", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.1", "chainSymbol": "TBNB", "_WETH": "******************************************", "currencyOptions": [{"label": "BNB", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}, {"label": "CAKE", "value": "******************************************"}], "swapOptions": [{"label": "Pancake", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.1", "chainSymbol": "TBNB", "_WETH": "******************************************", "currencyOptions": [{"label": "BNB", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}, {"label": "CAKE", "value": "******************************************"}], "swapOptions": [{"label": "Pancake", "value": "******************************************"}]}], "56": ["https://bscscan.com/address/", {"ModeAddress": "******************************************", "Fee": "0.05", "chainSymbol": "BNB"}, {"ModeAddress": "******************************************", "Fee": "0.1", "chainSymbol": "BNB", "_WETH": "******************************************", "rewardOptions": [{"label": "BNB", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}, {"label": "BUSD", "value": "******************************************"}], "currencyOptions": [{"label": "BNB", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}, {"label": "BUSD", "value": "******************************************"}], "swapOptions": [{"label": "Pancake", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.12", "chainSymbol": "BNB", "_WETH": "******************************************", "rewardOptions": [{"label": "BNB", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}, {"label": "BUSD", "value": "******************************************"}], "currencyOptions": [{"label": "BNB", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}, {"label": "BUSD", "value": "******************************************"}], "swapOptions": [{"label": "Pancake", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "oldModeAddress": "******************************************", "Fee": "0.2", "chainSymbol": "BNB", "_WETH": "******************************************", "rewardOptions": [{"label": "BNB", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}, {"label": "BUSD", "value": "******************************************"}], "currencyOptions": [{"label": "BNB", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}, {"label": "BUSD", "value": "******************************************"}], "swapOptions": [{"label": "Pancake", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.15", "chainSymbol": "BNB", "_WETH": "******************************************", "rewardOptions": [{"label": "BNB", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}, {"label": "BUSD", "value": "******************************************"}], "currencyOptions": [{"label": "BNB", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}, {"label": "BUSD", "value": "******************************************"}], "swapOptions": [{"label": "Pancake", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.06", "chainSymbol": "BNB", "_WETH": "******************************************", "rewardOptions": [{"label": "BNB", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}, {"label": "BUSD", "value": "******************************************"}], "currencyOptions": [{"label": "BNB", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}, {"label": "BUSD", "value": "******************************************"}], "swapOptions": [{"label": "Pancake", "value": "******************************************"}]}], "1": ["https://etherscan.io/address/", {"ModeAddress": "******************************************", "Fee": "0.03", "chainSymbol": "ETH"}, {"ModeAddress": "******************************************", "Fee": "0.0", "chainSymbol": "ETH", "_WETH": "******************************************", "rewardOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "currencyOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "swapOptions": [{"label": "Uniswap", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.0", "chainSymbol": "ETH", "_WETH": "******************************************", "rewardOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "currencyOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "swapOptions": [{"label": "Uniswap", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.0", "chainSymbol": "ETH", "_WETH": "******************************************", "rewardOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "currencyOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "swapOptions": [{"label": "Uniswap", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.0", "chainSymbol": "ETH", "_WETH": "******************************************", "rewardOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "currencyOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "swapOptions": [{"label": "Uniswap", "value": "******************************************"}]}], "5": ["https://goerli.etherscan.io/address/", {"ModeAddress": "0x3E9185Dc73AfeB926182e04B8d2eDC4A2Ed577e4", "Fee": "0.0", "chainSymbol": "ETH"}, {"ModeAddress": "******************************************", "Fee": "0.0", "chainSymbol": "ETH", "_WETH": "******************************************", "rewardOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "currencyOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "swapOptions": [{"label": "Uniswap", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.0", "chainSymbol": "ETH", "_WETH": "******************************************", "rewardOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "currencyOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "swapOptions": [{"label": "Uniswap", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.0", "chainSymbol": "ETH", "_WETH": "******************************************", "rewardOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "currencyOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "swapOptions": [{"label": "Uniswap", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.0", "chainSymbol": "ETH", "_WETH": "******************************************", "rewardOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "currencyOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "swapOptions": [{"label": "Uniswap", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.0", "chainSymbol": "ETH", "_WETH": "******************************************", "rewardOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "currencyOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "swapOptions": [{"label": "Uniswap", "value": "******************************************"}]}], "250": ["https://ftmscan.com/token/", {"ModeAddress": "******************************************", "Fee": "0.0", "chainSymbol": "FTM"}, {"ModeAddress": "******************************************", "Fee": "0.0", "chainSymbol": "FTM", "_WETH": "******************************************", "rewardOptions": [{"label": "FTM", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "currencyOptions": [{"label": "FTM", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "swapOptions": [{"label": "SpookySwap", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.0", "chainSymbol": "FTM", "_WETH": "******************************************", "rewardOptions": [{"label": "FTM", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "currencyOptions": [{"label": "FTM", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "swapOptions": [{"label": "SpookySwap", "value": "******************************************"}]}], "42161": ["https://arbiscan.io/token/", {"ModeAddress": "******************************************", "Fee": "0.03", "chainSymbol": "ETH"}, {"ModeAddress": "******************************************", "Fee": "0.0", "chainSymbol": "ETH", "_WETH": "******************************************", "rewardOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "currencyOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "swapOptions": [{"label": "Sushiswap", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.0", "chainSymbol": "ETH", "_WETH": "******************************************", "rewardOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "currencyOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "swapOptions": [{"label": "Sushiswap", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.0", "chainSymbol": "ETH", "_WETH": "******************************************", "rewardOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "currencyOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "swapOptions": [{"label": "Sushiswap", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.0", "chainSymbol": "ETH", "_WETH": "******************************************", "rewardOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "currencyOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "swapOptions": [{"label": "Sushiswap", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.04", "chainSymbol": "ETH", "_WETH": "******************************************", "rewardOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "currencyOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "swapOptions": [{"label": "Sushiswap", "value": "******************************************"}]}], "137": ["https://polygonscan.com/address/", {"ModeAddress": "******************************************", "Fee": "0.0", "chainSymbol": "ETH"}, {"ModeAddress": "******************************************", "Fee": "0.0", "chainSymbol": "ETH", "_WETH": "******************************************", "rewardOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "currencyOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "swapOptions": [{"label": "Quickswap", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.0", "chainSymbol": "ETH", "_WETH": "******************************************", "rewardOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "currencyOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "swapOptions": [{"label": "Quickswap", "value": "******************************************"}]}], "1116": ["https://scan.coredao.org/token/", {"ModeAddress": "******************************************", "Fee": "10.0", "chainSymbol": "CORE"}, {"ModeAddress": "******************************************", "Fee": "20.0", "chainSymbol": "CORE", "_WETH": "******************************************", "rewardOptions": [{"label": "CORE", "value": "******************************************"}, {"label": "USDT(45F1)", "value": "******************************************"}, {"label": "USDT(5817)", "value": "******************************************"}], "currencyOptions": [{"label": "CORE", "value": "******************************************"}, {"label": "USDT(45F1)", "value": "******************************************"}, {"label": "USDT(5817)", "value": "******************************************"}], "swapOptions": [{"label": "IceCreamswap", "value": "******************************************"}, {"label": "LFGswap", "value": "******************************************"}, {"label": "Archerswap", "value": "******************************************"}]}, {"ModeAddress": "0x69078EDC7B86bf6aA8FeF2221df79DE04a8513Ae", "Fee": "30.0", "chainSymbol": "CORE", "_WETH": "******************************************", "rewardOptions": [{"label": "CORE", "value": "******************************************"}, {"label": "USDT(45F1)", "value": "******************************************"}, {"label": "USDT(5817)", "value": "******************************************"}], "currencyOptions": [{"label": "CORE", "value": "******************************************"}, {"label": "USDT(45F1)", "value": "******************************************"}, {"label": "USDT(5817)", "value": "******************************************"}], "swapOptions": [{"label": "IceCreamswap", "value": "******************************************"}, {"label": "LFGswap", "value": "******************************************"}, {"label": "Archerswap", "value": "******************************************"}]}, {"ModeAddress": "0x48da6c7Eaa4D73952C9801Ddfb56Ac83bD4a56EA", "Fee": "40.0", "chainSymbol": "CORE", "_WETH": "******************************************", "rewardOptions": [{"label": "CORE", "value": "******************************************"}, {"label": "USDT(45F1)", "value": "******************************************"}, {"label": "USDT(5817)", "value": "******************************************"}], "currencyOptions": [{"label": "CORE", "value": "******************************************"}, {"label": "USDT(45F1)", "value": "******************************************"}, {"label": "USDT(5817)", "value": "******************************************"}], "swapOptions": [{"label": "IceCreamswap", "value": "******************************************"}, {"label": "LFGswap", "value": "******************************************"}, {"label": "Archerswap", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "30.0", "chainSymbol": "CORE", "_WETH": "******************************************", "rewardOptions": [{"label": "CORE", "value": "******************************************"}, {"label": "USDT(45F1)", "value": "******************************************"}, {"label": "USDT(5817)", "value": "******************************************"}], "currencyOptions": [{"label": "CORE", "value": "******************************************"}, {"label": "USDT(45F1)", "value": "******************************************"}, {"label": "USDT(5817)", "value": "******************************************"}], "swapOptions": [{"label": "IceCreamswap", "value": "******************************************"}, {"label": "LFGswap", "value": "******************************************"}, {"label": "Archerswap", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "15.0", "chainSymbol": "CORE", "_WETH": "******************************************", "rewardOptions": [{"label": "CORE", "value": "******************************************"}, {"label": "USDT(45F1)", "value": "******************************************"}, {"label": "USDT(5817)", "value": "******************************************"}], "currencyOptions": [{"label": "CORE", "value": "******************************************"}, {"label": "USDT(45F1)", "value": "******************************************"}, {"label": "USDT(5817)", "value": "******************************************"}], "swapOptions": [{"label": "IceCreamswap", "value": "******************************************"}, {"label": "LFGswap", "value": "******************************************"}, {"label": "Archerswap", "value": "******************************************"}]}], "66": ["https://www.okx.com/cn/explorer/oktc/token/", {"ModeAddress": "******************************************", "Fee": "0.5", "chainSymbol": "OKB"}, {"ModeAddress": "******************************************", "Fee": "1.0", "chainSymbol": "OKB", "_WETH": "******************************************", "rewardOptions": [{"label": "OKB", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "currencyOptions": [{"label": "OKB", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "swapOptions": [{"label": "OKCSwap", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "1.0", "chainSymbol": "OKB", "_WETH": "******************************************", "rewardOptions": [{"label": "OKB", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "currencyOptions": [{"label": "OKB", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "swapOptions": [{"label": "OKCSwap", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "2.0", "chainSymbol": "OKB", "_WETH": "******************************************", "rewardOptions": [{"label": "OKB", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "currencyOptions": [{"label": "OKB", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "swapOptions": [{"label": "OKCSwap", "value": "******************************************"}]}], "280": ["https://explorer.zksync.io/address/", {"ModeAddress": "******************************************", "Fee": "0.0", "chainSymbol": "ETH"}, {"ModeAddress": "******************************************", "Fee": "0.0", "chainSymbol": "ETH", "_WETH": "******************************************", "rewardOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDC", "value": "******************************************"}], "currencyOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDC", "value": "******************************************"}], "swapOptions": [{"label": "Syncswap", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.0", "chainSymbol": "ETH", "_WETH": "******************************************", "rewardOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDC", "value": "******************************************"}], "currencyOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDC", "value": "******************************************"}], "swapOptions": [{"label": "Quickswap", "value": "******************************************"}]}], "4002": ["https://testnet.ftmscan.com/token/", {"ModeAddress": "******************************************", "Fee": "0.0", "chainSymbol": "TFTM"}, {"ModeAddress": "******************************************", "Fee": "0.0", "chainSymbol": "TFTM", "_WETH": "******************************************", "rewardOptions": [{"label": "FTM", "value": "******************************************"}, {"label": "BLOQ", "value": "******************************************"}], "currencyOptions": [{"label": "FTM", "value": "******************************************"}, {"label": "BLOQ", "value": "******************************************"}], "swapOptions": [{"label": "SpookySwap", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.0", "chainSymbol": "TFTM", "_WETH": "******************************************", "rewardOptions": [{"label": "FTM", "value": "******************************************"}, {"label": "BLOQ", "value": "******************************************"}], "currencyOptions": [{"label": "FTM", "value": "******************************************"}, {"label": "BLOQ", "value": "******************************************"}], "swapOptions": [{"label": "SpookySwap", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.0", "chainSymbol": "TFTM", "_WETH": "******************************************", "rewardOptions": [{"label": "FTM", "value": "******************************************"}, {"label": "BLOQ", "value": "******************************************"}], "currencyOptions": [{"label": "FTM", "value": "******************************************"}, {"label": "BLOQ", "value": "******************************************"}], "swapOptions": [{"label": "SpookySwap", "value": "******************************************"}]}], "1115": ["https://scan.test.btcs.network/address/", {"ModeAddress": "******************************************", "Fee": "0.01", "chainSymbol": "CORE"}, {"ModeAddress": "******************************************", "Fee": "0.00", "chainSymbol": "CORE", "_WETH": "******************************************", "rewardOptions": [{"label": "CORE", "value": "******************************************"}, {"label": "MIGO", "value": "******************************************"}], "currencyOptions": [{"label": "CORE", "value": "******************************************"}, {"label": "MIGO", "value": "******************************************"}], "swapOptions": [{"label": "migoswap", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.0", "chainSymbol": "CORE", "_WETH": "******************************************", "rewardOptions": [{"label": "CORE", "value": "******************************************"}, {"label": "MIGO", "value": "******************************************"}], "currencyOptions": [{"label": "CORE", "value": "******************************************"}, {"label": "MIGO", "value": "******************************************"}], "swapOptions": [{"label": "migoswap", "value": "******************************************"}]}, {"ModeAddress": "0xcF376E024213Ca4AAAa70eC4C488C6B3E68853b5", "Fee": "0.0", "chainSymbol": "CORE", "_WETH": "******************************************", "rewardOptions": [{"label": "CORE", "value": "******************************************"}, {"label": "MIGO", "value": "******************************************"}], "currencyOptions": [{"label": "CORE", "value": "******************************************"}, {"label": "MIGO", "value": "******************************************"}], "swapOptions": [{"label": "migoswap", "value": "******************************************"}]}, {"ModeAddress": "0x7d5d8aFe4806009d08a1B134cFCC8dbe2a68F835", "Fee": "0.0", "chainSymbol": "CORE", "_WETH": "******************************************", "rewardOptions": [{"label": "CORE", "value": "******************************************"}, {"label": "MIGO", "value": "******************************************"}], "currencyOptions": [{"label": "CORE", "value": "******************************************"}, {"label": "MIGO", "value": "******************************************"}], "swapOptions": [{"label": "migoswap", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.0", "chainSymbol": "CORE", "_WETH": "******************************************", "rewardOptions": [{"label": "CORE", "value": "******************************************"}, {"label": "MIGO", "value": "******************************************"}], "currencyOptions": [{"label": "CORE", "value": "******************************************"}, {"label": "MIGO", "value": "******************************************"}], "swapOptions": [{"label": "migoswap", "value": "******************************************"}]}], "421613": ["https://goerli.arbiscan.io/address/", {"ModeAddress": "******************************************", "Fee": "0.00", "chainSymbol": "AGOR"}, {"ModeAddress": "******************************************", "Fee": "0.00", "chainSymbol": "AGOR", "_WETH": "******************************************", "rewardOptions": [{"label": "WETH", "value": "******************************************"}, {"label": "USDC", "value": "******************************************"}], "currencyOptions": [{"label": "WETH", "value": "******************************************"}, {"label": "USDC", "value": "******************************************"}], "swapOptions": [{"label": "swaper", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.0", "chainSymbol": "AGOR", "_WETH": "******************************************", "rewardOptions": [{"label": "WETH", "value": "******************************************"}, {"label": "USDC", "value": "******************************************"}], "currencyOptions": [{"label": "WETH", "value": "******************************************"}, {"label": "USDC", "value": "******************************************"}], "swapOptions": [{"label": "swaper", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.0", "chainSymbol": "AGOR", "_WETH": "******************************************", "rewardOptions": [{"label": "WETH", "value": "******************************************"}, {"label": "USDC", "value": "******************************************"}], "currencyOptions": [{"label": "WETH", "value": "******************************************"}, {"label": "USDC", "value": "******************************************"}], "swapOptions": [{"label": "swaper", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.0", "chainSymbol": "AGOR", "_WETH": "******************************************", "rewardOptions": [{"label": "WETH", "value": "******************************************"}, {"label": "USDC", "value": "******************************************"}], "currencyOptions": [{"label": "WETH", "value": "******************************************"}, {"label": "USDC", "value": "******************************************"}], "swapOptions": [{"label": "swaper", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.0", "chainSymbol": "AGOR", "_WETH": "******************************************", "rewardOptions": [{"label": "WETH", "value": "******************************************"}, {"label": "USDC", "value": "******************************************"}], "currencyOptions": [{"label": "WETH", "value": "******************************************"}, {"label": "USDC", "value": "******************************************"}], "swapOptions": [{"label": "panARB", "value": "******************************************"}]}], "84531": ["https://goerli.basescan.org/token/", {"ModeAddress": "******************************************", "Fee": "0.03", "chainSymbol": "BaseETH"}, {"ModeAddress": "******************************************", "Fee": "0.00", "chainSymbol": "BaseETH", "_WETH": "******************************************", "rewardOptions": [{"label": "WETH", "value": "******************************************"}, {"label": "USDC", "value": "******************************************"}], "currencyOptions": [{"label": "WETH", "value": "******************************************"}, {"label": "USDC", "value": "******************************************"}], "swapOptions": [{"label": "swaper", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.0", "chainSymbol": "BaseETH", "_WETH": "******************************************", "rewardOptions": [{"label": "WETH", "value": "******************************************"}, {"label": "USDC", "value": "******************************************"}], "currencyOptions": [{"label": "WETH", "value": "******************************************"}, {"label": "USDC", "value": "******************************************"}], "swapOptions": [{"label": "swaper", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.0", "chainSymbol": "BaseETH", "_WETH": "******************************************", "rewardOptions": [{"label": "WETH", "value": "******************************************"}, {"label": "USDC", "value": "******************************************"}], "currencyOptions": [{"label": "WETH", "value": "******************************************"}, {"label": "USDC", "value": "******************************************"}], "swapOptions": [{"label": "swaper", "value": "******************************************"}]}], "8453": ["https://basescan.org/token/", {"ModeAddress": "******************************************", "Fee": "0.03", "chainSymbol": "ETH"}, {"ModeAddress": "******************************************", "Fee": "0.00", "chainSymbol": "ETH", "_WETH": "******************************************", "rewardOptions": [{"label": "WETH", "value": "******************************************"}, {"label": "USDC", "value": "******************************************"}], "currencyOptions": [{"label": "WETH", "value": "******************************************"}, {"label": "USDC", "value": "******************************************"}], "swapOptions": [{"label": "swaper", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.0", "chainSymbol": "ETH", "_WETH": "******************************************", "rewardOptions": [{"label": "WETH", "value": "******************************************"}, {"label": "USDC", "value": "******************************************"}], "currencyOptions": [{"label": "WETH", "value": "******************************************"}, {"label": "USDC", "value": "******************************************"}], "swapOptions": [{"label": "swaper", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.0", "chainSymbol": "ETH", "_WETH": "******************************************", "rewardOptions": [{"label": "WETH", "value": "******************************************"}, {"label": "USDC", "value": "******************************************"}], "currencyOptions": [{"label": "WETH", "value": "******************************************"}, {"label": "USDC", "value": "******************************************"}], "swapOptions": [{"label": "swaper", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.0", "chainSymbol": "ETH", "_WETH": "******************************************", "rewardOptions": [{"label": "WETH", "value": "******************************************"}, {"label": "USDC", "value": "******************************************"}], "currencyOptions": [{"label": "WETH", "value": "******************************************"}, {"label": "USDC", "value": "******************************************"}], "swapOptions": [{"label": "swaper", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.04", "chainSymbol": "ETH", "_WETH": "******************************************", "rewardOptions": [{"label": "WETH", "value": "******************************************"}, {"label": "axlUSDC", "value": "******************************************"}], "currencyOptions": [{"label": "WETH", "value": "******************************************"}, {"label": "axlUSDC", "value": "******************************************"}], "swapOptions": [{"label": "BASEswap", "value": "0x327Df1E6de05895d2ab08513aaDD9313Fe505d86"}, {"label": "Leetswap", "value": "0xd3Ea3BC1F5A3F881bD6cE9761cbA5A0833a5d737"}]}], "719": ["https://puppyscan.shib.io/address/", {"ModeAddress": "******************************************", "Fee": "0.0", "chainSymbol": "TBONE"}], "109": ["https://www.shibariumscan.io/address/", {"ModeAddress": "******************************************", "Fee": "0.0", "chainSymbol": "TBONE"}]}