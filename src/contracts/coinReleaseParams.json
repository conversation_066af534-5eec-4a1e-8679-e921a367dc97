{"97": ["https://testnet.bscscan.com/address/", {"ModeAddress": "******************************************", "Fee": "0.05", "chainSymbol": "TBNB"}, {"ModeAddress": "******************************************", "original_Mode": "******************************************", "Fee": "0.1", "chainSymbol": "TBNB", "_WETH": "******************************************", "rewardOptions": [{"label": "BNB", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}, {"label": "CAKE", "value": "******************************************"}], "currencyOptions": [{"label": "BNB", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}, {"label": "CAKE", "value": "******************************************"}], "swapOptions": [{"label": "Pancake", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.11", "chainSymbol": "TBNB", "_WETH": "******************************************", "rewardOptions": [{"label": "BNB", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}, {"label": "CAKE", "value": "******************************************"}], "currencyOptions": [{"label": "BNB", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}, {"label": "CAKE", "value": "******************************************"}], "swapOptions": [{"label": "Pancake", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.2", "chainSymbol": "TBNB", "_WETH": "******************************************", "rewardOptions": [{"label": "BNB", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}, {"label": "CAKE", "value": "******************************************"}], "currencyOptions": [{"label": "BNB", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}, {"label": "CAKE", "value": "******************************************"}], "swapOptions": [{"label": "Pancake", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.15", "chainSymbol": "TBNB", "_WETH": "******************************************", "currencyOptions": [{"label": "BNB", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}, {"label": "CAKE", "value": "******************************************"}], "swapOptions": [{"label": "Pancake", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.06", "chainSymbol": "TBNB", "_WETH": "******************************************", "currencyOptions": [{"label": "BNB", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}, {"label": "CAKE", "value": "******************************************"}], "swapOptions": [{"label": "Pancake", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.16", "chainSymbol": "TBNB", "_WETH": "******************************************", "currencyOptions": [{"label": "BNB", "value": "******************************************"}], "swapOptions": [{"label": "Pancake", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.14", "chainSymbol": "TBNB", "_WETH": "******************************************", "rewardOptions": [{"label": "USDT", "value": "******************************************"}, {"label": "CAKE", "value": "******************************************"}], "currencyOptions": [{"label": "BNB", "value": "******************************************"}], "swapOptions": [{"label": "Pancake", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.13", "chainSymbol": "TBNB"}, {"ModeAddress": "******************************************", "Fee": "0.12", "chainSymbol": "TBNB", "_WETH": "******************************************", "rewardOptions": [{"label": "BNB", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}, {"label": "CAKE", "value": "******************************************"}], "currencyOptions": [{"label": "BNB", "value": "******************************************"}], "swapOptions": [{"label": "Pancake", "value": "******************************************"}]}], "56": ["https://bscscan.com/address/", {"ModeAddress": "******************************************", "Fee": "0.05", "chainSymbol": "BNB"}, {"ModeAddress": "******************************************", "Fee": "0.1", "chainSymbol": "BNB", "_WETH": "******************************************", "rewardOptions": [{"label": "BNB", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}, {"label": "BUSD", "value": "******************************************"}], "currencyOptions": [{"label": "BNB", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}, {"label": "BUSD", "value": "******************************************"}], "swapOptions": [{"label": "Pancake", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.11", "chainSymbol": "BNB", "_WETH": "******************************************", "rewardOptions": [{"label": "BNB", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}, {"label": "BUSD", "value": "******************************************"}], "currencyOptions": [{"label": "BNB", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}, {"label": "BUSD", "value": "******************************************"}], "swapOptions": [{"label": "Pancake", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.2", "chainSymbol": "BNB", "_WETH": "******************************************", "rewardOptions": [{"label": "BNB", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}, {"label": "BUSD", "value": "******************************************"}], "currencyOptions": [{"label": "BNB", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}, {"label": "BUSD", "value": "******************************************"}], "swapOptions": [{"label": "Pancake", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.15", "chainSymbol": "BNB", "_WETH": "******************************************", "rewardOptions": [{"label": "BNB", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}, {"label": "BUSD", "value": "******************************************"}], "currencyOptions": [{"label": "BNB", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}, {"label": "BUSD", "value": "******************************************"}], "swapOptions": [{"label": "Pancake", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.06", "chainSymbol": "BNB", "_WETH": "******************************************", "rewardOptions": [{"label": "BNB", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}, {"label": "BUSD", "value": "******************************************"}], "currencyOptions": [{"label": "BNB", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}, {"label": "BUSD", "value": "******************************************"}], "swapOptions": [{"label": "Pancake", "value": "******************************************"}]}, {"ModeAddress": "0x61C173e560Fc366ADad7B93ef3676a6dc016deeD", "Fee": "0.16", "chainSymbol": "BNB", "_WETH": "******************************************", "rewardOptions": [{"label": "BNB", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}, {"label": "BUSD", "value": "******************************************"}], "currencyOptions": [{"label": "BNB", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}, {"label": "BUSD", "value": "******************************************"}], "swapOptions": [{"label": "Pancake", "value": "******************************************"}]}, {"ModeAddress": "0xb30403d5AD5Da2fa442c03f443844A8Ef0cab146", "Fee": "0.14", "chainSymbol": "BNB", "_WETH": "******************************************", "rewardOptions": [{"label": "USDT", "value": "******************************************"}, {"label": "BUSD", "value": "******************************************"}], "currencyOptions": [{"label": "BNB", "value": "******************************************"}], "swapOptions": [{"label": "Pancake", "value": "******************************************"}]}, {"ModeAddress": "0x14523cEFC32E86AD2F9cD6637687C9404DFA18Ab", "Fee": "0.13", "chainSymbol": "BNB"}, {"ModeAddress": "0x1D10Aef61C61bD06b0fa2a01125426874b1b5177", "Fee": "0.12", "chainSymbol": "BNB", "_WETH": "******************************************", "rewardOptions": [{"label": "BNB", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}, {"label": "BUSD", "value": "******************************************"}], "currencyOptions": [{"label": "BNB", "value": "******************************************"}], "swapOptions": [{"label": "Pancake", "value": "******************************************"}]}], "1": ["https://etherscan.io/address/", {"ModeAddress": "******************************************", "Fee": "0.03", "chainSymbol": "ETH"}, {"ModeAddress": "******************************************", "Fee": "0.0", "chainSymbol": "ETH", "_WETH": "******************************************", "rewardOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "currencyOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "swapOptions": [{"label": "Uniswap", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.0", "chainSymbol": "ETH", "_WETH": "******************************************", "rewardOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "currencyOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "swapOptions": [{"label": "Uniswap", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.0", "chainSymbol": "ETH", "_WETH": "******************************************", "rewardOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "currencyOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "swapOptions": [{"label": "Uniswap", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.0", "chainSymbol": "ETH", "_WETH": "******************************************", "rewardOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "currencyOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "swapOptions": [{"label": "Uniswap", "value": "******************************************"}]}], "5": ["https://goerli.etherscan.io/address/", {"ModeAddress": "******************************************", "Fee": "0.0", "chainSymbol": "ETH"}, {"ModeAddress": "******************************************", "Fee": "0.0", "chainSymbol": "ETH", "_WETH": "******************************************", "rewardOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "currencyOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "swapOptions": [{"label": "Uniswap", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.0", "chainSymbol": "ETH", "_WETH": "******************************************", "rewardOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "currencyOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "swapOptions": [{"label": "Uniswap", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.0", "chainSymbol": "ETH", "_WETH": "******************************************", "rewardOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "currencyOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "swapOptions": [{"label": "Uniswap", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.0", "chainSymbol": "ETH", "_WETH": "******************************************", "rewardOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "currencyOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "swapOptions": [{"label": "Uniswap", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.0", "chainSymbol": "ETH", "_WETH": "******************************************", "rewardOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "currencyOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "swapOptions": [{"label": "Uniswap", "value": "******************************************"}]}], "250": ["https://ftmscan.com/token/", {"ModeAddress": "******************************************", "Fee": "0.0", "chainSymbol": "FTM"}, {"ModeAddress": "******************************************", "Fee": "0.0", "chainSymbol": "FTM", "_WETH": "******************************************", "rewardOptions": [{"label": "FTM", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "currencyOptions": [{"label": "FTM", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "swapOptions": [{"label": "SpookySwap", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.0", "chainSymbol": "FTM", "_WETH": "******************************************", "rewardOptions": [{"label": "FTM", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "currencyOptions": [{"label": "FTM", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "swapOptions": [{"label": "SpookySwap", "value": "******************************************"}]}], "42161": ["https://arbiscan.io/token/", {"ModeAddress": "******************************************", "Fee": "0.03", "chainSymbol": "ETH"}, {"ModeAddress": "******************************************", "Fee": "0.0", "chainSymbol": "ETH", "_WETH": "******************************************", "rewardOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "currencyOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "swapOptions": [{"label": "Sushiswap", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.0", "chainSymbol": "ETH", "_WETH": "******************************************", "rewardOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "currencyOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "swapOptions": [{"label": "Sushiswap", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.0", "chainSymbol": "ETH", "_WETH": "******************************************", "rewardOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "currencyOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "swapOptions": [{"label": "Sushiswap", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.0", "chainSymbol": "ETH", "_WETH": "******************************************", "rewardOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "currencyOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "swapOptions": [{"label": "Sushiswap", "value": "******************************************"}]}, {"ModeAddress": "0x2C7ce2CdE86AD9EdDFA553192611208A3773Fbb3", "Fee": "0.04", "chainSymbol": "ETH", "_WETH": "******************************************", "rewardOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "currencyOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "swapOptions": [{"label": "Sushiswap", "value": "******************************************"}]}], "1116": ["https://scan.coredao.org/token/", {"ModeAddress": "******************************************", "Fee": "10.0", "chainSymbol": "CORE"}, {"ModeAddress": "******************************************", "Fee": "20.0", "chainSymbol": "CORE", "_WETH": "******************************************", "rewardOptions": [{"label": "CORE", "value": "******************************************"}, {"label": "USDT(45F1)", "value": "******************************************"}, {"label": "USDT(5817)", "value": "******************************************"}], "currencyOptions": [{"label": "CORE", "value": "******************************************"}, {"label": "USDT(45F1)", "value": "******************************************"}, {"label": "USDT(5817)", "value": "******************************************"}], "swapOptions": [{"label": "IceCreamswap", "value": "******************************************"}, {"label": "LFGswap", "value": "******************************************"}, {"label": "Archerswap", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "30.0", "chainSymbol": "CORE", "_WETH": "******************************************", "rewardOptions": [{"label": "CORE", "value": "******************************************"}, {"label": "USDT(45F1)", "value": "******************************************"}, {"label": "USDT(5817)", "value": "******************************************"}], "currencyOptions": [{"label": "CORE", "value": "******************************************"}, {"label": "USDT(45F1)", "value": "******************************************"}, {"label": "USDT(5817)", "value": "******************************************"}], "swapOptions": [{"label": "IceCreamswap", "value": "******************************************"}, {"label": "LFGswap", "value": "******************************************"}, {"label": "Archerswap", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "40.0", "chainSymbol": "CORE", "_WETH": "******************************************", "rewardOptions": [{"label": "CORE", "value": "******************************************"}, {"label": "USDT(45F1)", "value": "******************************************"}, {"label": "USDT(5817)", "value": "******************************************"}], "currencyOptions": [{"label": "CORE", "value": "******************************************"}, {"label": "USDT(45F1)", "value": "******************************************"}, {"label": "USDT(5817)", "value": "******************************************"}], "swapOptions": [{"label": "IceCreamswap", "value": "******************************************"}, {"label": "LFGswap", "value": "******************************************"}, {"label": "Archerswap", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "30.0", "chainSymbol": "CORE", "_WETH": "******************************************", "rewardOptions": [{"label": "CORE", "value": "******************************************"}, {"label": "USDT(45F1)", "value": "******************************************"}, {"label": "USDT(5817)", "value": "******************************************"}], "currencyOptions": [{"label": "CORE", "value": "******************************************"}, {"label": "USDT(45F1)", "value": "******************************************"}, {"label": "USDT(5817)", "value": "******************************************"}], "swapOptions": [{"label": "IceCreamswap", "value": "******************************************"}, {"label": "LFGswap", "value": "******************************************"}, {"label": "Archerswap", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "15.0", "chainSymbol": "CORE", "_WETH": "******************************************", "rewardOptions": [{"label": "CORE", "value": "******************************************"}, {"label": "USDT(45F1)", "value": "******************************************"}, {"label": "USDT(5817)", "value": "******************************************"}], "currencyOptions": [{"label": "CORE", "value": "******************************************"}, {"label": "USDT(45F1)", "value": "******************************************"}, {"label": "USDT(5817)", "value": "******************************************"}], "swapOptions": [{"label": "IceCreamswap", "value": "******************************************"}, {"label": "LFGswap", "value": "******************************************"}, {"label": "Archerswap", "value": "******************************************"}]}], "66": ["https://www.okx.com/cn/explorer/oktc/token/", {"ModeAddress": "******************************************", "Fee": "0.5", "chainSymbol": "OKT"}, {"ModeAddress": "******************************************", "Fee": "1.0", "chainSymbol": "OKT", "_WETH": "******************************************", "rewardOptions": [{"label": "OKT", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "currencyOptions": [{"label": "OKT", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "swapOptions": [{"label": "OKCSwap", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "1.0", "chainSymbol": "OKT", "_WETH": "******************************************", "rewardOptions": [{"label": "OKT", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "currencyOptions": [{"label": "OKT", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "swapOptions": [{"label": "OKCSwap", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "2.0", "chainSymbol": "OKT", "_WETH": "******************************************", "rewardOptions": [{"label": "OKT", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "currencyOptions": [{"label": "OKT", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "swapOptions": [{"label": "OKCSwap", "value": "******************************************"}]}], "280": ["https://explorer.zksync.io/address/", {"ModeAddress": "******************************************", "Fee": "0.0", "chainSymbol": "ETH"}, {"ModeAddress": "******************************************", "Fee": "0.0", "chainSymbol": "ETH", "_WETH": "******************************************", "rewardOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDC", "value": "******************************************"}], "currencyOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDC", "value": "******************************************"}], "swapOptions": [{"label": "Syncswap", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.0", "chainSymbol": "ETH", "_WETH": "******************************************", "rewardOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDC", "value": "******************************************"}], "currencyOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDC", "value": "******************************************"}], "swapOptions": [{"label": "Quickswap", "value": "******************************************"}]}], "4002": ["https://testnet.ftmscan.com/token/", {"ModeAddress": "******************************************", "Fee": "0.0", "chainSymbol": "TFTM"}, {"ModeAddress": "******************************************", "Fee": "0.0", "chainSymbol": "TFTM", "_WETH": "******************************************", "rewardOptions": [{"label": "FTM", "value": "******************************************"}, {"label": "BLOQ", "value": "******************************************"}], "currencyOptions": [{"label": "FTM", "value": "******************************************"}, {"label": "BLOQ", "value": "******************************************"}], "swapOptions": [{"label": "SpookySwap", "value": "0xa6AD18C2aC47803E193F75c3677b14BF19B94883"}]}, {"ModeAddress": "******************************************", "Fee": "0.0", "chainSymbol": "TFTM", "_WETH": "******************************************", "rewardOptions": [{"label": "FTM", "value": "******************************************"}, {"label": "BLOQ", "value": "******************************************"}], "currencyOptions": [{"label": "FTM", "value": "******************************************"}, {"label": "BLOQ", "value": "******************************************"}], "swapOptions": [{"label": "SpookySwap", "value": "0xa6AD18C2aC47803E193F75c3677b14BF19B94883"}]}, {"ModeAddress": "******************************************", "Fee": "0.0", "chainSymbol": "TFTM", "_WETH": "******************************************", "rewardOptions": [{"label": "FTM", "value": "******************************************"}, {"label": "BLOQ", "value": "******************************************"}], "currencyOptions": [{"label": "FTM", "value": "******************************************"}, {"label": "BLOQ", "value": "******************************************"}], "swapOptions": [{"label": "SpookySwap", "value": "0xa6AD18C2aC47803E193F75c3677b14BF19B94883"}]}], "1115": ["https://scan.test.btcs.network/address/", {"ModeAddress": "******************************************", "Fee": "0.01", "chainSymbol": "CORE"}, {"ModeAddress": "0xD375cC284c190A2D49f70F696719258Bc8D47b1B", "Fee": "0.00", "chainSymbol": "CORE", "_WETH": "******************************************", "rewardOptions": [{"label": "CORE", "value": "******************************************"}, {"label": "MIGO", "value": "******************************************"}], "currencyOptions": [{"label": "CORE", "value": "******************************************"}, {"label": "MIGO", "value": "******************************************"}], "swapOptions": [{"label": "migoswap", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.0", "chainSymbol": "CORE", "_WETH": "******************************************", "rewardOptions": [{"label": "CORE", "value": "******************************************"}, {"label": "MIGO", "value": "******************************************"}], "currencyOptions": [{"label": "CORE", "value": "******************************************"}, {"label": "MIGO", "value": "******************************************"}], "swapOptions": [{"label": "migoswap", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.0", "chainSymbol": "CORE", "_WETH": "******************************************", "rewardOptions": [{"label": "CORE", "value": "******************************************"}, {"label": "MIGO", "value": "******************************************"}], "currencyOptions": [{"label": "CORE", "value": "******************************************"}, {"label": "MIGO", "value": "******************************************"}], "swapOptions": [{"label": "migoswap", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.0", "chainSymbol": "CORE", "_WETH": "******************************************", "rewardOptions": [{"label": "CORE", "value": "******************************************"}, {"label": "MIGO", "value": "******************************************"}], "currencyOptions": [{"label": "CORE", "value": "******************************************"}, {"label": "MIGO", "value": "******************************************"}], "swapOptions": [{"label": "migoswap", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.0", "chainSymbol": "CORE", "_WETH": "******************************************", "rewardOptions": [{"label": "CORE", "value": "******************************************"}, {"label": "MIGO", "value": "******************************************"}], "currencyOptions": [{"label": "CORE", "value": "******************************************"}, {"label": "MIGO", "value": "******************************************"}], "swapOptions": [{"label": "migoswap", "value": "******************************************"}]}], "84532": ["https://sepolia.basescan.org/token/", {"ModeAddress": "******************************************", "Fee": "0.01", "chainSymbol": "BaseETH"}, {"ModeAddress": "******************************************", "Fee": "0.02", "chainSymbol": "BaseETH", "_WETH": "******************************************", "rewardOptions": [{"label": "WETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "currencyOptions": [{"label": "WETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "swapOptions": [{"label": "Uniswap", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.022", "chainSymbol": "BaseETH", "_WETH": "******************************************", "rewardOptions": [{"label": "WETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "currencyOptions": [{"label": "WETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "swapOptions": [{"label": "Uniswap", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.04", "chainSymbol": "BaseETH", "_WETH": "******************************************", "rewardOptions": [{"label": "WETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "currencyOptions": [{"label": "WETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "swapOptions": [{"label": "Uniswap", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.03", "chainSymbol": "BaseETH", "_WETH": "******************************************", "currencyOptions": [{"label": "WETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "swapOptions": [{"label": "Uniswap", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.015", "chainSymbol": "BaseETH", "_WETH": "******************************************", "currencyOptions": [{"label": "WETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "swapOptions": [{"label": "Uniswap", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.036", "chainSymbol": "BaseETH", "_WETH": "******************************************", "currencyOptions": [{"label": "WETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "swapOptions": [{"label": "Uniswap", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.035", "chainSymbol": "BaseETH", "_WETH": "******************************************", "rewardOptions": [{"label": "WETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "currencyOptions": [{"label": "WETH", "value": "******************************************"}], "swapOptions": [{"label": "Uniswap", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.028", "chainSymbol": "TBNB"}, {"ModeAddress": "******************************************", "Fee": "0.025", "chainSymbol": "BaseETH", "_WETH": "******************************************", "rewardOptions": [{"label": "WETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "currencyOptions": [{"label": "WETH", "value": "******************************************"}, {"label": "USDT", "value": "******************************************"}], "swapOptions": [{"label": "Uniswap", "value": "******************************************"}]}], "8453": ["https://basescan.org/token/", {"ModeAddress": "******************************************", "Fee": "0.01", "chainSymbol": "ETH"}, {"ModeAddress": "******************************************", "Fee": "0.02", "chainSymbol": "ETH", "_WETH": "******************************************", "rewardOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDC", "value": "******************************************"}], "currencyOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDC", "value": "******************************************"}], "swapOptions": [{"label": "Uniswap", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.022", "chainSymbol": "ETH", "_WETH": "******************************************", "rewardOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDC", "value": "******************************************"}], "currencyOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDC", "value": "******************************************"}], "swapOptions": [{"label": "Uniswap", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.04", "chainSymbol": "ETH", "_WETH": "******************************************", "rewardOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDC", "value": "******************************************"}], "currencyOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDC", "value": "******************************************"}], "swapOptions": [{"label": "Uniswap", "value": "******************************************"}]}, {"ModeAddress": "0xBf9d794279f0c0Db264C76463851ba848b6d6357", "Fee": "0.03", "chainSymbol": "ETH", "_WETH": "******************************************", "currencyOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDC", "value": "******************************************"}], "swapOptions": [{"label": "Uniswap", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.015", "chainSymbol": "ETH", "_WETH": "******************************************", "currencyOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDC", "value": "******************************************"}], "swapOptions": [{"label": "Uniswap", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.036", "chainSymbol": "ETH", "_WETH": "******************************************", "currencyOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDC", "value": "******************************************"}], "swapOptions": [{"label": "Uniswap", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.035", "chainSymbol": "ETH", "_WETH": "******************************************", "rewardOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDC", "value": "******************************************"}], "currencyOptions": [{"label": "ETH", "value": "******************************************"}], "swapOptions": [{"label": "Uniswap", "value": "******************************************"}]}, {"ModeAddress": "******************************************", "Fee": "0.028", "chainSymbol": "ETH"}, {"ModeAddress": "******************************************", "Fee": "0.025", "chainSymbol": "ETH", "_WETH": "******************************************", "rewardOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDC", "value": "******************************************"}], "currencyOptions": [{"label": "ETH", "value": "******************************************"}, {"label": "USDC", "value": "******************************************"}], "swapOptions": [{"label": "Uniswap", "value": "******************************************"}]}], "137": ["https://polygonscan.com/address/", {"ModeAddress": "******************************************", "Fee": "20.0", "chainSymbol": "<PERSON><PERSON>"}], "80002": ["https://amoy.polygonscan.com/address/", {"ModeAddress": "******************************************", "Fee": "0.0", "chainSymbol": "TMatic"}], "81457": ["https://blastexplorer.io/address/", {"ModeAddress": "******************************************", "Fee": "0.03", "chainSymbol": "ETH"}], "168587773": ["https://sepolia.blastexplorer.io/address/", {"ModeAddress": "******************************************", "Fee": "0.0", "chainSymbol": "TETH"}]}