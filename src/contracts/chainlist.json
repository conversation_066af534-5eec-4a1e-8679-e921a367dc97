{"ChainList": [1, 56, 42161, 8453, 137, 1116, 66, 81457, 97, 80002, 168587773, 421613, 280, 84532, 5, 8453, 719, 109], "ChainInfo": [{"hexId": "0x1", "Id": 1, "Name": "ETH Mainnet", "rpcUrls": "https://mainnet.infura.io/v3/", "blockExplorerUrls": "https://etherscan.io", "CurrencyName": "ETH", "CurrencySymbol": "ETH"}, {"hexId": "0x38", "Id": 56, "Name": "BSC Mainnet", "rpcUrls": "https://bsc.blockrazor.xyz/1938091667949101056", "blockExplorerUrls": "https://bscscan.com", "CurrencyName": "BNB", "CurrencySymbol": "BNB"}, {"hexId": "0xA4B1", "Id": 42161, "Name": "Arbitrum One", "rpcUrls": "https://rpc.ankr.com/arbitrum", "blockExplorerUrls": "https://arbiscan.io", "CurrencyName": "ETH", "CurrencySymbol": "ETH"}, {"hexId": "0x2105", "Id": 8453, "Name": "Base", "rpcUrls": "https://base.blockpi.network/v1/rpc/public/", "blockExplorerUrls": "https://basescan.org/", "CurrencyName": "ETH", "CurrencySymbol": "ETH"}, {"hexId": "0x89", "Id": 137, "Name": "Polygon Mainnet", "rpcUrls": "https://polygon.llamarpc.com", "blockExplorerUrls": "https://polygonscan.com", "CurrencyName": "MATIC", "CurrencySymbol": "MATIC"}, {"hexId": "0x45C", "Id": 1116, "Name": "Core Mainnet", "rpcUrls": "https://rpc.coredao.org", "blockExplorerUrls": "https://scan.coredao.org/", "CurrencyName": "CORE", "CurrencySymbol": "CORE"}, {"hexId": "0x42", "Id": 66, "Name": "OKX Mainnet", "rpcUrls": "https://exchainrpc.okex.org", "blockExplorerUrls": "https://www.okx.com/cn/explorer/okc", "CurrencyName": "OKT", "CurrencySymbol": "OKT"}, {"hexId": "0x13e31", "Id": 81457, "Name": "Blast", "rpcUrls": "https://rpc.blast.io", "blockExplorerUrls": "https://blastexplorer.io/", "CurrencyName": "ETH", "CurrencySymbol": "ETH"}, {"hexId": "0x61", "Id": 97, "Name": "BSC Testnet", "rpcUrls": "https://data-seed-prebsc-2-s1.binance.org:8545", "blockExplorerUrls": "https://testnet.bscscan.com", "CurrencyName": "TBNB", "CurrencySymbol": "TBNB"}, {"hexId": "0x13882", "Id": 80002, "Name": "Polygon Amoy", "rpcUrls": "https://polygon-amoy.drpc.org/", "blockExplorerUrls": "https://amoy.polygonscan.com/", "CurrencyName": "TMatic", "CurrencySymbol": "TMatic"}, {"hexId": "0x14a34", "Id": 84532, "Name": "Base Sepolia Testnet", "rpcUrls": "https://base-sepolia.blockpi.network/v1/rpc/public", "blockExplorerUrls": "https://sepolia.basescan.org/", "CurrencyName": "TETH", "CurrencySymbol": "TETH"}, {"hexId": "0x45B", "Id": 1115, "Name": "CORE Test", "rpcUrls": "https://rpc.test.btcs.network", "blockExplorerUrls": "https://scan.test.btcs.network/", "CurrencyName": "TCORE", "CurrencySymbol": "TCORE"}, {"hexId": "0x118", "Id": 280, "Name": "ZK Era Test", "rpcUrls": "https://testnet.era.zksync.dev", "blockExplorerUrls": "https://goerli.explorer.zksync.io/", "CurrencyName": "ETH", "CurrencySymbol": "ETH"}]}