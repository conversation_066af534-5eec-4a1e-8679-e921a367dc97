import Vue from "vue";
import VueI18n from "vue-i18n";
import zhCN from "@/locales/zh-CN.js";
import enUS from "@/locales/en-US.js";
import elementZh from "element-ui/lib/locale/lang/zh-CN";
import elementEn from "element-ui/lib/locale/lang/en";
Vue.use(VueI18n);
const i18n = new VueI18n({
  locale: "zh-CN", // 默认语言
  messages: {
    "zh-CN": {
      ...zhCN,
      ...elementZh,
    },
    "en-US": {
      ...enUS,
      ...elementEn,
    },
  },
});
export default i18n;
