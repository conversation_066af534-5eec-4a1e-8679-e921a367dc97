import { BigNumber, ethers } from "ethers";
// import abiBQB from '@/abi/BQBToken.json'
import Chainlist from "@/contracts/chainlist.json";

const state = {
  provider: null,
  signer: null,
  address: null,
  chainId: null,
  chainName: null,
};

const mutations = {
  set_account(state) {
    // console.log(
    //   'no provider',
    //   new ethers.providers.Web3Provider(window.ethereum)
    // )
    state.provider = new ethers.providers.Web3Provider(window.ethereum);
    // state.signer = state.provider.getSinger()
    state.provider.getNetwork().then((network) => {
      const chain_index = Chainlist.ChainList.indexOf(network.chainId);
      if (chain_index != -1) {
        state.chainId = network.chainId;
        state.chainName = Chainlist.ChainInfo[chain_index].Name;
      } else {
        state.chainId = network.chainId;
        state.chainName = "Unknown";
      }
    });
  },
};

const actions = {
  async connect({ commit }) {
    return new Promise((resolve, reject) => {
      window.ethereum
        .request({
          method: "eth_requestAccounts",
        })
        .then((accounts) => {
          if (accounts.length == 0) {
            console.log("No connected");
          } else {
            state.address = ethers.utils.getAddress(accounts[0]);

            resolve(state.address);
          }
        })
        .catch((err) => {
          if (err.code === 4001) {
            console.log("Please connect to MetaMask.");
          } else {
            console.error(err);
          }
          reject(err);
        });
    });
  },

  disconnect({ state }) {
    state.address = null;
  },
  switchChain({ state }, chainArgs) {
    window.ethereum
      .request({
        method: "wallet_switchEthereumChain",
        params: [{ chainId: chainArgs.hexId }],
      })
      .then(() => {
        console.log("开始换链");
        state.chainName = chainArgs.Name;
        state.chainID = chainArgs.Id;
        console.log(state.chainID);
        location.reload();
      })
      .catch((error) => {
        console.log("error:添加链", error);
        if (error.code == 4902 || error.code == -32603) {
          window.ethereum
            .request({
              method: "wallet_addEthereumChain",
              params: [
                {
                  chainId: chainArgs.hexId,
                  chainName: chainArgs.Name,
                  rpcUrls: [chainArgs.rpcUrls],
                  blockExplorerUrls: [chainArgs.blockExplorerUrls],
                  nativeCurrency: {
                    name: chainArgs.CurrencyName,
                    symbol: chainArgs.CurrencySymbol,
                    decimals: 18,
                  },
                },
              ],
            })
            .then(() => {
              console.log("添加链");
              state.chainName = chainArgs.Name;
              state.chainID = chainArgs.Id;
            })
            .catch(() => {
              console.log("添加链错误");
              this.$message({
                type: "danger",
                message: "添加链错误,请删除已有链后重试!",
              });
            });
        } else {
          console.log("error:不知道哪错了");
        }
      });
  },
  //使用方法web3Provider.on('accountsChanged', accounts => {})
  addWalletListener({ commit }) {
    state.provider.on("accountsChanged", (accounts) => {
      console.log("accountsChanged", accounts);
    });
  },
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
};
