<template>
  <div class="main1">
    <div class="inner1">
      <el-alert
        v-show="true"
        type="error"
        :description="$t('contractCheck.tips')"
        center
        :closable="false"
      >
      </el-alert>
      <el-header class="header">
        <h2 class="h-title">{{$t('contractCheck.合约检测')}}</h2>
      </el-header>
      <el-main class="main1">
        <div>
          <el-input
            :placeholder="$t('contractCheck.请输入合约地址')"
            v-model="address"
            class="input-with-select"
          >
            <el-select v-model="select" slot="prepend" :placeholder="$t('contractCheck.请选择')">
              <el-option label="BSC" value="56"></el-option>
              <el-option label="Ethereum" value="1"></el-option>
              <el-option label="Arbitrum" value="42161"></el-option>
              <!-- <el-option label="Base" value="8453"></el-option>
              <el-option label="波场" value="tron"></el-option>
              <el-option label="OK链" value="66"></el-option>
              <el-option label="Polygon" value="137"></el-option>
              <el-option label="zkSync" value="324"></el-option>
              <el-option label="Fantom" value="250"></el-option> -->
            </el-select>
            <el-button
              slot="append"
              icon="el-icon-search"
              @click="fetchData"
              :loading="loading"
            ></el-button>
          </el-input>
        </div>
        <div v-show="show">
          <el-card class="card-1">
            <h3>{{$t("contractCheck.基本信息")}}</h3>
            <el-row
              v-for="(item, key) in baseInfo"
              :key="key"
              class="baseInfo"
              style="padding: 4px; margin-left: 10px"
            >
              <el-col :span="10">
                <div class="name_label">
                  {{ item.name }}
                </div>
              </el-col>
              <!-- <el-col :span="8">
                <el-tag v-if="item.value" size="small">{{ item.value }}</el-tag>
              </el-col> -->
              <el-col :span="14">
                <div class="grid-content bg-purple" style="text-align: right">
                  <el-tag v-if="item.tags" size="small" :type="item.color">{{
                    item.tags
                  }}</el-tag>
                </div>
              </el-col>
            </el-row>
          </el-card>
          <el-card class="overlay card-1">
            <div class="ownerBox" v-if="give_up_owner">
              <div>
                <svg
                  viewBox="0 0 1024 1024"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="#000000"
                  style="width: 50px"
                >
                  <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                  <g
                    id="SVGRepo_tracerCarrier"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  ></g>
                  <g id="SVGRepo_iconCarrier">
                    <path
                      fill="#67c23a"
                      d="M512 64a448 448 0 1 1 0 896 448 448 0 0 1 0-896zm-55.808 536.384-99.52-99.584a38.4 38.4 0 1 0-54.336 54.336l126.72 126.72a38.272 38.272 0 0 0 54.336 0l262.4-262.464a38.4 38.4 0 1 0-54.272-54.336L456.192 600.384z"
                    ></path>
                  </g>
                </svg>
              </div>
              <p class="owner-p">{{$t("contractCheck.已放弃所有权")}}</p>
            </div>
            <div class="ownerBox" v-if="!give_up_owner">
              <div>
                <svg
                  viewBox="0 0 512 512"
                  version="1.1"
                  xmlns="http://www.w3.org/2000/svg"
                  xmlns:xlink="http://www.w3.org/1999/xlink"
                  fill="#000000"
                  style="width: 50px"
                >
                  <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                  <g
                    id="SVGRepo_tracerCarrier"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  ></g>
                  <g id="SVGRepo_iconCarrier">
                    <title>error-filled</title>
                    <g
                      id="Page-1"
                      stroke="none"
                      stroke-width="1"
                      fill="none"
                      fill-rule="evenodd"
                    >
                      <g
                        id="add"
                        fill="#de1010f7"
                        transform="translate(42.666667, 42.666667)"
                      >
                        <path
                          d="M213.333333,3.55271368e-14 C331.136,3.55271368e-14 426.666667,95.5306667 426.666667,213.333333 C426.666667,331.136 331.136,426.666667 213.333333,426.666667 C95.5306667,426.666667 3.55271368e-14,331.136 3.55271368e-14,213.333333 C3.55271368e-14,95.5306667 95.5306667,3.55271368e-14 213.333333,3.55271368e-14 Z M262.250667,134.250667 L213.333333,183.168 L164.416,134.250667 L134.250667,164.416 L183.168,213.333333 L134.250667,262.250667 L164.416,292.416 L213.333333,243.498667 L262.250667,292.416 L292.416,262.250667 L243.498667,213.333333 L292.416,164.416 L262.250667,134.250667 Z"
                          id="Combined-Shape"
                        ></path>
                      </g>
                    </g>
                  </g>
                </svg>
              </div>
              <p class="owner-p-n">{{$t("contractCheck.未放弃所有权")}}</p>
            </div>
            <h3>{{$t("contractCheck.风险分析")}}</h3>
            <el-row
              v-for="(item, key) in securityInfo"
              :key="key"
              class="baseInfo"
              style="margin-left: 10px"
            >
              <el-col :span="12" style="padding-left: 4px"
                ><div class="grid-content bg-purple">
                  {{ item.name }}
                </div></el-col
              >
              <el-col :span="12" style="padding-right: 4px"
                ><div
                  class="grid-content bg-purple-light"
                  style="text-align: right"
                >
                  <el-tag
                    v-if="item.value"
                    size="small"
                    style="margin-bottom: 5px"
                    :type="item.color"
                    >{{ item.value }}</el-tag
                  >
                </div></el-col
              >
            </el-row>
          </el-card>
          <el-card class="card-1">
            <h3>
              <span>{{$t("contractCheck.做市信息")}}({{ data["lp_holder_count"] }})</span>
            </h3>
            <el-row
              v-for="(item, key) in marketInfo"
              :key="key"
              class="baseInfo"
            >
              <el-col :span="2" style="display: grid; place-items: center"
                ><span class="rank">{{ key + 1 }}</span></el-col
              >
              <el-col :span="22">
                <el-row style="margin-top: 4px; margin-bottom: 3px">
                  <el-tag size="small" class="address-color"
                    >{{ item.address }}
                  </el-tag>
                </el-row>
                <el-row style="margin-top: 4px; margin-bottom: 3px">
                  <el-tag size="small"
                    >{{ (item.balance * 1).toFixed(2) }} Lp</el-tag
                  >
                  <el-tag size="small" type="info" style="margin-left: 2px"
                    >{{$t("contractCheck.比例")}}:{{ (item.percent * 100).toFixed(2) }} %</el-tag
                  >
                  <el-tag
                    size="small"
                    v-if="item.tag"
                    style="margin-left: 2px"
                    type="success"
                    >{{ item.tag }}</el-tag
                  >
                  <i
                    v-if="item.is_locked"
                    style="margin-left: 2px"
                    class="el-icon-lock lock-color"
                  ></i>
                </el-row>
                <el-row style="margin-top: 4px; margin-bottom: 3px">
                  <el-col :span="24">
                    <div v-for="(i, k) in item.locked_detail" :key="k">
                      <el-tag size="small" type="danger"
                        >{{$t("contractCheck.解锁日期")}}： {{ i.end_time.substring(0, 10) }}</el-tag
                      >
                      <el-tag size="small" type="danger"
                        >{{$t("contractCheck.数量")}}：{{ (i.amount * 100).toFixed(2) }}</el-tag
                      >
                    </div>
                  </el-col>
                </el-row>
              </el-col>
            </el-row>
          </el-card>
          <el-card class="card-1">
            <h3>
              <el-row>
                <el-col
                  ><span
                    >{{ $t('contractCheck.持币信息',{hold:data["holder_count"],p:holder_top10_persent}) }}</span
                  ></el-col
                >
              </el-row>
            </h3>

            <el-row
              v-for="(item, key) in holderInfo"
              :key="key"
              class="baseInfo"
            >
              <el-col :span="2" style="display: grid; place-items: center"
                ><span class="rank">{{ key + 1 }}</span></el-col
              >
              <el-col :span="22">
                <el-row style="margin-top: 4px; margin-bottom: 3px">
                  <el-tag size="small" class="address-color"
                    >{{ item.address }}
                  </el-tag>
                </el-row>
                <el-row style="margin-top: 4px; margin-bottom: 3px">
                  <el-tag size="small" v-if="item.tag" type="success">{{
                    item.tag
                  }}</el-tag>
                </el-row>
                <el-row style="margin-top: 4px; margin-bottom: 3px">
                  <el-tag size="small" type="info"
                    >{{ (item.balance * 1).toFixed(2) }}
                    {{ data["token_symbol"] }}</el-tag
                  >

                  <el-tag size="small" type="info" style="margin-left: 2px"
                    >比例:{{ (item.percent * 100).toFixed(2) }} %</el-tag
                  >
                </el-row>
              </el-col>
              <!-- <el-col :span="5"
                ><div
                  class="grid-content bg-purple-light"
                  style="text-align: right;font-size: 10px;"
                >
                  {{ (item.balance * 1).toFixed(2) }} {{ data["token_symbol"] }}
                </div></el-col
              > -->
            </el-row>
          </el-card>
        </div>
      </el-main>
    </div>
  </div>
</template>
<script>
import axios from "axios";
import testData from "./devdata.json";
import {
  handleBaseInfo,
  handleHolderInfo,
  handleMarketInfo,
  handleSecurityInfo,
  getHolderTop10Persent,
} from "./label.js";
import { ethers } from "ethers";
export default {
  data() {
    return {
      loading: false,
      address: "",
      select: "56",
      scanApiKey: {
        1: "KHB45GZ3M8A252H6FNGE9ADCBP3JNNPUQG",
        56: "29IGMPAZ4AR7MHZVQMSKDSCC2MKW96UKYK",
      },
      //   data: testData["result"]["******************************************"],
      data: testData["result"]["******************************************"],
      baseInfo: null,
      securityInfo: null,
      poolInfo: null,
      marketInfo: null,
      holderInfo: null,
      holder_top10_persent: 0,
      give_up_owner: false,
      show: false,
      chain: {
        1: { name: "Ethereum", scan: "https://etherscan.io/address/" },
        56: { name: "BSC", scan: "https://bscscan.com/address/" },
        42161: { name: "Arbitrum", scan: "https://arbiscan.io/address/" },
      },
    };
  },
  methods: {
    getUrl() {
      // console.log(this.select);
      if (this.select != "tron") {
        const isValid = ethers.utils.isAddress(this.address);
        if (!isValid) {
          this.$notify({
            title: this.$t("contractCheck.错误"),
            message: this.$t("contractCheck.请输入正确的合约地址"),
            type: "error",
          });
          return;
        }
        this.address = this.address.toLowerCase();
      }

      let url =
        "https://api.gopluslabs.io/api/v1/token_security/" + this.select;
      url += "?contract_addresses=";
      url += this.address;
      // console.log(url);
      return url;
    },
    async getCreaterHash() {
      const SCAN_API = {
        56: "https://api.bscscan.com/api?module=contract&action=getcontractcreation&contractaddresses=",
        1: "https://api.etherscan.io/api?module=contract&action=getcontractcreation&contractaddresses=",
        // 42161:"https://api.bscscan.com/api?module=contract&action=getcontractcreation&contractaddresses="
      };
      const SCAN_API_HASH = {
        56: "https://api.bscscan.com/api?module=account&action=txlistinternal&txhash=",
        1: "https://api.etherscan.io/api?module=account&action=txlistinternal&txhash=",
      };
      const url = SCAN_API[this.select];
      if (url) {
        return false;
        // let req_url = url + this.address +'&apikey=' + this.scanApiKey[this.select]
        // const rsp = await axios.get(req_url)
        // // console.log(rsp.data.result[0]['txHash'])
        // const creatHash = rsp.data.result[0]['txHash']
        // let req_hash_url = SCAN_API_HASH[this.select] + creatHash + '&apikey=' + this.scanApiKey[this.select]
        // const rsp_hash = await axios.get(req_hash_url)
        // console.log(rsp_hash.data)
        // if(!rsp_hash.data.result){
        //   return false
        // }
        // if(rsp_hash.data.result[0]['to'] == "******************************************"){
        //   return true
        // }
        // return false
      }
      return false;
    },
    async fetchData() {
      this.loading = true;
      this.show = false;
      const url = this.getUrl();
      const isPandaTool = await this.getCreaterHash();
      axios
        .get(url)
        .then((response) => {
          // 处理响应数据
          this.loading = false;
          if (Object.keys(response.data["result"]).length > 0) {
            this.data = response.data["result"][this.address];
            this.data["is_panda_tool"] = isPandaTool;
            console.log(this.data);
            this.baseInfo = handleBaseInfo(this.data);
            this.marketInfo = handleMarketInfo(this.data);
            this.securityInfo = handleSecurityInfo(this.data);
            this.holderInfo = handleHolderInfo(this.data);
            this.holder_top10_persent = getHolderTop10Persent(this.data);
            if (
              this.data["owner_address"] ==
                "******************************************" ||
              this.data["owner_address"] ==
                "******************************************" ||
              this.data["owner_address"] == ""
            ) {
              this.give_up_owner = true;
            }
            this.show = true;
          } else {
            this.$notify({
              title: "错误",
              message: "无数据，请确认您选择的链是否正确",
              type: "error",
            });
          }
        })
        .catch((error) => {
          // 处理错误
          this.loading = false;
          console.error(error);
        });
      this.loading = false;
    },
  },
};
</script>
<style>
.el-select .el-input {
  width: 80px;
}
.input-with-select .el-input-group__prepend {
  background-color: #fff;
}
.baseInfo {
  border-bottom: 1px solid #c0c4cc;
  margin-top: 5px;
  margin-bottom: 6px;
}
.lock-color {
  color: #f56c6c;
}
.address-color {
  color: #606266;
}
.overlay {
  position: relative;
}
.ownerBox {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  text-align: center;
  z-index: 99;
}
.owner-success {
  color: #67c23a;
}
.owner-p {
  color: #67c23a;
  font-weight: 800;
  margin-block-start: 0;
}
.owner-p-n {
  color: #de1010f7;
  font-weight: 800;
  margin-block-start: 0;
}
.overlay::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgb(0 0 0 / 7%); /* 蒙板的颜色和透明度 */
}
.el-card__body {
  padding: 5px !important;
}
.h-title {
  margin-block-start: 0;
  margin-block-end: 0;
}
.p-tips {
  color: red;
}
@media screen and (max-width: 500px) {
  .app-container {
    padding: 10px;

    padding-bottom: 60px;
    background-color: #eeeeee;
  }
  .inner1 {
    background-color: #ffff;
    padding: 10px;
  }

  .header {
    text-align: center;
    margin-top: 20px;
    font-size: larger;
  }
  .main1 {
    /* text-align: center; */
    padding-left: 1%;
    padding-right: 1%;
    padding-bottom: 100px;

    overflow-x: hidden;
  }
  .flex-container {
    display: flex;
    flex-direction: row;
    margin-top: 10px;
  }
  .el-dialog {
    width: 90% !important;
  }
  .el-form {
    label-position: "top";
  }
}
@media screen and (min-width: 500px) {
  .app-container {
    padding: 20px;

    padding-bottom: 60px;
    background-color: #eeeeee;
  }
  .inner1 {
    background-color: #ffff;
    padding: 40px;
  }
  .header {
    text-align: center;
    margin-top: 20px;
    font-size: larger;
  }
  .main1 {
    /* text-align: center; */
    padding-left: 15%;
    padding-right: 15%;
    padding-bottom: 100px;
  }
  .flex-container {
    display: flex;
    flex-direction: row;
    margin-top: 20px;
  }
  .el-dialog {
    width: 50%;
  }
  .el-form {
    label-position: "left";
    label-width: "auto";
  }
}
.rank {
  padding: 3px;
  border-radius: 13px;
  width: 25px;
  height: 25px;
  text-align: center;
  margin-top: 41%;
  background-color: #3f9effbd;
  display: block;
}
.card-1 {
  margin-top: 10px;
}
</style>
