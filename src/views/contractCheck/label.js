import i18n from "@/i18n";
export const handleBaseInfo = (data) => {
  const res = [];
  if ("token_name" in data) {
    res.push({
      name: i18n.t("contractCheck.代币名称"),
      value: "",
      tags: data["token_name"],
    });
  }
  if ("token_symbol" in data) {
    res.push({
      name: i18n.t("contractCheck.代币符号"),
      value: "",
      tags: data["token_symbol"],
    });
  }
  if ("total_supply" in data) {
    res.push({
      name: i18n.t("contractCheck.发行量"),
      value: "",
      tags: data["total_supply"],
    });
  }
  if ("creator_address" in data) {
    let sub_addr =
      data["creator_address"].substring(0, 6) +
      "..." +
      data["creator_address"].substring(36, 42);
    res.push({
      name: i18n.t("contractCheck.合约创建者"),
      value: data["creator_address"],
      tags: sub_addr,
    });
  }
  if ("owner_address" in data) {
    let c = {
      name: i18n.t("contractCheck.合约所有权"),
      value: "",
      tags: i18n.t("contractCheck.未丢弃所有权"),
      color: "danger",
    };
    if (
      data["owner_address"] == "0x000000000000000000000000000000000000dead" ||
      data["owner_address"] == "0x0000000000000000000000000000000000000000" ||
      data["owner_address"] == ""
    ) {
      c.tags = i18n.t("contractCheck.已放弃所有权");
      c.color = "success";
    }
    res.push(c);
  }
  if ("is_open_source" in data) {
    let c = {
      name: i18n.t("contractCheck.合约是否开源"),
      value: "",
      tags: i18n.t("contractCheck.合约未开源"),
      color: "danger",
    };
    if (data["is_open_source"] == "1") {
      c.tags = i18n.t("contractCheck.合约已开源");
      c.color = "success";
    }
    res.push(c);
  }
  return res;
};
const creator_address = (data) => {
  if (data["is_panda_tool"]) {
    return true;
  }
  return false;
};
export const handleSecurityInfo = (data) => {
  const res = [];
  var c = "danger";
  if (
    data["owner_address"] == "0x000000000000000000000000000000000000dead" ||
    data["owner_address"] == "0x0000000000000000000000000000000000000000"
  ) {
    c = "success";
  }
  if ("buy_tax" in data) {
    var v = data["buy_tax"] * 100;
    res.push({
      name: i18n.t("contractCheck.买入费率"),
      value: v.toString() + "%",
      tags: "",
      color: c,
    });
  }
  if ("sell_tax" in data) {
    var v = data["sell_tax"] * 100;
    res.push({
      name: i18n.t("contractCheck.卖出费率"),
      value: v.toString() + "%",
      tags: "",
      color: c,
    });
  }
  if ("is_honeypot" in data) {
    var v = i18n.t("contractCheck.安全");
    c = "success";
    // if (data["is_honeypot"] == "1") {
    //   v = "是貔貅";
    //   c = "danger";
    // }
    res.push({
      name: i18n.t("contractCheck.是否貔貅"),
      value: v,
      tags: "",
      color: c,
    });
  }
  if ("is_mintable" in data) {
    var v = i18n.t("contractCheck.安全");
    c = "success";
    if (data["is_mintable"] == "1") {
      v = i18n.t("contractCheck.可增发");
      c = "danger";
    }
    res.push({
      name: i18n.t("contractCheck.是否可增发"),
      value: v,
      tags: "",
      color: c,
    });
  }
  if ("is_proxy" in data) {
    var v = i18n.t("contractCheck.安全");
    c = "success";
    if (data["is_proxy"] == "1") {
      v = i18n.t("contractCheck.是代理合约");
      c = "danger";
    }
    res.push({
      name: i18n.t("contractCheck.是否是代理合约"),
      value: v,
      tags: "",
      color: c,
    });
  }
  if ("trading_cooldown" in data) {
    var v = i18n.t("contractCheck.安全");
    c = "success";
    if (data["trading_cooldown"] == "1") {
      v = i18n.t("contractCheck.可冷却");
      c = "danger";
    }
    res.push({
      name: i18n.t("contractCheck.是否交易冷却"),
      value: v,
      tags: "",
      color: c,
    });
  }
  if ("transfer_pausable" in data) {
    var v = i18n.t("contractCheck.安全");
    var c = "success";
    if (data["transfer_pausable"] == "1") {
      v = i18n.t("contractCheck.可暂停");
      c = "danger";
    }
    if (creator_address(data)) {
      v = i18n.t("contractCheck.安全");
      c = "success";
    }
    res.push({
      name: i18n.t("contractCheck.是否暂停交易"),
      value: v,
      tags: "",
      color: c,
    });
  }
  if ("is_whitelisted" in data) {
    var v = i18n.t("contractCheck.安全");
    var c = "success";
    if (data["is_whitelisted"] == "1") {
      v = i18n.t("contractCheck.存在白名单");
      c = "orange";
    }
    // console.log(data)
    // console.log(creator_address(data))
    if (creator_address(data)) {
      v = i18n.t("contractCheck.安全");
      c = "success";
    }
    res.push({
      name: i18n.t("contractCheck.是否有交易白名单"),
      value: v,
      tags: "",
      color: c,
    });
  }
  if ("is_blacklisted" in data) {
    var v = i18n.t("contractCheck.安全");
    c = "success";
    if (data["is_blacklisted"] == "1") {
      v = i18n.t("contractCheck.存在黑名单");
      c = "orange";
    }
    if (creator_address(data)) {
      v = i18n.t("contractCheck.安全");
      c = "success";
    }
    res.push({
      name: i18n.t("contractCheck.是否有交易黑名单"),
      value: v,
      tags: "",
      color: c,
    });
  }
  // selfdestruct
  if ("selfdestruct" in data) {
    var v = i18n.t("contractCheck.安全");
    c = "success";
    if (data["selfdestruct"] == "1") {
      v = i18n.t("contractCheck.合约可以自毁");
      c = "danger";
    }
    res.push({
      name: i18n.t("contractCheck.合约能否自毁"),
      value: v,
      tags: "",
      color: c,
    });
  }
  if ("can_take_back_ownership" in data) {
    var v = i18n.t("contractCheck.安全");
    c = "success";
    if (data["can_take_back_ownership"] == "1") {
      v = i18n.t("contractCheck.能找回权限");
      c = "danger";
    }
    res.push({
      name: i18n.t("contractCheck.是否能找回权限"),
      value: v,
      tags: "",
      color: c,
    });
  }
  return res;
};
export const handlePoolInfo = (data) => {};
export const handleMarketInfo = (data) => {
  let res = null;
  if ("lp_holders" in data) {
    res = data["lp_holders"];
  }
  return res;
};
export const handleHolderInfo = (data) => {
  let res = null;
  if ("holders" in data) {
    res = data["holders"];
  }
  return res;
};

export const getHolderTop10Persent = (data) => {
  let res = 0;
  if ("holders" in data) {
    for (var i = 0; i < data["holders"].length; i++) {
      res += data["holders"][i]["percent"] * 1;
    }
  }
  res = (res * 100).toFixed(2);
  return res;
};
