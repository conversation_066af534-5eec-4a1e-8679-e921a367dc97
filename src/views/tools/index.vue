<template>
  <div class="app-container">
    <div class="inner">
      <el-alert
        v-show="support"
        :title="$t('tools.暂不支持此链')"
        type="error"
        :description="$t('tools.联系定制')"
        center
        :closable="false"
        show-icon
      >
      </el-alert>
      <el-header class="header" style="margin-bottom: 25px">
        <p>{{$t("tools.流动性修复工具")}}</p>
        <p style="font-size: 14px; font-weight: 300">
          {{$t("tools.说明")}}
        </p>
      </el-header>
      <el-main class="main">
        <el-form>
          <el-form-item :label="$t('tools.选择交易所')">
            <el-select
              v-model="selectSwap"
              @change="getSwap"
              :placeholder="$t('tools.请选择')"
            >
              <el-option
                v-for="item in swapOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
                <span style="float: left">{{ item.label }}</span>
              </el-option>
            </el-select>
            <el-input
              v-if="otherSwap"
              v-model="routerAddress"
              placeholder="0x..."
              style="margin-top: 10px"
            />
          </el-form-item>
          <el-form-item :label="$t('tools.V2池子地址')">
            <el-input v-model="poolAddress" :placeholder="$t('tools.请输入池子地址')" />
          </el-form-item>
          <el-row type="flex" justify="space-around">
            <el-button type="primary" @click="checkPool">{{$t('tools.查询池子')}}</el-button>
          </el-row>

          <el-form-item :label="$t('tools.池子代币数量')">
            <el-row>
              <el-tooltip placement="top" effect="light">
                <div style="font-size: 14px; font-weight: 300" slot="content">
                  {{$t('tools.池子代币数量说明')}}<br /><br />
                  {{$t('tools.请先查询池子获取池子内代币信息')}}<br />
                </div>
                <i class="el-icon-warning-outline" />
              </el-tooltip>
            </el-row>
            <el-row :gutter="20">
              <el-col :xs="4" :sm="3" style="margin-top: 10px"
                >{{ getName0 }}:</el-col
              >

              <el-col :xs="20" :sm="9" style="margin-top: 10px">
                <el-input
                  v-model="token0Num"
                  disabled
                  placeholder="0"
                ></el-input>
              </el-col>

              <el-col :xs="4" :sm="3" style="margin-top: 10px"
                >{{ getName1 }}:</el-col
              >

              <el-col :xs="20" :sm="9" style="margin-top: 10px">
                <el-input
                  v-model="token1Num"
                  disabled
                  placeholder="0"
                ></el-input>
              </el-col>
            </el-row>
          </el-form-item>
          <el-form-item :label="$t('tools.目标价格')">
            <el-row>
              <el-tooltip placement="top" effect="light">
                <div style="font-size: 14px; font-weight: 300" slot="content">
                  {{$t('tools.目标价格说明')}}<br /><br />
                  {{$t('tools.需添加超过池子内的代币数量才可自由修改价格')}}<br /><br />
                </div>

                <i class="el-icon-warning-outline" />
              </el-tooltip>
            </el-row>
            <el-row style="margin-top: 5px">
              <el-col :xs="4" :sm="3" style="margin-top: 10px"
                >{{ getName0 }}:</el-col
              >

              <el-col :xs="20" :sm="8" style="margin-top: 10px">
                <el-input
                  v-model="target0Num"
                  oninput="value=value.replace(/[^0-9.]/g,'')"
                  placeholder="0.000000000000000001"
                >
                </el-input>
              </el-col>
              <el-col
                :xs="24"
                :sm="1"
                style="
                  margin-top: 10px;
                  font-size: 24px;
                  font-weight: 600;
                  margin-right: 10px;
                "
                >==</el-col
              >
              <el-col :xs="4" :sm="3" style="margin-top: 10px"
                >{{ getName1 }}:</el-col
              >

              <el-col :xs="20" :sm="8" style="margin-top: 10px">
                <el-input
                  v-model="target1Num"
                  oninput="value=value.replace(/[^0-9.]/g,'')"
                  placeholder="0.000000000000000001"
                >
                </el-input>
              </el-col>
            </el-row>
          </el-form-item>
          <el-row type="flex" justify="space-around">
            <el-button type="primary" @click="computAddNum" plain size="mini"
              >{{$t('tools.核算加池数量')}}</el-button
            >
          </el-row>
          <el-form-item :label="$t('tools.加池数量')">
            <el-row>
              <el-tooltip placement="top" effect="light">
                <div style="font-size: 14px; font-weight: 300" slot="content">
                  {{$t('tools.加池数量说明')}}<br /><br />
                  {{$t('tools.加池数量包括转入池子的数量和真正添加LP的数量')}}<br /><br />
                  {{$t('tools.转入池子的代币无法获得LP通证')}}<br /><br />
                  {{$t('tools.默认的加池数量为0')}}<br /><br />
                  {{$t('tools.超过的部分将转入池子用于调整价格')}}<br /><br />
                </div>
                <i class="el-icon-warning-outline" />
              </el-tooltip>
            </el-row>
            <el-row style="margin-top: 5px">
              <el-col :xs="4" :sm="3" style="margin-top: 10px"
                >{{ getName0 }}:</el-col
              >

              <el-col :xs="20" :sm="9" style="margin-top: 10px">
                <el-input v-model="addToken0Numshow" disabled placeholder="0">
                </el-input>
              </el-col>
              <el-col :xs="4" :sm="3" style="margin-top: 10px"
                >{{ getName1 }}:</el-col
              >

              <el-col :xs="20" :sm="9" style="margin-top: 10px">
                <el-input v-model="addToken1Numshow" disabled placeholder="0">
                </el-input>
              </el-col>
            </el-row>
            <p></p>
            <!-- <p style="font-size:12px;font-weight:300;margin-top:-10px;margin-bottom:-15px;">
            绑定推荐关系需要上级向下级转账一定数额的代币,当下级回转后,视为绑定成功
          </p>
          <p style="font-size:12px;font-weight:300;margin-top:-10px;margin-bottom:-15px;">
            例:默认最小余额为0,上级可转账任意给下级,下级回转任意数量,最小余额可在控制台设置
          </p> -->
          </el-form-item>
          <el-form-item :label="$t('tools.授权')">
            <el-row>
              <el-tooltip placement="top" effect="light">
                <div style="font-size: 14px; font-weight: 300" slot="content">
                  {{$t('tools.授权说明')}}<br /><br />

                  {{$t('tools.修复方法需使用合约完成授权选择默认值即可')}}<br /><br />

                  {{$t('tools.需先将代币打入合约再由合约在一次交易中完成转入池子和加池的所有步奏')}}<br /><br />

                  {{$t('tools.否则攻击者可能提取出手动打入的代币')}}<br /><br />

                  {{$t('tools.造成资金损失')}}<br />
                </div>
                <i class="el-icon-warning-outline" />
              </el-tooltip>
            </el-row>
            <el-row type="flex" justify="space-around">
              <el-col :xs="8" :sm="4" style="margin-top: 10px">
                <el-button
                  v-if="approve0Loading"
                  :loading="true"
                  type="primary"
                  plain
                  @click="approve0"
                  size="medium"
                  >{{$t('tools.正在授权')}}{{ getName0 }}</el-button
                >
                <el-button
                  v-else-if="approved0"
                  type="primary"
                  plain
                  disabled
                  size="medium"
                  >{{ getName0 }}{{$t('tools.已授权')}}</el-button
                >
                <el-button
                  v-else
                  type="primary"
                  plain
                  @click="approve0"
                  size="medium"
                  >{{$t('tools.授权')}}{{ getName0 }}</el-button
                >
              </el-col>
              <el-col :xs="8" :sm="4" style="margin-top: 10px">
                <el-button
                  v-if="approve1Loading"
                  :loading="true"
                  type="primary"
                  plain
                  size="medium"
                  >{{$t('tools.正在授权')}}{{ getName1 }}</el-button
                >
                <el-button
                  v-else-if="approved1"
                  type="primary"
                  plain
                  disabled
                  size="medium"
                  >{{ getName1 }}{{$t('tools.已授权')}}</el-button
                >
                <el-button
                  v-else
                  type="primary"
                  plain
                  @click="approve1"
                  size="medium"
                  >{{$t('tools.授权')}}{{ getName1 }}</el-button
                >
              </el-col>
            </el-row>
          </el-form-item>
        </el-form>
        <p style="font-size: 14px; font-weight: 300">
          {{$t('tools.有限制交易功能的代币')}}
        </p>
        <p style="font-size: 14px; font-weight: 300">
          {{$t('tools.修复合约')}}:{{ this.LPfixAddress }}
          <i
            class="el-icon-copy-document"
            style="
              margin-left: 10px;
              font-size: 12px;
              font-weight: 400;
              color: #409eff;
            "
            @click="copy_str(LPfixAddress)"
            >{{$t('tools.复制')}}</i
          >
        </p>
        <el-row type="flex" justify="space-around" style="margin-top: 20px">
          <el-button v-if="addLoading" :loading="true" type="primary"
            >{{$t('tools.请稍后')}}</el-button
          >
          <el-button v-else type="primary" @click="checkParamsAdd"
            >{{$t('tools.确认修复')}}</el-button
          >
        </el-row>
      </el-main>
    </div>
  </div>
</template>

<script>
import CoinData from "@/contracts/PancakeERC20.json";
import LPfixABI from "@/contracts/LPfixABI.json";
import lpfixParam from "@/contracts/lpfixParam.json";
import chainParams from "@/contracts/coinReleaseParams.json";
import store from "@/store";
const { ethers, BigNumber } = require("ethers");
const supportChain = [56, 97];
export default {
  name: "Dashboard",
  data() {
    return {
      CoinData,
      LPfixABI,
      lpfixParam,
      chainParams,
      store,
      support: null,
      supportChain,
      LPfixAddress: null,

      selectSwap: null,
      swapOptions: [],
      otherSwap: null,
      routerAddress: null,

      poolAddress: null,
      poolABI: null,

      Amount0In: 1000000000,
      Amount1In: null,
      approvedAmount: 1000000000000,
      token0Name: null,
      token1Name: null,
      token0Num: null,
      token1Num: null,
      reserve0: null,
      reserve1: null,
      reserves0Diff: null,
      reserves1Diff: null,

      target0Num: null,
      target1Num: null,
      target0NumReal: null,
      target1NumReal: null,

      addToken0Num: null,
      addToken0Numshow: null,
      addToken1Num: null,
      addToken1Numshow: null,

      approved0: false,
      approved1: false,
      approve0Loading: false,
      approve1Loading: false,

      dialogVisible: false,
      addLoading: false,
    };
  },
  created() {
    let Inval = setInterval(() => {
      if (store.state.user.chainId) {
        window.clearInterval(Inval);
        const chainId = store.state.user.chainId;
        if (supportChain.includes(chainId)) {
          this.support = false;
        } else {
          this.support = true;
        }
        const selectChainParams = chainParams[chainId][6];
        this.LPfixAddress = ethers.utils.getAddress(
          lpfixParam[chainId][0].lpfixAddress
        );
        this.poolABI = CoinData.abi;
        for (let i = 0; i < selectChainParams.swapOptions.length; i++) {
          var temAddress = ethers.utils.getAddress(
            selectChainParams.swapOptions[i].value
          );
          var temParam = {
            label: selectChainParams.swapOptions[i].label,
            value: temAddress,
          };
          this.swapOptions.push(temParam);
        }
      }
    }, 1000);
  },
  computed: {
    getName0() {
      if (this.token0Name == null) {
        return "???";
      } else {
        return this.token0Name;
      }
    },
    getName1() {
      if (this.token1Name == null) {
        return "???";
      } else {
        return this.token1Name;
      }
    },
  },
  methods: {
    getSwap(selectSwap) {
      console.log("selectSwap", selectSwap);
      if (selectSwap == 1) {
        this.otherSwap = true;
        this.routerAddress = null;
      } else {
        this.otherSwap = false;
        this.routerAddress = selectSwap;
      }
    },

    checkPool() {
      console.log(this.poolAddress);
      let isPool = ethers.utils.isAddress(this.poolAddress);
      if (!isPool) {
        this.$message({
          type: "error",
          message: this.$t('tools.池子地址不正确')+"!",
        });
      } else {
        this.poolAddress = ethers.utils.getAddress(this.poolAddress);
        this.getBasicParams();
      }
    },
    async getBasicParams() {
      const provider = store.state.user.provider;
      const signer = provider.getSigner();
      const poolContract = new ethers.Contract(
        this.poolAddress,
        this.poolABI,
        signer
      );
      let token0 = await poolContract.token0();
      this.token0 = ethers.utils.getAddress(token0);

      const token0Contract = new ethers.Contract(
        this.token0,
        this.poolABI,
        signer
      );
      token0Contract
        .symbol()
        .then((symbol) => {
          this.token0Name = symbol;
          console.log("0name", this.token0Name);
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t('tools.获取token0简称错误')+"!",
          });
        });

      let token1 = await poolContract.token1();
      this.token1 = ethers.utils.getAddress(token1);
      const token1Contract = new ethers.Contract(
        this.token1,
        this.poolABI,
        signer
      );
      token1Contract
        .symbol()
        .then((symbol) => {
          this.token1Name = symbol;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t('tools.获取token0简称错误')+"!",
          });
        });

      poolContract
        .getReserves()
        .then((backdata) => {
          // console.log(backdata)
          // (parseFloat(new Number(backdata._reserve0.toString())/(10**18)).toFixed(18)).toString()
          this.reserve0 = backdata._reserve0;
          this.reserve1 = backdata._reserve1;
          this.token0Num = parseFloat(
            new Number(backdata._reserve0.toString()) / 10 ** 18
          )
            .toFixed(18)
            .toString();
          this.token1Num = parseFloat(
            new Number(backdata._reserve1.toString()) / 10 ** 18
          )
            .toFixed(18)
            .toString();
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t('tools.获取池子内代币数量错误')+"!",
          });
        });
    },
    checkTargetNum(targetValue) {
      var amountArray = targetValue.toString().split(".");
      if (amountArray.length == 1) {
        let bigTargetValue = BigNumber.from(amountArray[0]).mul(
          BigNumber.from(10).pow(18)
        );
        console.log(bigTargetValue);
        return bigTargetValue;
      } else if (amountArray.length == 2) {
        var intPart = BigNumber.from(amountArray[0]).mul(
          BigNumber.from(10).pow(18)
        );
        var floatPart = BigNumber.from(amountArray[1]).mul(
          BigNumber.from(10).pow(18 - amountArray[1].length)
        );
        let bigTargetValue = intPart.add(floatPart);
        console.log(bigTargetValue);
        return bigTargetValue;
      } else {
        this.$message({
          type: "danger",
          message: this.$t('tools.请输入正确的数字格式')+"!",
        });
        return 0;
      }
    },

    computAddNum() {
      this.target0NumReal = this.checkTargetNum(this.target0Num);
      this.target1NumReal = this.checkTargetNum(this.target1Num);

      this.reserves0Diff = this.target0NumReal.sub(
        BigNumber.from(this.reserve0)
      );
      this.reserves1Diff = this.target1NumReal.sub(
        BigNumber.from(this.reserve1)
      );
      if (this.reserves0Diff.lt(0)) {
        this.$message({
          type: "danger",
          message: this.$t('tools.数量错误请提高第一个代币的数量')+"",
        });
      }
      if (this.reserves1Diff.lt(0)) {
        this.$message({
          type: "danger",
          message: this.$t('tools.数量错误请提高第二个代币的数量')+"!",
        });
      }
      if (this.reserves0Diff.gte(0) && this.reserves1Diff.gte(0)) {
        this.Amount0In = BigNumber.from(this.Amount0In);
        this.Amount1In = BigNumber.from(this.Amount0In)
          .mul(BigNumber.from(this.target1NumReal))
          .div(BigNumber.from(this.target0NumReal));
        this.addToken0Num = this.reserves0Diff.add(this.Amount0In);
        this.addToken0Numshow = parseFloat(
          new Number(this.addToken0Num.toString()) / 10 ** 18
        )
          .toFixed(18)
          .toString();
        this.addToken1Num = this.reserves1Diff.add(this.Amount1In);
        this.addToken1Numshow = parseFloat(
          new Number(this.addToken1Num.toString()) / 10 ** 18
        )
          .toFixed(18)
          .toString();
        console.log("reserves0Diff", this.reserves0Diff);
        console.log("reserves1Diff", this.reserves1Diff);
        console.log("Amount0In", this.Amount0In);
        console.log("Amount1In", this.Amount1In);
      }
    },
    approve0() {
      this.approve0Loading = true;
      this.approve(this.token0)
        .then((rs) => {
          this.approved0 = true;
          this.approve0Loading = false;
        })
        .catch((error) => {
          console.log("错误!", error);
          this.$message({
            type: "danger",
            message: this.$t('tools.流动性修复工具')+"授权失败,请重试!",
          });
        });
    },
    approve1() {
      this.approve1Loading = true;
      this.approve(this.token1)
        .then(() => {
          this.approved1 = true;
          this.approve1Loading = false;
        })
        .catch((error) => {
          console.log("错误!", error);
          this.$message({
            type: "danger",
            message: this.$t('tools.授权失败请重试')+"!",
          });
        });
    },
    async approve(tokenAddress) {
      const abi = [
        "function balanceOf(address owner) view returns (uint256)",
        "function decimals() view returns (uint8)",
        "function symbol() view returns (string)",
        "function transfer(address to, uint amount) returns (bool)",
        "function approve(address spender, uint256 amount) external returns (bool)",
        "function allowance(address owner, address spender) external view returns (uint256)",
      ];
      const provider = store.state.user.provider;
      const signer = provider.getSigner();
      const ContractAddress = ethers.utils.getAddress(tokenAddress);
      const erc20 = new ethers.Contract(ContractAddress, abi, signer);

      var getAllowance = await erc20.allowance(
        store.state.user.address,
        this.LPfixAddress
      );
      getAllowance = ethers.utils.formatEther(getAllowance);

      if (getAllowance >= this.approvedAmount) {
        return true;
      } else {
        var unlimited =
          "115792089237316195423570985008687907853269984665640564039457584007913129639935";
        const tx = await erc20.approve(this.LPfixAddress, unlimited);
        this.$message({
          type: "success",
          message: this.$t('tools.已提交等待区块确认') +"!",
        });
        await tx.wait();
        this.$message({
          type: "success",
          message: this.$t('tools.授权成功')+"!",
        });
        return true;
      }
    },

    async checkParamsAdd() {
      this.addLoading = true;
      const abi = [
        "function balanceOf(address owner) view returns (uint256)",
        "function decimals() view returns (uint8)",
        "function symbol() view returns (string)",
        "function transfer(address to, uint amount) returns (bool)",
        "function approve(address spender, uint256 amount) external returns (bool)",
        "function allowance(address owner, address spender) external view returns (uint256)",
        "function transAndAdd(address router,address LPaddress,address token0,address token1, uint256 trans0, uint256 trans1,uint256 add0,uint256 add1) public",
      ];
      const provider = store.state.user.provider;
      const signer = provider.getSigner();
      const token0Address = ethers.utils.getAddress(this.token0);
      const token0 = new ethers.Contract(token0Address, abi, signer);

      var getAllowance0 = await token0.allowance(
        store.state.user.address,
        this.LPfixAddress
      );
      if (getAllowance0.lt(this.addToken0Num)) {
        this.addLoading = false;
        this.$message({
          type: "danger",
          message: this.$t('tools.token0未授权') +"!",
        });
      }
      const token1Address = ethers.utils.getAddress(this.token0);
      const token1 = new ethers.Contract(token1Address, abi, signer);
      var getAllowance1 = await token1.allowance(
        store.state.user.address,
        this.LPfixAddress
      );
      console.log("getAllowance1", getAllowance1);
      if (getAllowance1.lt(this.addToken1Num)) {
        this.addLoading = false;
        this.$message({
          type: "danger",
          message: this.$t('tools.token1未授权')+"!",
        });
      }
      console.log("reserves0Diff", this.reserves0Diff);
      console.log("reserves1Diff", this.reserves1Diff);
      console.log("Amount0In", this.Amount0In);
      console.log("Amount1In", this.Amount1In);
      this.LPfixAddress = ethers.utils.getAddress(this.LPfixAddress);
      const fixContract = new ethers.Contract(
        this.LPfixAddress,
        LPfixABI,
        signer
      );
      fixContract
        .transAndAdd(
          this.routerAddress,
          this.poolAddress,
          this.token0,
          this.token1,
          this.reserves0Diff,
          this.reserves1Diff,
          this.Amount0In,
          this.Amount1In
        )
        .then((rs) => {
          this.addLoading = false;
          this.$message({
            type: "success",
            message: this.$t('tools.已提交等待区块确认')+"!",
          });

          rs.wait().then(() => {
            this.$message({
              type: "success",
              message: this.$t('tools.修复成功')+"!",
            });
          });
        })
        .catch((error) => {
          console.log("错误!", error);
          this.$message({
            type: "danger",
            message: this.$t('tools.修复失败')+"",
          });
          this.addLoading = false;
        });
    },

    copy_str(text) {
      if (navigator.clipboard) {
        // clipboard api 复制
        navigator.clipboard.writeText(text);
      } else {
        var textarea = document.createElement("textarea");
        document.body.appendChild(textarea);
        // 隐藏此输入框
        textarea.style.position = "fixed";
        textarea.style.clip = "rect(0 0 0 0)";
        textarea.style.top = "10px";
        // 赋值
        textarea.value = text;
        // 选中
        textarea.select();
        // 复制
        document.execCommand("copy", true);
        // 移除输入框
        document.body.removeChild(textarea);
      }
      this.$message({
        type: "success",
        message: this.$t('tools.已复制') +"!",
      });
    },
  },
};
</script>

<style scoped>
@media screen and (max-width: 500px) {
}
@media screen and (min-width: 500px) {
  .el-input {
    width: 80%;
  }
  .el-select {
    width: 80%;
  }
}
</style>
