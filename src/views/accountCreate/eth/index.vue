<template>
  <div class="app-container">
    <div class="inner">
      <el-header class="header">
        <p>{{ $t("accountCreate.批量生产以太坊兼容链钱包") }}</p>
      </el-header>
      <el-main class="main">
        <el-form ref="form" :model="form" label-width="120px">
          <el-form-item
            :label="$t('accountCreate.请输入生成数量')"
            prop="amount"
            :rules="[
              { required: true, message: $t('accountCreate.数量不能为空') },
              { type: 'number', message: $t('accountCreate.数量必须为数字值') },
            ]"
          >
            <el-input v-model.number="form.amount" autocomplete="off" />
          </el-form-item>
        </el-form>
        <div>
          <el-button type="primary" :loading="isLoading" @click="onSubmit">
            {{
              isLoading ? $t("accountCreate.生成中") : $t("accountCreate.生成")
            }}
          </el-button>
          <el-button
            type="primary"
            icon="el-icon-download"
            @click="handleExport"
            :disabled="disable"
            >{{ $t("accountCreate.导出为CSV") }}</el-button
          >
          <!-- <el-table :data="list1" style="width: 100%">
            <el-table-column prop="address" label="公钥" width="180" />
            <el-table-column prop="privateKey" label="私钥" width="180" />
            <el-table-column prop="phrase" label="助记词" />
          </el-table> -->
          <p style="color: red">{{ $t("accountCreate.警告") }}</p>
          <el-table
            :data="list1"
            max-height="500"
            border
            style="width: 100%; margin-top: 10px"
          >
            <el-table-column
              prop="address"
              :label="$t('accountCreate.以太坊地址')"
              width="200"
            >
            </el-table-column>
            <el-table-column
              prop="tronAddress"
              :label="$t('accountCreate.波场地址')"
              width="200"
            >
            </el-table-column>
            <el-table-column
              prop="privateKey"
              :label="$t('accountCreate.私钥')"
              width="200"
            >
            </el-table-column>
            <el-table-column prop="phrase" :label="$t('accountCreate.助记词')">
            </el-table-column>
          </el-table>
        </div>
      </el-main>
    </div>
  </div>
</template>

<script>
import { ethers } from "ethers";
import bs58check from "bs58check";
// import { saveAs } from "file-saver";
// import XLSX from "xlsx";
export default {
  data() {
    return {
      isLoading: false,
      disable: true,
      form: {
        amount: 1,
      },
      list1: [],
    };
  },
  methods: {
    onSubmit() {
      this.isLoading = true;
      this.list1 = [];
      // 执行生成操作，可以是异步操作
      this.generate()
        .then(() => {
          this.isLoading = false;
          this.disable = false;
        })
        .catch(() => {
          this.isLoading = false;
        });

      // 生成完毕，取消 loading 状态
    },
    async generate() {
      const count = this.form.amount;
      for (let i = 0; i < count; i++) {
        // 等待一段时间模拟异步操作

        const randomWallet = ethers.Wallet.createRandom();
        const tronAddressHex = "41" + randomWallet.address.slice(2); // 波场地址以 "41" 开头
        const tronAddressBase58 = bs58check.encode(
          Buffer.from(tronAddressHex, "hex")
        );
        const temp = {
          address: randomWallet.address,
          tronAddress: tronAddressBase58,
          privateKey: randomWallet.privateKey.slice(2),
          phrase: randomWallet.mnemonic.phrase,
          // phrase:""
        };
        this.list1.push(temp);
        await new Promise((resolve) => setTimeout(resolve, 1));
      }
    },
    handleExport() {
      if (this.list1.length == 0) {
        this.$message({
          type: "error",
          message: this.$t("accountCreate.请先生成地址"),
        });
        return;
      }
      const XLSX = require("xlsx");
      // import { saveAs } from "file-saver";
      const saveAs = require("file-saver");
      const data = this.list1;

      const worksheet = XLSX.utils.json_to_sheet(data);

      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, "Sheet1");

      const blob = new Blob(
        [XLSX.write(workbook, { bookType: "csv", type: "array" })],
        { type: "text/csv;charset=UTF-8" }
      );
      saveAs(blob, "data.csv");
    },
  },
};
</script>

<style scoped>
.line {
  text-align: center;
}
@media screen and (max-width: 500px) {
  .main {
    text-align: center;
    padding-left: 0;
    padding-right: 0;
  }
}
@media screen and (min-width: 500px) {
  .header {
    text-align: center;
    margin-top: 20px;
    font-size: larger;
  }
  .main {
    text-align: center;
    padding-left: 10%;
    padding-right: 10%;
  }
}
</style>
