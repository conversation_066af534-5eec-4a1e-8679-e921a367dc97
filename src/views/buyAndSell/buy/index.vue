<template>
  <div class="app-container">
    <div class="inner">
      <el-header class="header">
        <!-- <p>市值管理</p> -->
        <p style="text-align: center; font-size: 30px;">市值管理</p>
      </el-header>
      <el-main class="main">
        <el-form :model="form" label-width="100px" label-position="left">
          <el-form-item label="选择交易所">
            <el-select
              v-model="selectSwap"
              @change="selectSwapFunc"
              placeholder="请选择"
            >
              <el-option
                v-for="(item, k) in swapOptions"
                :key="k"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
            <el-select
              v-model="routerAddress"
              placeholder="请选择"
              v-show="swapSelectSwitch"
            >
              <el-option
                v-for="(item, k) in swapRouters"
                :key="k"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="代币A (输入)">
            <div>
              <el-input
                placeholder="请选择"
                v-model="tokenA"
                class="input-with-select"
              >
                <el-select
                  v-model="tokenA"
                  slot="prepend"
                  placeholder="请选择"
                  @change="selectTokenFuncA"
                >
                  <el-option
                    v-for="(item, k) in swapTokensOptions"
                    :key="k"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
                <el-button
                  slot="append"
                  icon="el-icon-search"
                  class="search"
                  @click="searchA"
                  :loading="loaddingA"
                ></el-button>
              </el-input>
            </div>
          </el-form-item>
          <el-form-item label="代币B (输出)">
            <!-- <el-input
              v-model="tokenB"
              placeholder="请输入目标代币地址"
            /> -->
            <div>
              <el-input
                placeholder="请选择"
                v-model="tokenB"
                class="input-with-select"
              >
                <el-select
                  v-model="tokenB"
                  slot="prepend"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="(item, k) in swapTokensOptions"
                    :key="k"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
                <el-button
                  slot="append"
                  icon="el-icon-search"
                  class="search"
                  @click="searchB"
                  @change="selectTokenFuncB"
                  :loading="loaddingB"
                ></el-button>
              </el-input>
            </div>
            <el-popover
              placement="top-start"
              title="提示"
              width="400"
              trigger="hover"
              content="这里用代币A去兑换代币B，点击交换后则是反向操作。确无误后点击查询池子"
            >
              <el-button
                style="margin-top: 10px"
                type="primary"
                slot="reference"
                @click="exchangeToken"
              >
                交换
              </el-button>
              <el-button
                style="margin-top: 10px"
                type="primary"
                slot="reference"
                @click="GetPool"
                :loading="loadingGetPool"
              >
                查询池子
              </el-button>
            </el-popover>
          </el-form-item>
          <el-form-item>
            <el-table v-show="form._Pool" :data="poolData" style="width: 100%">
              <el-table-column
                prop="poolAddress"
                label="池子地址"
                width="150"
              />
              <el-table-column prop="USDTBalance" :label="tokenASymbol" width="150" />
              <el-table-column prop="tokenBalance" :label="tokenBSymbol" width="150" />
              <el-table-column
                prop="TokenPrice"
                :label="'价格:'+ tokenASymbol"
                width="150"
              />
            </el-table>
          </el-form-item>
          <el-form-item label="钱包私钥">
            <el-input v-model="form.privtKeys" type="textarea" :rows="10" />
            <el-popover
              placement="top-start"
              title="提示"
              width="400"
              trigger="hover"
              content="未限制钱包数量,一般不要超过100个,交易时长过久易导致网络中断.钱包可重复添加,添加多个相同钱包,一次执行则交易多次"
            >
              <el-button
                style="
                  margin-top: 10px;
                  margin-bottom: 10px;
                  margin-right: 10px;
                "
                type="primary"
                @click="GetWallte"
                slot="reference"
                >添加钱包</el-button
              >
            </el-popover>
            <el-button
              style="margin-top: 10px; margin-bottom: 10px"
              type="primary"
              @click="clearWallet"
              >清除钱包</el-button
            >
          </el-form-item>
          <el-form-item>
            <el-table
              v-show="form._Options"
              :data="addressData"
              style="width: 100%"
            >
              <el-table-column prop="address" label="钱包地址" width="200" />
              <el-table-column prop="ETHBalance" label="BNB数量" width="150" />
              <el-table-column
                prop="USDTBalance"
                :label="tokenASymbol + ' 余额'"
                width="150"
              />
              <el-table-column
                prop="tokenBalance"
                :label="tokenBSymbol + ' 余额'"
                width="150"
              />
              <el-table-column prop="isApproved" label="授权" width="120" />
            </el-table>
          </el-form-item>
          <!-- </el-form> -->
          <!-- <el-form v-show="form._Options" :model="form" style = "margin-top:10px;margin-bottom:20px;" label-width="auto" label-position="left" > -->
          <el-form-item v-show="form._Options">
            <el-popover
              placement="top-start"
              title="提示"
              width="400"
              trigger="hover"
              content="批量买入前,请确认钱包有足够BNB支付手续费,每个钱包都需支付其转账所需的手续费。
            批量买入前,请确认钱包已经授权Pancake交易所转出USDT。
            批量授权,依次将钱包授权给Pancake,请注意BNB消耗。
            "
            >
              <el-button
                style="
                  margin-top: 10px;
                  margin-bottom: 10px;
                  margin-right: 10px;
                "
                type="primary"
                @click="CheckWallte"
                slot="reference"
                >查询余额</el-button
              >
            </el-popover>

            <el-button
              style="margin-top: 10px; margin-bottom: 10px"
              type="primary"
              @click="GetApproval"
              >批量授权</el-button
            >
          </el-form-item>
          <el-form-item v-show="form._Options">
            <p>目前价格:{{ nowPrice }}</p>
          </el-form-item>
          <el-form-item v-show="form._Options" label="目标价格">
            <el-input v-model="form.targetPrice" placeholder="targetPrice" />
          </el-form-item>
          <el-form-item v-show="form._Options" label="单笔交易量">
            <el-input v-model="form.tradeNum" placeholder="0.5" />
          </el-form-item>
          <el-form-item v-show="form._Options" label="时间间隔(s)">
            <el-input v-model="form.tradeSeconds" placeholder="1" />
          </el-form-item>
          <el-form-item v-show="form._Options">
            <el-popover
              placement="top-start"
              title="提示"
              width="600"
              trigger="hover"
              content="批量买入前,请确认已经查询过池子信息,否则将无法转账。
            由于pancake需收取0.25%手续费,价格计算会有千分之一的偏差。
            批量授权,依次将钱包授权给Pancake,请注意BNB消耗。
            每笔转账大约需要10秒确认,如需快速买入,建议将时间间隔设置为1s,过低可能导致错误!
            所有钱包买入一次后仍未到达目标价格,再次点击执行即可。
            循环过程中到达目标价格,程序将自动终止并显示结果。
            一笔交易通常10秒内确认结果,并自动添加数据。
            如遇卡死,请检查所有条件是否满足,刷新页面,重新操作即可。
            "
            >
              <el-button
                v-show="form._Options"
                style="
                  margin-top: 10px;
                  margin-bottom: 10px;
                  margin-right: 10px;
                "
                type="primary"
                :loading="loadingTask"
                @click="open"
                slot="reference"
                >{{ loadingTask ? "执行中" : "立即执行" }}</el-button
              >
            </el-popover>

            <el-button
              v-show="form._Options"
              type="primary"
              :disabled="disableCancle"
              @click="cancelTask"
              >取消</el-button
            >
          </el-form-item>
          <el-form-item v-show="form.TxInfo">
            <el-table :data="TxData" style="width: 100%">
              <el-table-column prop="address" label="钱包地址" width="300" />
              <el-table-column prop="TxHash" label="交易ID" width="300" />
              <el-table-column prop="TxStatus" label="交易状态" width="100" />
              <el-table-column
                prop="currentPrice"
                label="最新价格"
                width="200"
              />
            </el-table>
            <p v-show="form.TxInfo">{{ this.form.DoneInfo }}</p>
          </el-form-item>
        </el-form>
      </el-main>
    </div>
    <div style="background-color: #eeeeee; margin-top: 100px">
      <el-divider
        >有任何问题请加入
        <el-link
          icon="el-icon-telegram"
          href="https://t.me/pandatool"
          target="_blank"
          >PandaTool</el-link
        >
        交流群进行反馈</el-divider
      >
    </div>
  </div>
</template>

<script>
import ERC20 from "@/contracts/MostSimple.json";
import PancakeERC20 from "@/contracts/PancakeERC20.json";
import PancakeFactory from "@/contracts/PancakeFactory.json";
import PancakeRouter from "@/contracts/PancakeRouter.json";
import swap from "@/views/buyAndSell/swap.json";
import chainList from "@/contracts/chainlist.json";
import store from "@/store";
const { ethers, utils, BigNumber } = require("ethers");
export default {
  name: "Dashboard",
  data() {
    return {
      selectSwap: [],
      swapOptions: [],
      swapTokensOptions: [],
      swapRouters: [{ label: "v2", value: "" }],
      swapSelectSwitch: false,
      ERC20,
      PancakeERC20,
      PancakeFactory,
      PancakeRouter,
      tokenA: "",
      tokenB: "",
      loaddingA:false,
      loaddingB:false,
      loadingGetPool:false,
      tokenASymbol: "",
      tokenBSymbol: "",
      tokenAdecimals:18,
      tokenBdecimals:18,
      factoryAddress: "",
      routerAddress: "",
      rpc: "",
      isERC20Token: true,
      value: [],
      // options: swapsOptions.options,
      poolData: [
        {
          token0IsU: true,
          poolAddress: "",
          tokenBalance: "",
          USDTBalance: "",
          TokenPrice: "",
        },
      ],
      addressData: [
        {
          address: "",
          prvKey: "",
          ETHBalance: "",
          tokenBalance: "",
          USDTBalance: "",
          isApproved: "",
        },
      ],
      TxData: [],
      form: {
        privtKeys: "",
        tokenAddress: "",
        _Pool: false,
        targetPrice: "1",
        tradeNum: "1",
        tradeSeconds: "1",
        _Options: false,
        TxInfo: false,
        DoneInfo: "运行中……",
      },
      stopTask: false,
      disableCancle: true,
      loadingTask: false,
      nowPrice: 0,
    };
  },

  created() {
    let Inval = setInterval(() => {
      this.swapOptions = swap[store.state.user.chainId];
      // console.log(this.swapOptions);
      for (let i = 1; i < chainList.ChainInfo.length; i++) {
        if (chainList.ChainInfo[i].Id == store.state.user.chainId) {
          this.rpc = chainList.ChainInfo[i].rpcUrls;
        }
      }
      console.log("当前rpc", this.rpc);
      window.clearInterval(Inval);
    }, 500);
  },
  methods: {
    exchangeToken() {
      var temp = this.tokenA;
      this.tokenA = this.tokenB;
      this.tokenB= temp;
    },
    searchA(){
      this.loaddingA = true
      var constractAddress = this.tokenA
      this.getSymbol(constractAddress).then((res)=>{
        this.tokenASymbol = res
        // console.log(res)
        const elementToAdd = {
              label:res,
              symbal: res,
              value: constractAddress
            }
        if (!this.swapTokensOptions.some(obj => obj.value === constractAddress)) {
          this.swapTokensOptions.push(elementToAdd);
          //******************************************
        } 
        this.loaddingA = false
      }).catch((error)=>{
        this.loaddingA = false
        console.log(error)
        this.$message({
            type: "error",
            message: "查询错误,请确保网络正常或者输入正确的地址",
          });
      })
    },
    searchB(){
      this.loaddingB = true
      var constractAddress = this.tokenB
      this.getSymbol(constractAddress).then((res)=>{
        this.tokenASymbol = res
        // console.log(res)
        const elementToAdd = {
          label: res,
          symbal: res,
          value: constractAddress
        }
        if (!this.swapTokensOptions.some(obj => obj.value === constractAddress)) {
          this.swapTokensOptions.push(elementToAdd);
          //******************************************
        } 
        this.loaddingB = false
      }).catch((error)=>{
        this.loaddingB = false
        console.log(error)
        this.$message({
            type: "error",
            message: "查询错误,请确保网络正常或者输入正确的地址",
          });
      })
    },
    async getSymbol (contractAddress){
      const ContractAddress = ethers.utils.getAddress(contractAddress);
      const abi = [
        "function balanceOf(address owner) view returns (uint256)",
        "function decimals() view returns (uint8)",
        "function symbol() view returns (string)",
      ];
      const provider = new ethers.providers.JsonRpcProvider(this.rpc);
      const erc20 = new ethers.Contract(ContractAddress, abi, provider);
      var symbol = await erc20.symbol()
      return symbol
    },
    
    selectSwapFunc() {
      this.swapSelectSwitch = true;
      this.factoryAddress = this.selectSwap.swapParams.factoryAddress;
      this.swapRouters = this.selectSwap.swapParams.routers;
      this.swapTokensOptions = this.selectSwap.tokens;
      this.routerAddress = this.swapRouters[0].value;
    },
    selectTokenFuncA(item) {
      this.isERC20Token = true;
      if (item == this.swapTokensOptions[0].value) {
        this.isERC20Token = false;
        this.tokenASymbol = this.swapTokensOptions[0].symbal
      }
      //console.log(this.isERC20Token)
    },
    selectTokenFuncB(item) {
      this.isERC20Token = true;
      if (item == this.swapTokensOptions[0].value) {
        this.isERC20Token = false;
        this.tokenBSymbol = this.swapTokensOptions[0].symbal
      }
      //console.log(this.isERC20Token)
    },

    GetPool() {
      this.loadingGetPool = true
      this.$message({
        type: "primary",
        message: "查询中,请稍后!",
      });
      this.GetPoolInfo()
        .then(() => {
          this.loadingGetPool = false
          this.$message({
            type: "success",
            message: "查询完成!",
          });
        })
        .catch((err) => {
          this.loadingGetPool = false
          console.log(err);
          this.$message({
            type: "error",
            message: "查询错误,请确认池子已添加或网络连接",
          });
        });
    },
    async GetPoolInfo() {
      this.tokenASymbol = await this.getSymbol(this.tokenA)
      this.tokenBSymbol = await this.getSymbol(this.tokenB)
      this.form._Pool = true;
      const pairABI = this.PancakeERC20.abi;
      const factoryABI = this.PancakeFactory.abi;
      const routerABI = this.PancakeRouter;
      // console.log("rpc", this.rpc);
      // console.log("routerAddress", this.routerAddress);
      // console.log("factoryAddress", this.factoryAddress);
      // console.log("tokenA", this.tokenA);
      // console.log("tokenAddress", this.tokenB);
      const provider = new ethers.providers.JsonRpcProvider(this.rpc);
      const factory = new ethers.Contract(
        this.factoryAddress,
        factoryABI,
        provider
      );
      const pairAddress = await factory.getPair(
        this.tokenA,
        this.tokenB
      );
      if (pairAddress == "******************************************") {
        this.$message({
          type: "error",
          message: "池子查询失败，请确认池子",
        });
        return;
      }
      // console.log("pairAddress", pairAddress);
      this.poolData[0].poolAddress = pairAddress;
      const pool = new ethers.Contract(pairAddress, pairABI, provider);
      const token0 = await pool.token0();
      const token1 = await pool.token1();
     
      const erc20Abi = ["function decimals() view returns (uint8)",]
      const erc20token0 = new ethers.Contract(token0,erc20Abi,provider)
      const erc20token1 = new ethers.Contract(token1,erc20Abi,provider)
      const token0Decimals = await erc20token0.decimals()
      const token1Decimals = await erc20token1.decimals()
      // console.log("token0",tokenADecimals)
      // let router = new ethers.Contract(this.routerAddress, routerABI, provider);
      const Reserves = await pool.getReserves();
      const decimals = BigNumber.from((10 ** 18).toString());
      const token0Num = parseFloat(
        new Number(Reserves[0].toString()) / 10 ** token0Decimals
      ).toFixed(18);
      const token1Num = parseFloat(
        new Number(Reserves[1].toString()) / 10 ** token1Decimals
      ).toFixed(18);
      // console.log("token0Num", token0Num);
      if (token0 == this.tokenA) {
        this.tokenAdecimals = token0Decimals
        this.tokenBdecimals = token1Decimals
        this.poolData[0].token0IsU = true;
        this.poolData[0].USDTBalance = token0Num;
        this.poolData[0].tokenBalance = token1Num;
        this.poolData[0].TokenPrice = await this.GetPrice(
          decimals,
          Reserves[1],
          Reserves[0]
        );
      } else {
        this.tokenAdecimals = token1Decimals
        this.tokenBdecimals = token0Decimals
        this.poolData[0].token0IsU = false;
        this.poolData[0].USDTBalance = token1Num;
        this.poolData[0].tokenBalance = token0Num;
        this.poolData[0].TokenPrice = await this.GetPrice(
          decimals,
          Reserves[0],
          Reserves[1]
        );
      }
      this.nowPrice = this.poolData[0].TokenPrice;
      console.log(this.poolData);
    },
    GetWallte() {
      this.AddWallte()
        .then(() => {
          this.$message({
            type: "success",
            message: "添加完成!",
          });
        })
        .catch(() => {
          this.$message({
            type: "error",
            message: "错误,请确认私钥格式!",
          });
        });
    },
    async AddWallte() {
      const keys = this.form.privtKeys.split("\n");
      const addressData = [];
      keys.forEach((key) => {
        const pubKey = new ethers.Wallet(key).publicKey;
        const address = utils.computeAddress(pubKey);

        addressData.push({
          address: address,
          prvKey: key,
          ETHBalance: "",
          tokenBalance: "",
          USDTBalance: "",
          isApproved: "",
        });
      });
      this.addressData = addressData;
      // console.log(this.addressData)
      this.form._Options = true;
    },
    clearWallet() {
      this.form._Options = false;
      this.form.privtKeys = "";
      this.$message({
        type: "success",
        message: "已清除!",
      });
    },
    CheckWallte() {
      this.$message({
        type: "primary",
        message: "查询中,请稍后!",
      });
      this.CheckChildAddress()
        .then(() => {
          this.$message({
            type: "success",
            message: "查询完成!",
          });
        })
        .catch((err) => {
          console.log(err);
          this.$message({
            type: "error",
            message: "错误,请确认私钥是否正确,或网络连接!",
          });
        });
    },
    async CheckChildAddress() {
      const abi = this.ERC20.abi;
      const provider = new ethers.providers.JsonRpcProvider(this.rpc);
      // let bigETH = await provider.getBalance(address)
      // let ETHBalance = parseFloat().toFixed(18)
      // console.log(ETHBalance)
      // let contractAddress = "******************************************";
      const USDTContract = new ethers.Contract(this.tokenA, abi, provider);
      const TokenContract = new ethers.Contract(
        this.tokenB,
        abi,
        provider
      );
      this.addressData.forEach((row, index) => {
        const address = row.address;
        provider.getBalance(address).then((balance) => {
          // 余额是 BigNumber (in wei); 格式化为 ether 字符串
          const ETHBalance = parseFloat(
            new Number(balance.toString()) / 10 ** 18
          ).toFixed(18);
          this.addressData[index].ETHBalance = ETHBalance;
        });
        TokenContract.balanceOf(address).then((balance) => {
          // 余额是 BigNumber (in wei); 格式化为 ether 字符串
          const TokenBalance = parseFloat(
            new Number(balance.toString()) / 10 ** 18
          ).toFixed(18);
          this.addressData[index].tokenBalance = TokenBalance;
        });
        USDTContract.balanceOf(address).then((balance) => {
          // 余额是 BigNumber (in wei); 格式化为 ether 字符串
          const USDTBalance = parseFloat(
            new Number(balance.toString()) / 10 ** 18
          ).toFixed(18);
          this.addressData[index].USDTBalance = USDTBalance;
        });
        USDTContract.allowance(address, this.routerAddress).then((amount) => {
          if (amount.gt(ethers.constants.WeiPerEther.mul(1000))) {
            this.addressData[index].isApproved = "已授权";
          } else {
            this.addressData[index].isApproved = "未授权";
          }
        });
      });
    },
    open() {
      this.$confirm("当前执行批量买入, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$message({
            type: "success",
            message: "执行中,请稍后!",
          });
          this.disableCancle = false;
          this.loadingTask = true;
          this.onSubmit()
            .then(() => {})
            .catch((err) => {
              this.$message({
                type: "error",
                message: "错误,请检查配置是否正确!",
              });
              console.log(err);
              this.form.DoneInfo = "";
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消",
          });
        });
    },
    async onSubmit() {
      this.form.DoneInfo = "运行中……";
      this.stopTask = false;
      const routerABI = this.PancakeRouter;
      const pairABI = this.PancakeERC20.abi;
      const provider = new ethers.providers.JsonRpcProvider(this.rpc);
      const router = new ethers.Contract(
        this.routerAddress,
        routerABI,
        provider
      );
      const pool = new ethers.Contract(
        this.poolData[0].poolAddress,
        pairABI,
        provider
      );
      // let privateKey = "3250269e59a64416f330811232778b5844271f0188d4239bcac7e182ab9e179c"
      console.log(this.form.tradeNum)
      const buyAmount = ethers.utils.parseUnits(
        this.form.tradeNum.toString(),
        this.tokenBdecimals
      )
      // const buyAmount_ = BigNumber.from(
      //   (Number(this.form.tradeNum) * 10 ** 18).toString()
      // );
      const path = [this.tokenA, this.tokenB];
      const deadline = new Date().getTime() + 60000;
      const tradeSeconds = Number(this.form.tradeSeconds) * 1000;
      // console.log(path);
      // console.log('changdu', this.addressData.length)
      const sleep = (delay) =>
        new Promise((resolve) => setTimeout(resolve, delay));
      this.form.TxInfo = true;
      while (true) {
        if (this.stopTask) {
          break;
        }
        for (let i = 0; i < this.addressData.length; i++) {
          // this.addressData.forEach(async (row,index)=>{
          const txdata = {
            address: "",
            TxHash: "",
            TxStatus: "",
            currentPrice: "",
          };
          txdata.address = this.addressData[i].address;
          // console.log(txdata)
          const toAddress = this.addressData[i].address;
          const wallet = new ethers.Wallet(
            this.addressData[i].prvKey,
            provider
          );
          const contractWithSigner = router.connect(wallet);
          // console.log(contractWithSigner)
          // return
          var tx;
          var WETH = this.swapTokensOptions[0].value
          if (path[0] === WETH){ // swapExactETHForTokensSupportingFeeOnTransferTokens
            console.log("eth换代币")
            tx =
              await contractWithSigner.swapExactETHForTokensSupportingFeeOnTransferTokens(
                0,
                path,
                toAddress,
                deadline,
                { value: buyAmount }
              );
          }else if(path[1] === WETH){ //swapExactTokensForETHSupportingFeeOnTransferTokens
            console.log("代币换eth")
            tx =
              await contractWithSigner.swapExactTokensForETHSupportingFeeOnTransferTokens(
                buyAmount,
                0,
                path,
                toAddress,
                deadline
              );
          }else{ // swapExactTokensForTokensSupportingFeeOnTransferTokens
            console.log("代币换代币")
            tx =
              await contractWithSigner.swapExactTokensForTokensSupportingFeeOnTransferTokens(
                buyAmount,
                0,
                path,
                toAddress,
                deadline
              );
          }
          console.log("result", tx.hash);
          txdata.TxHash = tx.hash;
          // 操作还没完成，需要等待挖矿
          await tx.wait();
          txdata.TxStatus = "完成";
          console.log("done", i);
          this.$message({
            type: "success",
            message: tx.hash + "交易完成！",
          });
          const Reserves = await pool.getReserves();
          if (this.poolData[0].token0IsU) {
            var currentPrice = await this.GetPrice(
              ethers.constants.WeiPerEther,
              Reserves[1],
              Reserves[0]
            );
          } else {
            var currentPrice = await this.GetPrice(
              ethers.constants.WeiPerEther,
              Reserves[0],
              Reserves[1]
            );
          }
          txdata.currentPrice = currentPrice;
          this.nowPrice = currentPrice;
          console.log("tokenPrice", currentPrice);
          this.TxData.push(txdata);
          if (currentPrice >= Number(this.form.targetPrice)) {
            this.$message({
              type: "success",
              message: "恭喜!已达目标价格" + this.form.targetPrice,
            });
            this.form.DoneInfo = "恭喜!已达目标价格";
            this.loadingTask = false;
            this.stopTask = true;
            break;
          } else {
            console.log("waiting for few seconds~");
            this.$message({
              type: "info",
              message: "当前目标价格:" + currentPrice,
            });
            await sleep(tradeSeconds);
          }
        }
      }

      this.form.DoneInfo = "本次执行已结束,当前币价为:" + currentPrice;
      console.log("循环结束,当前币价为:", currentPrice);
    },
    cancelTask() {
      this.stopTask = true;
      this.disableCancle = true;
      this.form.DoneInfo = "运行中……";
      this.$notify({
        title: "成功",
        message: "自动交易停止",
        type: "success",
      });
      this.loadingTask = false;
    },
    GetApproval() {
      this.$message({
        type: "primary",
        message: "查询中,请稍后!",
      });
      this.ApprovalToRouter()
        .then(() => {
          this.$message({
            type: "success",
            message: "查询完成!",
          });
        })
        .catch(() => {
          this.$message({
            type: "error",
            message: "错误,请检查网络连接!",
          });
        });
    },

    async ApprovalToRouter() {
      const abi = this.ERC20.abi;
      const provider = new ethers.providers.JsonRpcProvider(this.rpc);
      const USDTContract = new ethers.Contract(this.tokenA, abi, provider);
      const MAX = BigNumber.from(
        "115792089237316195423570985008687907853269984665640564039457584007913129639935"
      );
      const sleep = (delay) =>
        new Promise((resolve) => setTimeout(resolve, delay));
      console.log(MAX);
      for (let i = 0; i < this.addressData.length; i++) {
        const wallet = new ethers.Wallet(this.addressData[i].prvKey, provider);
        const contractWithSigner = USDTContract.connect(wallet);
        const tx = await contractWithSigner.approve(this.routerAddress, MAX);
        this.$message({
          type: "info",
          message: "第" + i + "个地址" + "授权哈希为:" + tx.hash,
        });
        console.log("result", tx.hash);
        await tx.wait();
        this.$message({
          type: "success",
          message: this.addressData[i].address + "授权完成!",
        });
        console.log("done", i);
        await sleep(1000);
        console.log("sleep done");
      }
      console.log("完成!");
    },
    async GetPrice(AmmountIn, ReservesIn, ReservesOut) {
      const routerABI = this.PancakeRouter;
      const provider = new ethers.providers.JsonRpcProvider(this.rpc);
      const router = new ethers.Contract(
        this.routerAddress,
        routerABI,
        provider
      );
      const bigPrice = await router.getAmountOut(
        AmmountIn,
        ReservesIn,
        ReservesOut
      );
      const tokenprice = parseFloat(
        new Number(bigPrice.toString()) / 10 ** 18
      ).toFixed(18);
      return tokenprice;
    },
  },
};
</script>

<style>
.el-select .el-input {
  width: 100px;
}
.input-with-select .el-input-group__prepend {
  background-color: #fff;
}
.search{
  background-color: #409EFF !important;
  color: #FFF !important;
}
</style>

<style scoped>
@media screen and (max-width: 500px) {
  .main {
    text-align: center;
    padding-left: 0;
    padding-right: 0;
  }
}
@media screen and (min-width: 500px) {
  .header {
    text-align: center;
    margin-top: 20px;
    font-size: larger;
  }
  .main {
    text-align: center;
    padding-left: 15%;
    padding-right: 18%;
    margin-bottom: 10px;
  }
}
</style>

</style>
