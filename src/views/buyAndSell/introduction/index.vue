<template>
  <div class="app-container">
    <div class="inner" style="padding-top: 0px">
      <el-main class="main">
        <p style="text-align: center; font-size: 30px">操作说明</p>
        <!-- <p style="text-align: center;">批量交易顺序</p> -->
        <el-card class="box-card">
          <el-row
            :gutter="24"
            v-for="(item, k) in notice"
            :key="k"
            class="notice-item"
          >
            <el-col :span="1"> {{ k + 1 }}. </el-col>
            <el-col :span="23">
              {{ item }}
            </el-col>
          </el-row>
        </el-card>
      </el-main>
    </div>
    <div style="background-color: #eeeeee; margin-top: 100px">
      <el-divider
        >有任何问题请加入
        <el-link
          icon="el-icon-telegram"
          href="https://t.me/pandatool"
          target="_blank"
          >PandaTool</el-link
        >

        交流群进行反馈</el-divider
      >
    </div>
  </div>
</template>

<script>
export default {
  name: "Introduction",
  data() {
    return {
      notice: [
        "换链请在还有上角切换,先选择交易所",
        "代币A(输入)表示买入币种用代币A去买代币B,点击交换可反向操作。举例说明:在薄饼上做了熊猫币对USDT的交易对,如果要拉盘,则用USDT买入熊猫币。这里代币A填USDT的合约地址,代币B填熊猫币的合约地址。点击查询池子即可。如果要砸盘则点击交换即可",
        "查询钱包余额,请确保钱包内的Token余额支持单笔交易的数量,单笔交易的GasFee,BNB数量可对比SWAP中收取的量做参考,略高即可。",
        "若查询钱包余额,有钱包未授权SWAP转账相应代币,交易会失,请使用批量授权。",
        "批量授权手续费请参考线上SWAP数据,留足BNB余额。",
        "买入操作,请确保目标价格高于现价,卖出同理。",
        "单笔交易量,小数点后不要超过精度,一般为18。USDT精度为18。",
        "如需快速买入,建议将时间间隔设置为1s,过低可能导致错误!",
        "单笔交易量,即单次交易金额,币价快速上涨或下跌会引起SWAP交易保护,导致交易失败,请前往官方确认。",
        "GasFee自动计算,一般与正常交易相同,如有异常,请检查代币机制。",
        "所有准备工作完成后,点击立即执行。取消按取消键",
        "交易信息会以表格形式在最下方展示,每笔交易完成后会自动添加到表格中。",
        "交易中,会显示运行中,交易完成会展示相应文案,并展示当前币价。",
        "如需多次循环,无需刷新页面,再次点击立即执行,交易数据会继续添加在表格中。",
        "若展示运行中,且长时间内无数据添加,则可能为网络中断,重新打开系统操作即可。",
      ],
    };
  },
};
</script>

<style scoped>
@media screen and (max-width: 500px) {
  .main {
    text-align: left;
    padding-left: 0;
    padding-right: 0;
  }
}
@media screen and (min-width: 500px) {
  .header {
    text-align: left;
    margin-top: 20px;
    font-size: larger;
  }
  .notice-item {
    color: dimgray;
    text-align: left;
    margin-top: 15px;
    font-size: 18px;
  }
  .main {
    text-align: left;
    padding-left: 10%;
    padding-right: 10%;
    margin-bottom: 10px;
  }
}
</style>
