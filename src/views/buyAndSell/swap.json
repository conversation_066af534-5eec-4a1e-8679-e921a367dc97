{"97": [{"label": "薄饼测试", "value": {"swapName": "薄饼测试", "symbal": "tBNB", "tokens": [{"label": "tBNB", "symbal": "tBNB", "value": "******************************************"}, {"label": "BUSD", "symbal": "BUSD", "value": "******************************************"}], "swapParams": {"factoryAddress": "******************************************", "routers": [{"label": "v2", "value": "******************************************"}]}}}], "1": [{"label": "Uniswap", "value": {"swapName": "Uniswap", "symbal": "ETH", "tokens": [{"label": "ETH", "symbal": "ETH", "value": "******************************************"}, {"label": "USDT", "symbal": "USDT", "value": "******************************************"}], "swapParams": {"factoryAddress": "******************************************", "routers": [{"label": "v2", "value": "******************************************"}]}}}], "137": [{"label": "Quickswap", "value": {"swapName": "Quickswap", "symbal": "MATIC", "tokens": [{"label": "MATIC", "symbal": "MATIC", "value": "******************************************"}, {"label": "USDT", "symbal": "USDT", "value": "******************************************"}], "swapParams": {"factoryAddress": "******************************************", "routers": [{"label": "v2", "value": "******************************************"}]}}}], "250": [{"label": "SpookySwap", "value": {"swapName": "SpookySwap", "symbal": "FTM", "tokens": [{"label": "FTM", "symbal": "FTM", "value": "******************************************"}, {"label": "USDT", "symbal": "USDT", "value": "0x049d68029688eAbF473097a2fC38ef61633A3C7A"}], "swapParams": {"factoryAddress": "0x152eE697f2E276fA89E96742e9bB9aB1F2E61bE3", "routers": [{"label": "v2", "value": "0xF491e7B69E4244ad4002BC14e878a34207E38c29"}]}}}], "66": [{"label": "OKTCSwap", "value": {"swapName": "OKTCSwap", "symbal": "OKB", "tokens": [{"label": "OKB", "symbal": "OKB", "value": "******************************************"}, {"label": "USDT", "symbal": "USDT", "value": "******************************************"}], "swapParams": {"factoryAddress": "******************************************", "routers": [{"label": "v2", "value": "******************************************"}]}}}], "42161": [{"label": "Sushiswap", "value": {"swapName": "Sushiswap", "symbal": "ETH", "tokens": [{"label": "ETH", "symbal": "ETH", "value": "******************************************"}, {"label": "USDT", "symbal": "USDT", "value": "******************************************"}], "swapParams": {"factoryAddress": "******************************************", "routers": [{"label": "v2", "value": "******************************************"}]}}}], "1116": [{"label": "shadowswap", "value": {"swapName": "shadowswap", "symbal": "CORE", "tokens": [{"label": "CORE", "symbal": "CORE", "value": "******************************************"}, {"label": "USDT", "symbal": "USDT", "value": "******************************************"}], "swapParams": {"factoryAddress": "******************************************", "routers": [{"label": "v2", "value": "******************************************"}]}}}], "56": [{"label": "Pancake", "value": {"swapName": "Pancake", "symbal": "BNB", "tokens": [{"label": "BNB", "symbal": "BNB", "value": "******************************************"}, {"label": "USDT", "symbal": "USDT", "value": "0x55d398326f99059fF775485246999027B3197955"}, {"label": "BUSD", "symbal": "BUSD", "value": "0xe9e7CEA3DedcA5984780Bafc599bD69ADd087D56"}], "swapParams": {"factoryAddress": "0xcA143Ce32Fe78f1f7019d7d551a6402fC5350c73", "routers": [{"label": "v2", "value": "0x10ED43C718714eb63d5aA57B78B54704E256024E"}]}}}]}