<template>
  <div class="app-container">
    <div class="inner">
      <el-header class="header">
        <p>批量卖出页</p>
      </el-header>
      <el-main class="main">
        <el-form :model="form" label-width="100px" label-position="left">
          <el-form-item label="选择交易所">
            <el-select
              v-model="selectSwap"
              @change="selectSwapFunc"
              placeholder="请选择"
            >
              <el-option
                v-for="item in swapOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
            <el-select
              v-model="routerAddress"
              placeholder="请选择"
              v-show="swapSelectSwitch"
            >
              <el-option
                v-for="item in swapRouters"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="选择代币">
            <div>
              <el-input
                placeholder="请选择"
                v-model="USDTAddress"
                class="input-with-select"
              >
                <el-select
                  v-model="USDTAddress"
                  slot="prepend"
                  placeholder="请选择"
                  @change="selectTokenFunc"
                >
                  <el-option
                    v-for="item in swapTokensOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  >
                  </el-option>
                </el-select>
              </el-input>
            </div>
          </el-form-item>
          <el-form-item label="目标代币地址">
            <el-input
              v-model="form.tokenAddress"
              placeholder="0x4950d9E63C2BA04355b1ba1Bfa4245777aF93d5E"
            />
            <el-popover
              placement="top-start"
              title="提示"
              width="400"
              trigger="hover"
              content="查询池子前,需添加目标代币和USDT的交易对,否则查询不出结果!查询数据较多,一般需要5秒左右"
            >
              <el-button
                style="margin-top: 10px"
                type="primary"
                slot="reference"
                @click="GetPool"
              >
                查询池子
              </el-button>
            </el-popover>
          </el-form-item>
          <el-form-item>
            <el-table v-show="form._Pool" :data="poolData" style="width: 100%">
              <el-table-column
                prop="poolAddress"
                label="池子地址"
                width="300"
              />
              <el-table-column
                prop="tokenBalance"
                label="目标代币数量"
                width="230"
              />
              <el-table-column
                prop="USDTBalance"
                label="交易对代币数量"
                width="230"
              />
              <el-table-column prop="TokenPrice" label="代币价格" width="150" />
            </el-table>
          </el-form-item>
          <el-form-item label="钱包私钥">
            <el-input v-model="form.privtKeys" type="textarea" :rows="10" />
            <el-popover
              placement="top-start"
              title="提示"
              width="400"
              trigger="hover"
              content="未限制钱包数量,一般不要超过100个,交易时长过久易导致网络中断.钱包可重复添加,添加多个相同钱包,一次执行则交易多次"
            >
              <el-button
                style="
                  margin-top: 10px;
                  margin-bottom: 10px;
                  margin-right: 10px;
                "
                type="primary"
                @click="GetWallte"
                slot="reference"
                >添加钱包</el-button
              >
            </el-popover>
            <el-button
              style="margin-top: 10px; margin-bottom: 10px"
              type="primary"
              @click="clearWallet"
              >清除钱包</el-button
            >
          </el-form-item>
          <el-form-item>
            <el-table
              v-show="form._Options"
              :data="addressData"
              style="width: 100%"
            >
              <el-table-column prop="address" label="钱包地址" width="200" />
              <el-table-column prop="ETHBalance" label="BNB数量" width="200" />
              <el-table-column
                prop="tokenBalance"
                label="目标代币数量"
                width="200"
              />
              <el-table-column
                prop="USDTBalance"
                label="交易对代币数量"
                width="200"
              />
              <el-table-column
                prop="isApproved"
                label="Token授权"
                width="120"
              />
            </el-table>
          </el-form-item>
          <!-- </el-form> -->
          <!-- <el-form v-show="form._Options" :model="form" style = "margin-top:10px;margin-bottom:20px;" label-width="auto" label-position="left" > -->
          <el-form-item v-show="form._Options">
            <el-popover
              placement="top-start"
              title="提示"
              width="400"
              trigger="hover"
              content="批量买入前,请确认钱包有足够BNB支付手续费,每个钱包都需支付其转账所需的手续费。
            批量买入前,请确认钱包已经授权Pancake交易所转出USDT。
            批量授权,依次将钱包授权给Pancake,请注意BNB消耗。
            "
            >
              <el-button
                style="
                  margin-top: 10px;
                  margin-bottom: 10px;
                  margin-right: 10px;
                "
                type="primary"
                @click="CheckWallte"
                slot="reference"
                >查询余额</el-button
              >
            </el-popover>
            <el-button
              style="margin-top: 10px; margin-bottom: 10px"
              type="primary"
              @click="GetApproval"
              >批量授权</el-button
            >
          </el-form-item>
          <el-form-item v-show="form._Options">
            <p>目前价格:{{ nowPrice }}</p>
          </el-form-item>
          <el-form-item v-show="form._Options" label="目标价格">
            <el-input v-model="form.targetPrice" placeholder="10" />
          </el-form-item>
          <el-form-item v-show="form._Options" label="单笔交易量(Token)">
            <el-input v-model="form.tradeNum" placeholder="0.5" />
          </el-form-item>
          <el-form-item v-show="form._Options" label="时间间隔(s)">
            <el-input v-model="form.tradeSeconds" placeholder="3" />
          </el-form-item>
          <el-form-item v-show="form._Options">
            <el-popover
              placement="top-start"
              title="提示"
              width="600"
              trigger="hover"
              content="批量买入前,请确认已经查询过池子信息,否则将无法转账。
            由于pancake需收取0.25%手续费,价格计算会有千分之一的偏差。
            批量授权,依次将钱包授权给Pancake,请注意BNB消耗。
            每笔转账大约需要10秒确认,如需快速买入,建议将时间间隔设置为1s,过低可能导致错误!
            所有钱包买入一次后仍未到达目标价格,再次点击执行即可。
            循环过程中到达目标价格,程序将自动终止并显示结果。
            一笔交易通常10秒内确认结果,并自动添加数据。
            如遇卡死,请检查所有条件是否满足,刷新页面,重新操作即可。
            "
            >
              <el-button
                v-show="form._Options"
                style="
                  margin-top: 10px;
                  margin-bottom: 10px;
                  margin-right: 10px;
                "
                type="primary"
                :loading="loadingTask"
                @click="open"
                slot="reference"
                >{{ loadingTask ? "执行中" : "立即执行" }}</el-button
              >
            </el-popover>
            <el-button
              v-show="form._Options"
              type="primary"
              :disabled="disableCancle"
              @click="cancelTask"
              >取消</el-button
            >
          </el-form-item>
          <el-form-item v-show="form.TxInfo">
            <el-table :data="TxData" style="width: 100%">
              <el-table-column prop="address" label="钱包地址" width="300" />
              <el-table-column prop="TxHash" label="交易ID" width="300" />
              <el-table-column prop="TxStatus" label="交易状态" width="100" />
              <el-table-column
                prop="currentPrice"
                label="最新价格"
                width="200"
              />
            </el-table>
            <p v-show="form.TxInfo">{{ this.form.DoneInfo }}</p>
          </el-form-item>
        </el-form>
      </el-main>
    </div>
    <div style="background-color: #eeeeee; margin-top: 100px">
      <el-divider
        >有任何问题请加入
        <el-link
          icon="el-icon-telegram"
          href="https://t.me/pandatool"
          target="_blank"
          >PandaTool</el-link
        >

        交流群进行反馈</el-divider
      >
    </div>
  </div>
</template>

<script>
import ERC20 from "@/contracts/MostSimple.json";
import PancakeERC20 from "@/contracts/PancakeERC20.json";
import PancakeFactory from "@/contracts/PancakeFactory.json";
import PancakeRouter from "@/contracts/PancakeRouter.json";
import swap from "@/views/buyAndSell/swap.json";
import chainList from "@/contracts/chainlist.json";
import store from "@/store";
const { ethers, utils, BigNumber } = require("ethers");
export default {
  name: "Dashboard",
  data() {
    return {
      selectSwap: [],
      swapOptions: [],
      swapTokensOptions: [],
      swapRouters: [{ label: "v2", value: "" }],
      swapSelectSwitch: false,
      ERC20,
      value: [],
      PancakeERC20,
      PancakeFactory,
      PancakeRouter,
      USDTAddress: "******************************************",
      factoryAddress: "******************************************",
      routerAddress: "******************************************",
      rpc: "https://bsc-testnet.public.blastapi.io",
      isERC20Token: false,
      // store,
      poolData: [
        {
          token0IsU: true,
          poolAddress: "",
          tokenBalance: "",
          USDTBalance: "",
          TokenPrice: "",
        },
      ],
      addressData: [
        {
          address: "",
          prvKey: "",
          ETHBalance: "",
          tokenBalance: "",
          USDTBalance: "",
          isApproved: "",
        },
      ],
      TxData: [],
      form: {
        privtKeys:
          "",

        tokenAddress: "",
        _Pool: false,
        targetPrice: "10",
        tradeNum: "1",
        tradeSeconds: "1",
        _Options: false,
        TxInfo: false,
        DoneInfo: "运行中……",
      },
      stopTask: false,
      disableCancle: true,
      loadingTask: false,
      nowPrice: 0,
    };
  },
  created() {
    let Inval = setInterval(() => {
      this.swapOptions = swap[store.state.user.chainId];
      console.log(this.swapOptions);
      for (let i = 1; i < chainList.ChainInfo.length; i++) {
        if (chainList.ChainInfo[i].Id == store.state.user.chainId) {
          this.rpc = chainList.ChainInfo[i].rpcUrls;
        }
      }
      console.log("当前rpc", this.rpc);
      window.clearInterval(Inval);
    }, 500);
  },
  methods: {
    selectSwapFunc() {
      this.swapSelectSwitch = true;
      this.factoryAddress = this.selectSwap.swapParams.factoryAddress;
      this.swapRouters = this.selectSwap.swapParams.routers;
      this.swapTokensOptions = this.selectSwap.tokens;
      this.routerAddress = this.swapRouters[0].value;
    },
    selectTokenFunc(item) {
      this.isERC20Token = true;
      if (item == this.swapTokensOptions[0].value) {
        this.isERC20Token = false;
      }
      //console.log(this.isERC20Token)
    },
    GetPool() {
      this.$message({
        type: "primary",
        message: "查询中,请稍后!",
      });
      this.GetPoolInfo()
        .then(() => {
          this.$message({
            type: "success",
            message: "查询完成!",
          });
        })
        .catch(() => {
          this.$message({
            type: "error",
            message: "查询错误,请确认池子已添加或网络连接",
          });
        });
    },
    async GetPoolInfo() {
      this.form._Pool = true;
      const pairABI = this.PancakeERC20.abi;
      const factoryABI = this.PancakeFactory.abi;
      const routerABI = this.PancakeRouter;
      // const bytecode = data.bytecode
      // console.log(abi)
      const provider = new ethers.providers.JsonRpcProvider(this.rpc);
      const factory = new ethers.Contract(
        this.factoryAddress,
        factoryABI,
        provider
      );
      const pairAddress = await factory.getPair(
        this.USDTAddress,
        this.form.tokenAddress
      );
      if (pairAddress == "******************************************") {
        this.$message({
          type: "error",
          message: "池子查询失败，请确认池子",
        });
        return;
      }
      console.log(pairAddress);
      this.poolData[0].poolAddress = pairAddress;
      const pool = new ethers.Contract(pairAddress, pairABI, provider);
      const token0 = await pool.token0();
      const token1 = await pool.token1();
      console.log(token0, token1);
      // let router = new ethers.Contract(this.routerAddress, routerABI, provider);
      const Reserves = await pool.getReserves();
      const decimals = BigNumber.from((10 ** 18).toString());
      const token0Num = parseFloat(
        new Number(Reserves[0].toString()) / 10 ** 18
      ).toFixed(18);
      const token1Num = parseFloat(
        new Number(Reserves[1].toString()) / 10 ** 18
      ).toFixed(18);
      // const amountIn = BigNumber.from((10**18).toString())

      console.log("token0Num", token0Num);
      // let tokenprice = await router.getAmountOut(amountIn,Reserves[0],Reserves[1])
      // console.log('tokenprice',(tokenprice.div(amountIn)).toString())
      if (token0 == this.USDTAddress) {
        this.poolData[0].token0IsU = true;
        this.poolData[0].USDTBalance = token0Num;
        this.poolData[0].tokenBalance = token1Num;
        this.poolData[0].TokenPrice = await this.GetPrice(
          decimals,
          Reserves[1],
          Reserves[0]
        );
      } else {
        this.poolData[0].token0IsU = false;
        this.poolData[0].USDTBalance = token1Num;
        this.poolData[0].tokenBalance = token0Num;
        this.poolData[0].TokenPrice = await this.GetPrice(
          decimals,
          Reserves[0],
          Reserves[1]
        );
      }
      this.nowPrice = this.poolData[0].TokenPrice;
      console.log(this.poolData);
    },

    GetWallte() {
      this.AddWallte()
        .then(() => {
          this.$message({
            type: "success",
            message: "添加完成!",
          });
        })
        .catch(() => {
          this.$message({
            type: "error",
            message: "错误,请确认私钥格式!",
          });
        });
    },
    async AddWallte() {
      const keys = this.form.privtKeys.split("\n");
      const addressData = [];
      keys.forEach((key) => {
        const pubKey = new ethers.Wallet(key).publicKey;
        const address = utils.computeAddress(pubKey);

        addressData.push({
          address: address,
          prvKey: key,
          ETHBalance: "",
          tokenBalance: "",
          USDTBalance: "",
          isApproved: "",
        });
      });
      this.addressData = addressData;
      // console.log(this.addressData)
      this.form._Options = true;
    },
    clearWallet() {
      this.form._Options = false;
      this.form.privtKeys = "";
      this.$message({
        type: "success",
        message: "已清除!",
      });
    },
    CheckWallte() {
      this.$message({
        type: "primary",
        message: "查询中,请稍后!",
      });
      this.CheckChildAddress()
        .then(() => {
          this.$message({
            type: "success",
            message: "查询完成!",
          });
        })
        .catch(() => {
          this.$message({
            type: "error",
            message: "错误,请确认私钥是否正确,或网络连接!",
          });
        });
    },
    async CheckChildAddress() {
      const abi = this.ERC20.abi;
      const provider = new ethers.providers.JsonRpcProvider(this.rpc);
      // let address = "******************************************"
      // let bigETH = await provider.getBalance(address)
      // let ETHBalance = parseFloat(new Number(bigETH.toString())/(10**18)).toFixed(18)
      // console.log(ETHBalance)
      // let contractAddress = "******************************************";
      const USDTContract = new ethers.Contract(this.USDTAddress, abi, provider);
      const TokenContract = new ethers.Contract(
        this.form.tokenAddress,
        abi,
        provider
      );
      this.addressData.forEach((row, index) => {
        const address = row.address;
        provider.getBalance(address).then((balance) => {
          // 余额是 BigNumber (in wei); 格式化为 ether 字符串
          const ETHBalance = parseFloat(
            new Number(balance.toString()) / 10 ** 18
          ).toFixed(18);
          this.addressData[index].ETHBalance = ETHBalance;
        });
        TokenContract.balanceOf(address).then((balance) => {
          // 余额是 BigNumber (in wei); 格式化为 ether 字符串
          const TokenBalance = parseFloat(
            new Number(balance.toString()) / 10 ** 18
          ).toFixed(18);
          this.addressData[index].tokenBalance = TokenBalance;
        });
        USDTContract.balanceOf(address).then((balance) => {
          // 余额是 BigNumber (in wei); 格式化为 ether 字符串
          const USDTBalance = parseFloat(
            new Number(balance.toString()) / 10 ** 18
          ).toFixed(18);
          this.addressData[index].USDTBalance = USDTBalance;
        });
        TokenContract.allowance(address, this.routerAddress).then((amount) => {
          if (amount.gt(ethers.constants.WeiPerEther.mul(1000))) {
            this.addressData[index].isApproved = "已授权";
          } else {
            this.addressData[index].isApproved = "未授权";
          }
        });
      });
    },
    open() {
      this.$confirm("当前执行批量卖出, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.$message({
            type: "success",
            message: "执行中,请稍后!",
          });
          this.disableCancle = false;
          this.loadingTask = true;
          this.onSubmit()
            .then(() => {})
            .catch((res) => {
              console.log(res);
              this.$message({
                type: "error",
                message: "错误,请检查配置是否正确!",
              });
              this.form.DoneInfo = "";
            });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消",
          });
        });
    },
    async onSubmit() {
      this.form.DoneInfo = "运行中……";
      this.stopTask = false;
      const routerABI = this.PancakeRouter;
      const pairABI = this.PancakeERC20.abi;
      const provider = new ethers.providers.JsonRpcProvider(this.rpc);
      const router = new ethers.Contract(
        this.routerAddress,
        routerABI,
        provider
      );
      const pool = new ethers.Contract(
        this.poolData[0].poolAddress,
        pairABI,
        provider
      );
      // let privateKey = "3250269e59a64416f330811232778b5844271f0188d4239bcac7e182ab9e179c"
      const txAmount = BigNumber.from(
        (Number(this.form.tradeNum) * 10 ** 18).toString()
      );
      // console.log("cc",this.form.tokenAddress,this.USDTAddress)
      const path = [this.form.tokenAddress, this.USDTAddress];
      const deadline = new Date().getTime() + 60000;
      const tradeSeconds = Number(this.form.tradeSeconds) * 1000;
      const sleep = (delay) =>
        new Promise((resolve) => setTimeout(resolve, delay));
      this.form.TxInfo = true;
      while (true) {
        if (this.stopTask) {
          break;
        }
        for (let i = 0; i < this.addressData.length; i++) {
          // this.addressData.forEach(async (row,index)=>{
          const txdata = {
            address: "",
            TxHash: "",
            TxStatus: "",
            currentPrice: "",
          };
          txdata.address = this.addressData[i].address;
          console.log(this.addressData[i].prvKey);
          const toAddress = this.addressData[i].address;
          const wallet = new ethers.Wallet(
            this.addressData[i].prvKey,
            provider
          );
          const contractWithSigner = router.connect(wallet);
          var tx;
          if (this.isERC20Token) {
            tx =
              await contractWithSigner.swapExactTokensForTokensSupportingFeeOnTransferTokens(
                txAmount,
                0,
                path,
                toAddress,
                deadline
              );
          } else {
            console.log("txamount", txAmount);
            console.log("txamount", this.form.tradeNum);
            tx =
              await contractWithSigner.swapExactTokensForETHSupportingFeeOnTransferTokens(
                txAmount,
                0,
                path,
                toAddress,
                deadline
              );
          }
          console.log("result", tx.hash);
          txdata.TxHash = tx.hash;
          // 操作还没完成，需要等待挖矿
          await tx.wait();
          txdata.TxStatus = "完成";
          console.log("done", i);
          this.$message({
            type: "success",
            message: tx.hash + "交易完成！",
          });

          const Reserves = await pool.getReserves();
          if (this.poolData[0].token0IsU) {
            var currentPrice = await this.GetPrice(
              ethers.constants.WeiPerEther,
              Reserves[1],
              Reserves[0]
            );
          } else {
            var currentPrice = await this.GetPrice(
              ethers.constants.WeiPerEther,
              Reserves[0],
              Reserves[1]
            );
          }
          txdata.currentPrice = currentPrice;
          this.nowPrice = currentPrice;
          console.log("tokenPrice", currentPrice);
          this.TxData.push(txdata);
          if (currentPrice <= Number(this.form.targetPrice)) {
            this.form.DoneInfo = "恭喜!已达目标价格";
            this.stopTask = true;
            break;
          } else {
            console.log("waiting for few seconds~");
            await sleep(tradeSeconds);
          }
        }
      }

      if (currentPrice <= Number(this.form.targetPrice)) {
        this.$message({
          type: "success",
          message: "恭喜!已达目标价格" + currentPrice,
        });
        this.loadingTask = false;
        this.form.DoneInfo = "恭喜!已达目标价格!";
      } else {
        this.$message({
          type: "info",
          message: "当前目标价格:" + currentPrice,
        });
        this.form.DoneInfo = "本次执行已结束,当前币价为:" + currentPrice;
      }
    },
    cancelTask() {
      this.stopTask = true;
      this.disableCancle = true;
      this.form.DoneInfo = "运行中……";
      this.$notify({
        title: "成功",
        message: "自动交易停止",
        type: "success",
      });
      this.loadingTask = false;
    },
    GetApproval() {
      this.$message({
        type: "primary",
        message: "授权中,请稍后!",
      });
      this.ApprovalToRouter()
        .then(() => {
          this.$message({
            type: "success",
            message: "授权完成!",
          });
        })
        .catch(() => {
          this.$message({
            type: "error",
            message: "错误,请检查网络连接!",
          });
        });
    },

    async ApprovalToRouter() {
      const abi = this.ERC20.abi;
      const provider = new ethers.providers.JsonRpcProvider(this.rpc);
      const tokenContract = new ethers.Contract(
        this.form.tokenAddress,
        abi,
        provider
      );
      const MAX = BigNumber.from(
        "115792089237316195423570985008687907853269984665640564039457584007913129639935"
      );
      const sleep = (delay) =>
        new Promise((resolve) => setTimeout(resolve, delay));
      console.log(MAX);
      for (let i = 0; i < this.addressData.length; i++) {
        const wallet = new ethers.Wallet(this.addressData[i].prvKey, provider);
        const contractWithSigner = tokenContract.connect(wallet);
        const tx = await contractWithSigner.approve(this.routerAddress, MAX);
        this.$message({
          type: "info",
          message: "第" + i + "个地址" + "授权哈希为:" + tx.hash,
        });
        console.log("result", tx.hash);
        await tx.wait();
        this.$message({
          type: "success",
          message: this.addressData[i].address + "授权完成!",
        });

        await sleep(1000);
        console.log("sleep done");
      }
      console.log("完成!");
    },
    async GetPrice(AmmountIn, ReservesIn, ReservesOut) {
      const routerABI = this.PancakeRouter;
      const provider = new ethers.providers.JsonRpcProvider(this.rpc);
      const router = new ethers.Contract(
        this.routerAddress,
        routerABI,
        provider
      );
      const bigPrice = await router.getAmountOut(
        AmmountIn,
        ReservesIn,
        ReservesOut
      );
      const tokenprice = parseFloat(
        new Number(bigPrice.toString()) / 10 ** 18
      ).toFixed(18);
      return tokenprice;
    },
  },
};
</script>

<style scoped>
@media screen and (max-width: 500px) {
  .main {
    text-align: center;
    padding-left: 0;
    padding-right: 0;
  }
}
@media screen and (min-width: 500px) {
  .header {
    text-align: center;
    margin-top: 20px;
    font-size: larger;
  }
  .main {
    text-align: center;
    padding-left: 15%;
    padding-right: 18%;
    margin-bottom: 300px;
  }
}
</style>
