<template>
  <el-form
    ref="form"
    :model="form"
    label-width="100px"
    style="max-width: 800px; margin: 0 auto"
  >
    <el-form-item label="姓名" prop="name">
      <el-input v-model="form.name" />
    </el-form-item>
    <el-form-item label="性别" prop="gender">
      <el-radio-group v-model="form.gender">
        <el-radio label="男" />
        <el-radio label="女" />
      </el-radio-group>
    </el-form-item>
    <el-form-item label="年龄" prop="age">
      <el-input v-model.number="form.age" type="number" />
    </el-form-item>
    <el-form-item label="所在地" prop="location">
      <el-select v-model="form.location">
        <el-option label="北京" value="Beijing" />
        <el-option label="上海" value="Shanghai" />
        <el-option label="广州" value="Guangzhou" />
        <el-option label="深圳" value="Shenzhen" />
      </el-select>
    </el-form-item>
    <el-form-item label="备注" prop="remark">
      <el-input v-model="form.remark" type="textarea" />
    </el-form-item>
    <el-form-item>
      <el-button type="primary" @click="submitForm">提交</el-button>
      <el-button @click="resetForm">重置</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
export default {
  data() {
    return {
      form: {
        name: '',
        gender: '',
        age: null,
        location: '',
        remark: ''
      }
    }
  },
  methods: {
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          // 表单验证通过，提交表单数据
          console.log(this.form)
        }
      })
    },
    resetForm() {
      this.$refs.form.resetFields()
    }
  }
}
</script>
