<template>
  <div class="app-container">
    <div class="inner">
      <el-alert
        v-show="support"
        :title="$t('multisend.暂不支持此链')"
        type="error"
        :description="$t('multisend.如有需要请联系管理员定制')"
        center
        :closable="false"
        show-icon
      >
      </el-alert>
      <el-header class="header">
        <p class="p-title">{{ $t("multisend.批量转账") }}</p>
        <span class="header-span"
          >{{ $t("multisend.手续费提示") }}:{{ this.headerFee
          }}{{ this.headerSymbol }}</span
        >
        <div class="jiaocheng">
          <el-link :href="helpUrl" target="_brank" type="danger">{{
            $t("multisend.点击查看手把手教程")
          }}</el-link>
        </div>
      </el-header>
      <el-main class="main" style="margin-top: 30px">
        <el-form
          ref="form"
          :model="form"
          label-width="200px"
          label-position="top"
        >
          <el-form-item :label="$t('multisend.发送代币')">
            <el-autocomplete
              v-model="state"
              class="j-input"
              value-key="value"
              :fetch-suggestions="querySearchAsync"
              :placeholder="$t('multisend.请输入代币合约地址')"
              @select="handleSelect"
              @change="getErc20Contract"
            >
              <i
                slot="suffix"
                class="el-input__icon el-icon-circle-close"
                @click="clearInput"
              />
            </el-autocomplete>
          </el-form-item>
          <el-form-item :label="$t('multisend.地址数量提示')">
            <el-button
              type="primary"
              style="margin-bottom: 10px"
              @click="dialogVisible = true"
              >{{ $t("multisend.随机地址") }}</el-button
            >
            <el-input
              v-model="form.textarea"
              type="textarea"
              :placeholder="$t('multisend.请输入内容')"
              maxlength="50000"
              show-word-limit
              :rows="6"
              style="font-size: 14px"
              @focus="lastStep"
            >
            </el-input>
          </el-form-item>
          <p style="color: red; font-size: 10px">
            {{ $t("multisend.注意") }}
          </p>
          <el-form-item v-show="form.nextSwich">
            <el-button
              type="primary"
              :loading="nextLoading"
              @click="nextStep"
              >{{ $t("multisend.下一步") }}</el-button
            >
          </el-form-item>
          <el-form-item v-show="form.TxInfo">
            <el-form-item>
              <el-table
                :data="tableData"
                border
                max-height="300"
                style="width: 100%; margin-bottom: 10px"
              >
                <el-table-column
                  prop="address"
                  :label="$t('multisend.接收地址')"
                >
                </el-table-column>
                <el-table-column
                  prop="amount"
                  :label="$t('multisend.数量')"
                  width="180"
                >
                </el-table-column>
              </el-table>
            </el-form-item>
            <el-form-item>
              <el-card class="box-card">
                <div class="clearfix">
                  <span>{{ $t("multisend.摘要") }}</span>
                </div>
                <el-row :gutter="24">
                  <el-col :span="6" class="card-row-left"
                    >{{ $t("multisend.转出地址") }}:</el-col
                  >
                  <el-col :span="18" class="card-row-right">{{
                    this.address
                  }}</el-col>
                </el-row>
                <el-row :gutter="24">
                  <el-col :span="6" class="card-row-left"
                    >{{ $t("multisend.代币余额") }}:</el-col
                  >
                  <el-col :span="18" class="card-row-right"
                    >{{ this.form.selectToken.balance }} &nbsp;
                    {{ this.form.selectToken.symbol }}</el-col
                  >
                </el-row>
                <el-row :gutter="24">
                  <el-col :span="6" class="card-row-left"
                    >{{ $t("multisend.代币地址") }}:</el-col
                  >
                  <el-col :span="18" class="card-row-right">{{
                    this.form.selectToken.address
                  }}</el-col>
                </el-row>
                <el-row :gutter="24">
                  <el-col :span="6" class="card-row-left"
                    >{{ $t("multisend.转账金额") }}:</el-col
                  >
                  <el-col :span="18" class="card-row-right"
                    >{{ this.showApprove }}
                    {{ this.form.selectToken.symbol }}</el-col
                  >
                </el-row>
              </el-card>
            </el-form-item>
            <el-form-item v-show="disableAppove" style="margin-top: 20px">
              <el-radio-group v-model="form.radio">
                <el-radio label="1"
                  >{{ $t("multisend.授权金额") }}:{{
                    $t("multisend.发送数量")
                  }}({{ this.showApprove }} {{ this.form.selectToken.symbol }})
                </el-radio>
                <el-radio label="0">{{ $t("multisend.全部授权") }}</el-radio>
                <el-form-item style="margin-top: 10px">
                  <el-button
                    type="primary"
                    :loading="approveLoading"
                    @click="doApprove"
                    >{{ $t("multisend.授权") }}</el-button
                  >
                  <el-button type="primary" @click="lastStep">{{
                    $t("multisend.返回")
                  }}</el-button>
                </el-form-item>
              </el-radio-group>
            </el-form-item>
            <el-form-item v-show="!disableAppove" style="margin-top: 20px">
              <el-form-item>
                <el-button
                  type="primary"
                  :loading="sendLoading"
                  @click="send"
                  >{{ $t("multisend.转账") }}</el-button
                >
                <el-button type="primary" @click="lastStep">{{
                  $t("multisend.返回")
                }}</el-button>
              </el-form-item>
            </el-form-item>
          </el-form-item>
        </el-form>
      </el-main>
    </div>
    <el-dialog
      :title="$t('multisend.输入随机地址数量')"
      :visible.sync="dialogVisible"
      width="50%"
    >
      <el-form :model="randonSet">
        <p style="color: red; margin-left: 1rem">
          {{ $t("multisend.警告一") }}
          <br />
          {{ $t("multisend.警告二") }}<br />{{ $t("multisend.警告三") }}
        </p>
        <el-form-item class="dialog-form-item">
          <el-row>
            <el-col :span="6"
              ><span> {{ $t("multisend.地址数") }} </span>
            </el-col>
            <el-col :span="18"
              ><el-input
                type="text"
                v-model="randonSet.addressNum"
                autocomplete="off"
              ></el-input
            ></el-col>
            <el-col :span="6"
              ><span> {{ $t("multisend.最小金额") }} </span>
            </el-col>
            <el-col :span="18"
              ><el-input
                type="text"
                v-model="randonSet.min"
                autocomplete="off"
              ></el-input
            ></el-col>
            <el-col :span="6"
              ><span> {{ $t("multisend.最大金额") }} </span>
            </el-col>
            <el-col :span="18"
              ><el-input
                type="text"
                v-model="randonSet.max"
                autocomplete="off"
              ></el-input
            ></el-col>
          </el-row>
        </el-form-item>
        <el-form-item>
          <el-row :gutter="24">
            <el-col :span="12">
              <el-button
                @click="dialogVisible = false"
                style="margin-left: 70%"
                >{{ $t("multisend.取消") }}</el-button
              ></el-col
            >
            <el-col :span="12">
              <el-button
                type="primary"
                @click="randomAddress"
                :loading="raddressLoading"
                >{{ $t("multisend.确定") }}</el-button
              ></el-col
            >
          </el-row>
          <p>{{ message }}</p>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { ethers, utils } from "ethers";
import multisendAbi from "@/contracts/multiTransfer.abi.json";
import sendList from "@/contracts/multiTransferContractList.json";
import store from "@/store";
import axios from "axios";

export default {
  data() {
    return {
      helpUrl: `https://help.pandatool.org/${
        this.$i18n.locale == "en-US" ? "english/" : ""
      }tools/batch-transfer`,
      wallet: { balance: 0 },
      headerFee: 0,
      headerSymbol: "",
      chainId: 0,
      chainName: "",
      address: "",
      nextLoading: false,
      approveLoading: false,
      sendLoading: false,
      dialogVisible: false,
      disableAppove: true,
      tokenList: [],
      tokenTableData: [],
      state: "",
      support: false,
      timeout: null,
      clicked: 0,
      activeNames: ["1"],
      randonSet: {
        addressNum: 200,
        min: 1,
        max: 10,
      },
      message: "",
      raddressLoading: false,
      form: {
        textarea: "******************************************,1",
        radio: "0",
        symbol: "",
        token: "",
        selectToken: [],
        handleTextare: [],
        approve: 0,
        TxInfo: false,
        nextSwich: true,
      },
      showApprove: 0,
      transferPara: [],
      tableData: [],
      feeAmount: 0,
    };
  },
  created() {
    let Inval = setInterval(() => {
      // this.headerFee = sendList[store.state.user.chainId].fee;
      this.headerSymbol = sendList[store.state.user.chainId].symbol;
      // console.log(sendList[store.state.user.chainId].switch)
      if (sendList[store.state.user.chainId].switch) {
        this.getFee().then((res) => {
          this.headerFee = ethers.utils.formatEther(res);
          // console.log(this.headerFee);
        });
      } else {
        this.support = true;
      }
      this.chainId = store.state.user.chainId;
      this.chainName = store.state.user.chainName;
      this.address = store.state.user.address;
      var covalenthqApiKey = "ckey_31066290a660460998adea1e988";
      var covalentApi = "https://api.covalenthq.com/v1/";
      var url =
        covalentApi +
        this.chainId +
        "/address/" +
        this.address +
        "/balances_v2/?no-nft-fetch=true&key=" +
        covalenthqApiKey;
      axios
        .get(url)
        .then((response) => {
          var reqdata = response.data.data;
          for (var i in reqdata.items) {
            reqdata.items[i].balance;
            var balance = ethers.utils.formatEther(reqdata.items[i].balance);
            if (
              parseFloat(balance) > 0 &&
              reqdata.items[i].contract_ticker_symbol
            ) {
              var temp = {
                value:
                  reqdata.items[i].contract_ticker_symbol +
                  " : " +
                  reqdata.items[i].contract_address,
                address: reqdata.items[i].contract_address,
                balance: balance,
                decimals: reqdata.items[i].contract_decimals,
                symbol: reqdata.items[i].contract_ticker_symbol,
              };
              this.tokenList.push(temp);
              // console.log(this.tokenList)
            }
          }
        })
        .catch((error) => {
          const provider = store.state.user.provider;
          provider.getBalance(store.state.user.address).then((res) => {
            var balance = ethers.utils.formatEther(res);
            var temp = {
              value: (this.headerSymbol =
                sendList[store.state.user.chainId].symbol +
                " : " +
                "******************************************"),
              address: "******************************************",
              balance: balance,
              decimals: 18,
              symbol: (this.headerSymbol =
                sendList[store.state.user.chainId].symbol),
            };
            this.tokenList.push(temp);
          });

          console.error(error);
        });
      if (store.state.user.address) {
        window.clearInterval(Inval);
      }
    }, 500);
  },
  methods: {
    querySearchAsync(queryString, cb) {
      var restaurants = this.tokenList;
      var results = queryString
        ? restaurants.filter(this.createStateFilter(queryString))
        : restaurants;
      clearTimeout(this.timeout);
      this.timeout = setTimeout(() => {
        cb(results);
      }, 300 * Math.random());
    },
    createStateFilter(queryString) {
      return (state) => {
        return (
          state.value.toLowerCase().indexOf(queryString.toLowerCase()) === 0
        );
      };
    },
    clearInput() {
      this.state = "";
      this.form.selectToken = [];
      this.lastStep();
    },

    nextStep() {
      if (!sendList[store.state.user.chainId].switch) {
        this.$notify({
          title: this.$t("multisend.提示"),
          message: this.$t("multisend.暂不支持此链"),
          type: "warning",
        });
        return;
      }
      if (!this.checkSelect()) {
        return;
      }
      this.nextLoading = true;
      this.tableData = [];
      var transferPara = {
        addresses: [],
        amount: [],
        totalAmount: ethers.utils.parseUnits("0"),
      };
      var arrText = this.form.textarea.split("\n");
      for (const i in arrText) {
        if (arrText[i] == "") {
          continue;
        }
        var tableDataItem = { address: "", amount: 0 };
        var itext = arrText[i].split(",");
        if (itext.length == 2 && parseFloat(itext[1]) > 0) {
          try {
            let hang = parseInt(i) + 1;
            // console.log(i)
            // console.log(hang)
            if (!ethers.utils.isAddress(itext[0].trim())) {
              this.$message({
                title: this.$t("multisend.地址错误"),
                message: `${this.$t("multisend.message1", { hang: hang })}`,
                type: "error",
              });
              this.nextLoading = false;
              return;
            }
            transferPara.addresses.push(
              ethers.utils.getAddress(itext[0].trim())
            );
            tableDataItem.address = ethers.utils.getAddress(itext[0].trim());
            tableDataItem.amount = itext[1].trim();
            this.tableData.push(tableDataItem);
            var amount = ethers.utils.parseUnits(
              itext[1].trim(),
              this.form.selectToken.decimals
            );
            transferPara.amount.push(amount);
            transferPara.totalAmount = transferPara.totalAmount.add(amount);
          } catch (e) {
            this.$message({
              title: this.$t("multisend.格式错误"),
              message: this.$t("multisend.message2"),
              type: "error",
            });
            this.nextLoading = false;
            console.log(e);
            return;
          }
        }
      }
      this.transferPara = transferPara;
      this.form.approve = transferPara.totalAmount;
      this.showApprove = ethers.utils.formatUnits(
        transferPara.totalAmount,
        this.form.selectToken.decimals
      );

      this.checkApprove(store.state.user.provider, this.form.selectToken).then(
        (res) => {
          // console.log("ddddd",res)
          // console.log(res);
          // console.log("d",this.disableAppove)
          this.form.TxInfo = true;
          this.form.nextSwich = false;
          this.disableAppove = true;
          this.sendLoading = false;
          if (res) {
            this.disableAppove = false;
          }
        }
      );
    },
    lastStep() {
      this.form.TxInfo = false;
      this.form.nextSwich = true;
      this.nextLoading = false;
      this.sendLoading = false;
    },
    handleSelect(item) {
      this.form.selectToken = item;
      // console.log(this.form.selectToken)
    },
    async randomAddress() {
      this.raddressLoading = true;
      let str = "";
      this.message = this.$t("multisend.生产地址中");
      for (let i = 0; i < this.randonSet.addressNum; i++) {
        const randomWallet = ethers.Wallet.createRandom();
        let address = randomWallet.address;
        let amount = this.myRandom(this.randonSet.min, this.randonSet.max);
        str += address + "," + amount + "\n";
        console.log(i)
        await new Promise((resolve) => setTimeout(resolve, 1));
      }
      this.dialogVisible = false;
      this.raddressLoading = false;
      this.message = "";
      this.form.textarea = str;
    },
    myRandom(m, n) {
      //如果m大于n，则交换m和n的值
      // console.log(m, n);
      const min = Math.min(m, n);
      const max = Math.max(m, n);
      const random = min + Math.random() * (max - min);
      return parseFloat(random.toFixed(2));
      // if (m > n) {
      //   [m, n] = [n, m];
      // }
      // //如果m等于n，则返回m
      // if (m === n) {
      //   return m;
      // }
      // //如果m和n小于或等于0，则返回1
      // if (m < 0 || n < 0) {
      //   return 0;
      // }
      // // 如果n 和 m 是小数 则返回小数
      // if (m == 0 && n == 1) {
      //   let randomNumber;
      //   do {
      //     randomNumber = parseFloat(Math.random().toFixed(2));
      //   } while (randomNumber === 0);
      //   return randomNumber;
      // }
      // console.log(Number(m + Math.random() * (n - m)))
      // if (n - m <= 1) {
      //   return parseFloat(Number(m + Math.random() * (n - m)).toFixed(2));
      // }

      // //返回m到n之间的随机数
      // // return Math.random() * (n - m + 1) + m;
      // return parseFloat(Number(Math.random() * (n - m + 1) + m).toFixed(2));
    },
    getErc20Contract() {
      this.nextLoading = true;
      this.getErc20Token(this.state).then((res) => {
        this.form.selectToken = res;
        this.tokenList.unshift(res);
        this.nextLoading = false;
      });
    },
    checkSelect() {
      if (this.form.selectToken.length == 0) {
        this.$message({
          type: "error",
          message: this.$t("multisend.请选择token"),
        });
        return false;
      }
      return true;
    },
    send() {
      this.approveLoading = false;
      if (!this.checkSelect()) {
        return;
      }
      if (this.transferPara.addresses.length == 0) {
        return;
      }
      this.sendLoading = true;
      this.checkApprove(store.state.user.provider, this.form.selectToken).then(
        (res) => {
          if (this.showApprove > this.form.selectToken.balance) {
            this.$message({
              type: "error",
              message: this.$t("multisend.message4"),
            });
            return;
          }
          if (!res) {
            this.$message({
              type: "error",
              message: this.$t("multisend.message3"),
            });
            this.disableAppove = true;
            return;
          }
          if (
            this.form.selectToken.address ==
              "******************************************" ||
            this.form.selectToken.address ==
              "******************************************"
          ) {
            this.multiTransferETH()
              .then((res) => {
                console.log(res);
                this.sendLoading = false;
                this.$message({
                  type: "success",
                  message: this.$t("multisend.转账成功") + res["hash"],
                });
              })
              .catch((err) => {
                this.sendLoading = false;
                console.log(err);
                if (err.message.includes("insufficient funds for transfer")) {
                  this.$message({
                    type: "error",
                    message: this.$t("multisend.转账失败余额不足"),
                  });
                  return;
                }
                this.$message({
                  type: "error",
                  message: this.$t("multisend.转账失败") + err.message,
                });
              });
            return;
          } else {
            this.multiTransferToken()
              .then((res) => {
                this.sendLoading = false;
                console.log(res);
                this.$message({
                  type: "success",
                  message: this.$t("multisend.转账成功") + res["hash"],
                });
              })
              .catch((err) => {
                this.sendLoading = false;
                console.log(
                  "error in send token",
                  err.message.includes("transfer amount exceeds balance")
                );
                if (
                  err.message.includes("insufficient funds for transfer") ||
                  err.message.includes("transfer amount exceeds balance")
                ) {
                  this.$message({
                    type: "error",
                    message: this.$t("multisend.转账失败余额不足"),
                  });
                  return;
                }
                this.$message({
                  type: "error",
                  message: this.$t("multisend.转账失败") + err.message,
                });
              });
          }
        }
      );
    },
    async getFee() {
      const abi = multisendAbi;
      const provider = store.state.user.provider;
      const signer = provider.getSigner(store.state.user.address);
      const mutisend = new ethers.Contract(
        sendList[store.state.user.chainId].address,
        abi,
        signer
      );
      var res = await mutisend.getCoinCost();
      this.feeAmount = res;
      return res;
    },
    async getErc20Token(address) {
      const abi = [
        "function balanceOf(address owner) view returns (uint256)",
        "function decimals() view returns (uint8)",
        "function symbol() view returns (string)",
      ];
      const ContractAddress = ethers.utils.getAddress(address);
      const provider = store.state.user.provider;
      const signer = provider.getSigner(store.state.user.address);
      const erc20 = new ethers.Contract(ContractAddress, abi, signer);
      const symbol = await erc20.symbol();
      const decimals = await erc20.decimals();
      const balance = await erc20.balanceOf(store.state.user.address);
      var res = {
        value: symbol + " : " + address,
        address: address,
        balance: balance / 10 ** decimals,
        decimals: decimals,
        symbol: symbol,
      };
      return res;
      // this.tokenList.push(temp);
    },
    async multiTransferETH() {
      const abi = multisendAbi;
      const provider = store.state.user.provider;
      const gasPrice = await provider.getGasPrice();
      const signer = provider.getSigner(store.state.user.address);
      const mutisend = new ethers.Contract(
        sendList[store.state.user.chainId].address,
        abi,
        signer
      );
      var value = this.form.approve.add(this.feeAmount);
      // console.log(value.toString());
      // const gasEstimate = await mutisend.estimateGas.multiTransferETH(
      //   this.transferPara.addresses,
      //   this.transferPara.amount,
      //   { value: value }
      // );
      console.log(gasPrice.toString());
      var res = await mutisend.multiTransferETH(
        this.transferPara.addresses,
        this.transferPara.amount,
        { value: value, gasPrice: gasPrice }
      );
      await provider.waitForTransaction(res.hash, 1);
      return res;
    },
    async multiTransferToken() {
      const abi = multisendAbi;
      const provider = store.state.user.provider;
      const gasPrice = await provider.getGasPrice();
      console.log("multiTransferToken gasPrice", gasPrice.toString());
      const signer = provider.getSigner(store.state.user.address);
      const ContractAddress = ethers.utils.getAddress(
        this.form.selectToken.address
      );
      const mutisend = new ethers.Contract(
        sendList[store.state.user.chainId].address,
        abi,
        signer
      );
      // const gasEstimate = await mutisend.estimateGas.multiTransferToken(
      //   ContractAddress,
      //   this.transferPara.addresses,
      //   this.transferPara.amount
      // );
      // console.log(gasEstimate.toString());
      var res = await mutisend.multiTransferToken(
        ContractAddress,
        this.transferPara.addresses,
        this.transferPara.amount,
        {
          value: this.feeAmount,
          gasPrice: gasPrice,
          // gasLimit: gasEstimate,
        }
      );
      await provider.waitForTransaction(res.hash, 1);
      return res;
    },
    // approve 金额
    doApprove() {
      if (!this.checkSelect()) {
        return;
      }
      this.approveLoading = true;
      this.approve(store.state.user.provider, this.form.selectToken)
        .then((res) => {
          if (res == 2) {
            this.$message({
              type: "success",
              message: this.$t("multisend.无需授权可以直接转账"),
            });
            return;
          }
          if (res == 1) {
            this.$message({
              type: "success",
              message: this.$t("multisend.授权额度充足可以直接转账"),
            });
            this.disableAppove = false;
            this.sendLoading = false;
            return;
          }
          this.disableAppove = false;
          this.approveLoading = false;
          this.$message({
            type: "success",
            message: this.$t("multisend.完成授权") + res["hash"],
          });
        })
        .catch((err) => {
          console.log(err);
          this.$message({
            type: "error",
            message: this.$t("multisend.授权失败"),
          });
          this.disableAppove = false;
          this.approveLoading = false;
        });
    },

    async approve(provider, selectToken) {
      const abi = [
        "function balanceOf(address owner) view returns (uint256)",
        "function decimals() view returns (uint8)",
        "function symbol() view returns (string)",
        "function transfer(address to, uint amount) returns (bool)",
        "function approve(address spender, uint256 amount) external returns (bool)",
        "function allowance(address owner, address spender) external view returns (uint256)",
      ];
      if (
        selectToken.address == "******************************************" ||
        selectToken.address == "******************************************"
      ) {
        return 2;
      }
      const ContractAddress = ethers.utils.getAddress(selectToken.address);
      const signer = provider.getSigner(store.state.user.address);
      const erc20 = new ethers.Contract(ContractAddress, abi, signer);
      // return
      // console.log(store.state.user)
      // console.log(sendList[store.state.user.chainId].address)
      var getAllowance = await erc20.allowance(
        store.state.user.address,
        sendList[store.state.user.chainId].address
      );
      // console.log(sendList[store.state.user.chainId].address)
      // console.log(getAllowance)
      if (getAllowance.gte(this.form.approve)) {
        // console.log(123)
        return 1;
      }
      // const decimals = await erc20.decimals();
      var balance = await erc20.balanceOf(signer.getAddress());
      balance = ethers.utils.formatEther(balance);
      var unlimited =
        "115792089237316195423570985008687907853269984665640564039457584007913129639935";
      var approveAmount = this.form.approve;
      if (this.form.radio == "0") {
        approveAmount = unlimited;
      }

      var res = await erc20.approve(
        sendList[store.state.user.chainId].address,
        approveAmount
      );
      // console.log(res)
      await provider.waitForTransaction(res.hash, 1);
      // console.log("receipt",receipt)
      return res;
    },
    async checkApprove(provider, selectToken) {
      const abi = [
        "function balanceOf(address owner) view returns (uint256)",
        "function decimals() view returns (uint8)",
        "function symbol() view returns (string)",
        "function transfer(address to, uint amount) returns (bool)",
        "function approve(address spender, uint256 amount) external returns (bool)",
        "function allowance(address owner, address spender) external view returns (uint256)",
      ];
      if (
        selectToken.address == "******************************************" ||
        selectToken.address == "******************************************"
      ) {
        return true;
      }
      const ContractAddress = ethers.utils.getAddress(selectToken.address);
      const signer = provider.getSigner(store.state.user.address);
      const erc20 = new ethers.Contract(ContractAddress, abi, signer);
      var getAllowance = await erc20.allowance(
        store.state.user.address,
        sendList[store.state.user.chainId].address
      );
      if (getAllowance.gte(this.form.approve)) {
        return true;
      }
      return false;
    },
  },
};
</script>

<style scoped>
.line {
  text-align: center;
}
.j-input {
  width: 100%;
}
.clear-button {
  padding-right: 10%;
}
.card-row-right .card-row-left {
  font-size: 12px;
}
.card-row-right {
  text-align: left;
}
.card-row-left {
  text-align: right;
}

.clearfix {
  padding: 2px !important;
  font-size: 15px;
}
.jiaocheng {
  margin-top: 0.5rem;
}
@media screen and (max-width: 500px) {
  .main {
    text-align: center;
    padding-left: 0;
    padding-right: 0;
  }
  .jiaocheng {
    margin-top: 0.1rem;
  }
  .p-title {
    display: block;
    margin-block-start: 0em;
    margin-block-end: 0em;
    margin-inline-start: 0px;
    margin-inline-end: 0px;
  }
  .header-span {
    font-size: 14px;
  }
}
@media screen and (min-width: 500px) {
  .header {
    text-align: center;
    margin-top: 20px;
    font-size: larger;
  }
  .main {
    text-align: center;
    padding-left: 20%;
    padding-right: 20%;
  }
}

.dialog-form-item {
}
</style>
