<template>
  <div class="app-container">
    <div class="inner">
      <el-alert
        v-show="support"
        :title="$t('liquidity.暂不支持此链')"
        type="error"
        :description="$t('liquidity.联系定制')"
        center
        :closable="false"
        show-icon
      >
      </el-alert>
      <el-header class="header" style="margin-bottom: 25px">
        <p>
          {{ $t("liquidity.创建并买入流动池") }}
          <a
            style="
              margin-left: 10px;
              font-size: 12px;
              font-weight: 400;
              color: #409eff;
            "
            :href="helpURL"
            target="_blank"
            >{{ $t("coinRelease.common.教程") }}</a
          >
        </p>
        <p style="font-size: 14px; font-weight: 300">
          {{ $t("liquidity.捆绑说明") }}
        </p>
      </el-header>
      <el-main class="main">
        <el-form>
          <el-form-item :label="$t('liquidity.加池类型')">
            <el-radio-group v-model="poolType">
              <el-radio label="v2">{{ $t("liquidity.PancakeV2") }}</el-radio>
              <el-radio label="v3" disabled>{{
                $t("liquidity.PancakeV3稳定池")
              }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item :label="$t('liquidity.选择底池代币')">
            <el-select
              v-model="selectCurrency"
              @change="getCurrency"
              :placeholder="$t('liquidity.请选择')"
            >
              <el-option
                v-for="item in currencyOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
                <span style="float: left">{{ item.label }}</span>
              </el-option>
              <el-option
                :label="$t('liquidity.其他代币')"
                value="1"
              ></el-option>
            </el-select>
            <el-input
              v-if="otherCurrency"
              v-model="currency"
              placeholder="0x..."
              style="margin-top: 10px"
            />
          </el-form-item>
          <el-form-item :label="$t('liquidity.加池代币地址')">
            <el-input
              v-model="tokenAddress"
              :placeholder="$t('liquidity.请输入代币地址')"
            />
          </el-form-item>
          <el-form-item :label="$t('liquidity.创建钱包私钥')">
            <el-input
              v-model="addprivateKey"
              :placeholder="$t('liquidity.请输入私钥')"
            />
          </el-form-item>
          <el-row type="flex" justify="space-around">
            <el-button v-if="checkLoading" :loading="true" type="primary">{{
              $t("liquidity.请稍后")
            }}</el-button>

            <el-button v-else type="primary" @click="checkToken">{{
              $t("liquidity.查询代币")
            }}</el-button>
          </el-row>
          <el-form-item :label="$t('liquidity.创建钱包')">
            <el-row>
              <el-tooltip placement="top" effect="light">
                <div style="font-size: 14px; font-weight: 300" slot="content">
                  {{ $t("liquidity.创建钱包说明") }}<br /><br />
                </div>

                <i class="el-icon-warning-outline" />
              </el-tooltip>
            </el-row>
            <el-row style="margin-top: 5px">
              <el-col :xs="4" :sm="3" style="margin-top: 10px"
                >{{ $t("liquidity.钱包地址") }}:</el-col
              >

              <el-col :xs="24" :sm="10" style="margin-top: 10px">
                <div>
                  {{ this.getShortAddr(addAddress) }}
                  <i
                    class="el-icon-copy-document"
                    style="
                      margin-left: 10px;
                      font-size: 12px;
                      font-weight: 400;
                      color: #409eff;
                    "
                    @click="copy_str(addAddress)"
                    >{{ $t("liquidity.复制") }}</i
                  >
                </div>
              </el-col>
              <el-col
                :xs="12"
                :sm="5"
                style="
                  margin-top: 10px;
                  font-size: 12px;

                  margin-left: 10px;
                "
                >{{ chainSymbol }}{{ $t("liquidity.余额") }}:{{
                  " " + getAddBalance
                }}
              </el-col>
            </el-row>
          </el-form-item>

          <el-form-item :label="$t('liquidity.加池数量')">
            <el-row>
              <el-tooltip placement="top" effect="light">
                <div style="font-size: 14px; font-weight: 300" slot="content">
                  {{ $t("liquidity.加池数量说明") }}<br /><br />
                  {{ $t("liquidity.请先查询代币获取代币信息") }}<br /><br />
                </div>

                <i class="el-icon-warning-outline" />
              </el-tooltip>
            </el-row>
            <el-row style="margin-top: 5px">
              <el-col :xs="4" :sm="3" style="margin-top: 10px"
                >{{ getTokenName }}:</el-col
              >

              <el-col :xs="24" :sm="10" style="margin-top: 10px">
                <el-input
                  v-model="addTokenNum"
                  @input="handleAddToken()"
                  oninput="value=value.replace(/[^0-9.]/g,'')"
                  placeholder="0.000000000000000001"
                >
                </el-input>

                <i
                  class="el-icon-wallet"
                  style="
                    margin-left: 10px;
                    font-size: 12px;
                    font-weight: 400;
                    color: #409eff;
                  "
                  @click="addAll(0)"
                  >{{ $t("liquidity.全部") }}</i
                >
              </el-col>
              <el-col
                :xs="12"
                :sm="5"
                style="
                  margin-top: 10px;
                  font-size: 12px;

                  margin-left: 10px;
                "
                >{{ $t("liquidity.余额") }}:{{ " " + getTokenBalance }}
              </el-col>
            </el-row>
            <el-row style="margin-top: 5px">
              <el-col :xs="4" :sm="3" style="margin-top: 10px"
                >{{ getCurrencyName }}:</el-col
              >

              <el-col :xs="24" :sm="10" style="margin-top: 10px">
                <el-input
                  v-model="addCurrencyNum"
                  @input="handleAddCurrency()"
                  oninput="value=value.replace(/[^0-9.]/g,'')"
                  placeholder="0.000000000000000001"
                >
                </el-input>
                <i
                  class="el-icon-wallet"
                  style="
                    margin-left: 10px;
                    font-size: 12px;
                    font-weight: 400;
                    color: #409eff;
                  "
                  @click="addAll(1)"
                  >{{ $t("liquidity.全部") }}</i
                >
              </el-col>
              <el-col
                :xs="12"
                :sm="5"
                style="
                  margin-top: 10px;
                  font-size: 12px;

                  margin-left: 10px;
                "
                >{{ $t("liquidity.余额") }}:{{ " " + getCurrencyBalance }}
              </el-col>
            </el-row>
          </el-form-item>

          <el-form-item :label="$t('liquidity.预估价格')">
            {{ getPrice }} {{ "  " }} {{ getCurrencyName }}
          </el-form-item>
          <el-form-item
            class="importTable"
            :label="$t('liquidity.捆绑买入地址')"
          >
            <el-row>
              <i
                class="el-icon-upload2"
                style="
                  margin-left: 10px;
                  font-size: 14px;
                  font-weight: 400;
                  color: #409eff;
                "
                @click="dialogImportVisible = true"
                >{{ $t("liquidity.批量导入私钥") }}</i
              >
            </el-row>
            <el-dialog
              :title="$t('liquidity.批量导入私钥')"
              :visible.sync="dialogImportVisible"
              :close-on-click-modal="false"
              width="50%"
              center
            >
              <div style="font-size: 14px; font-weight: 300">
                {{ $t("liquidity.每行一个私钥") }}
              </div>

              <div class="input-wrapper">
                <div class="editor">
                  <!-- 行号区域 -->
                  <div class="line-numbers">
                    <div
                      v-for="(line, index) in lineCount"
                      :key="index"
                      :class="{ 'error-line': errorLines.includes(index) }"
                    >
                      {{ index + 1 }}
                    </div>
                  </div>

                  <!-- 输入区域 -->
                  <textarea
                    v-model="privateText"
                    @scroll="syncScroll"
                    @input="handleInput"
                    ref="textarea"
                    wrap="off"
                    spellcheck="false"
                    placeholder="ca9d648aaba552be602fc59e766801c0ed0cf6b15b95fa5d85fb7a567fc61ada"
                  ></textarea>
                </div>

                <!-- 错误提示 -->
                <p class="error-msg" v-if="errorLines.length">
                  {{
                    $t("liquidity.私钥格式错误", {
                      index: errorLines.map((i) => i + 1).join("、"),
                    })
                  }}
                </p>
                <p style="font-size: 14px; font-weight: 300">
                  {{ $t("liquidity.点击导入将自动移除错误重复私钥") }}
                </p>
              </div>

              <span slot="footer" class="dialog-footer">
                <el-button plain @click="dialogImportVisible = false">{{
                  $t("coinRelease.common.取消")
                }}</el-button>
                <el-button
                  v-if="importLoading"
                  :loading="true"
                  type="primary"
                  plain
                  >{{ $t("liquidity.请稍后") }}</el-button
                >
                <el-button v-else type="primary" plain @click="importWallets">{{
                  $t("liquidity.导入")
                }}</el-button>
              </span>
            </el-dialog>

            <table cellpadding="10" cellspacing="0" width="95%">
              <thead>
                <tr>
                  <th>#</th>
                  <th>
                    <span class="required">*</span> {{ $t("liquidity.私钥") }}
                  </th>
                  <th>{{ $t("liquidity.钱包地址") }}</th>
                  <th>
                    <span style="font-size: 12px; font-weight: 400">{{
                      chainSymbol
                    }}</span>
                    {{ $t("liquidity.余额") }}
                    <i
                      class="el-icon-refresh"
                      style="
                        margin-left: 5px;
                        font-size: 12px;
                        font-weight: 400;
                        color: #409eff;
                      "
                      @click="checkWalletBalance"
                    ></i>
                  </th>
                  <th>
                    <span class="required">*</span>
                    {{ $t("liquidity.买入金额") }}
                    <span style="font-size: 12px; font-weight: 400">{{
                      chainSymbol
                    }}</span>
                  </th>

                  <th>{{ $t("liquidity.操作") }}</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(wallet, index) in wallets" :key="wallet.index">
                  <td>{{ index + 1 }}</td>
                  <td>
                    <div
                      style="
                        display: flex;
                        justify-content: space-between;
                        align-items: center;

                        height: 100%; /* Ensure the td takes full height */
                      "
                    >
                      <input
                        v-model="wallet.privateKey"
                        :placeholder="$t('liquidity.请输入私钥')"
                        @input="handleOneKey(index)"
                        style="
                          flex: 1;
                          margin-right: 10px;
                          border: none;
                          height: 100%;
                        "
                      />
                      <i
                        v-show="wallet.privateKey"
                        class="el-icon-error"
                        style="font-size: 14px; color: rgb(98, 96, 96)"
                        @click="handleDeleteKey(index)"
                      ></i>
                    </div>
                  </td>
                  <td>
                    {{ getShortAddr(wallet.walletAddress)
                    }}<i
                      v-show="wallet.walletAddress != null"
                      class="el-icon-copy-document"
                      style="
                        margin-left: 5px;
                        font-size: 12px;
                        font-weight: 400;
                        color: #409eff;
                      "
                      @click="copy_str(wallet.walletAddress)"
                      >{{ $t("liquidity.复制") }}</i
                    >
                  </td>
                  <td v-if="new Number(wallet.balance) > 0.01">
                    {{ wallet.balance || "-" }}
                  </td>
                  <td v-else style="color: red">
                    {{ wallet.balance || "-" }}
                  </td>
                  <td>
                    <div
                      style="
                        display: flex;
                        justify-content: space-between;
                        align-items: center;

                        height: 100%; /* Ensure the td takes full height */
                      "
                    >
                      <input
                        v-model="wallet.amount"
                        :placeholder="$t('liquidity.请输入买入数量')"
                        @input="handleBuyAmount(index)"
                        type="float"
                        min="0"
                        style="
                          flex: 1;
                          margin-right: 10px;
                          border: none;
                          height: 100%;
                        "
                      />
                      <el-button
                        class="moveButton"
                        size="mini"
                        type="plain"
                        @click="setMax(index)"
                        >{{ $t("liquidity.全部") }}</el-button
                      >
                    </div>
                  </td>
                  <td>
                    <el-button
                      class="moveButton"
                      size="small"
                      type="plain"
                      v-if="wallets.length > 1"
                      @click="removeRow(index)"
                    >
                      ➖
                    </el-button>
                    <el-button
                      class="moveButton"
                      size="small"
                      type="plain"
                      v-if="index === wallets.length - 1"
                      @click="addRow"
                    >
                      ➕
                    </el-button>
                  </td>
                </tr>
              </tbody>
            </table>
          </el-form-item>
        </el-form>
        <div style="font-size: 14px; font-weight: 300; margin-top: 30px">
          <p>
            <i
              class="el-icon-warning"
              style="font-size: 14px; font-weight: 400; color: #409eff"
            ></i>
            {{
              $t("liquidity.捆绑买入地址说明", {
                chainSymbol: chainSymbol,
              })
            }}
          </p>
          <p>
            <i
              class="el-icon-warning"
              style="font-size: 14px; font-weight: 400; color: #409eff"
            ></i>
            {{ $t("liquidity.有限制交易功能的代币捆绑") }}
          </p>
          <p>
            <i
              class="el-icon-warning"
              style="font-size: 14px; font-weight: 400; color: #409eff"
            ></i>
            {{ $t("liquidity.此功能最多支持") }}
          </p>
        </div>

        <el-row type="flex" justify="space-around" style="margin-top: 30px">
          <div>
            <el-button v-if="addLoading" :loading="true" type="primary">{{
              $t("liquidity.请稍后")
            }}</el-button>
            <el-button v-else type="primary" @click="onSubmit">{{
              $t("liquidity.立即加池")
            }}</el-button>
            <span style="font-size: 12px; margin-left: 10px"
              >{{ $t("coinRelease.common.费用") }}:{{ getCostAndSymbol }}/{{
                $t("liquidity.钱包地址")
              }}</span
            >
          </div>
        </el-row>
        <el-dialog
          :title="$t('liquidity.创建流动池')"
          :visible.sync="dialogVisible"
          :close-on-click-modal="false"
          show-close
        >
          <el-collapse v-model="activeNames">
            <el-collapse-item :title="$t('liquidity.预估消耗')" name="1">
              <div style="font-size: 14px; margin-top: 10px">
                <span style="color: red">{{ addTokenNum }} </span>
                {{ "  " + tokenName }}
              </div>

              <div
                v-show="!currencyIsEth"
                style="font-size: 14px; margin-top: 10px"
              >
                <span style="color: red">{{ addCurrencyNum }} </span>
                {{ "  " + currencyName }}
              </div>

              <div style="font-size: 14px; margin-top: 10px">
                <span style="color: red">{{ ETHCost }} </span>
                {{ "  " + chainSymbol }}
              </div>
              <div style="font-size: 14px; margin-top: 10px">
                {{ $t("liquidity.请确保钱包余额充足余额不足将")
                }}<span style="color: red">{{ $t("liquidity.加池失败") }}!</span
                >！
              </div>
            </el-collapse-item>
            <el-collapse-item :title="$t('liquidity.池子地址')" name="2">
              <div style="font-size: 16px; margin-top: 10px">
                {{ getPoolAddress }}
                <i
                  v-show="poolAddress != null"
                  class="el-icon-copy-document"
                  style="
                    margin-left: 10px;
                    font-size: 12px;
                    font-weight: 400;
                    color: #409eff;
                  "
                  @click="copy_str(poolAddress)"
                  >{{ $t("liquidity.复制") }}</i
                >
              </div>
            </el-collapse-item>
          </el-collapse>
          <span slot="footer" class="dialog-footer">
            <el-button v-show="manageCenter" @click="enterManage">{{
              $t("liquidity.前往管理流动性")
            }}</el-button>
            <el-button v-if="addLoading" type="primary" :loading="true">{{
              $t("liquidity.请稍后")
            }}</el-button>
            <el-button v-else-if="manageCenter" type="primary" disabled>{{
              $t("liquidity.加池完成")
            }}</el-button>
            <el-button v-else type="primary" @click="checkParamsAdd">{{
              $t("liquidity.立即加池")
            }}</el-button>
          </span>
        </el-dialog>
      </el-main>
    </div>
  </div>
</template>

<script>
import CoinData from "@/contracts/PancakeERC20.json";
import routerABI from "@/contracts/PancakeRouter.json";
import factoryABI from "@/contracts/PancakeFactory.json";
import lpAddParam from "@/contracts/lpAddParam.json";
import chainParams from "@/contracts/coinReleaseParams.json";
import store from "@/store";
import axios from "axios";
const { ethers, BigNumber } = require("ethers");
const supportChain = [56, 97];
const tokenABI = [
  "function balanceOf(address owner) view returns (uint256)",
  "function decimals() view returns (uint8)",
  "function symbol() view returns (string)",
  "function transfer(address to, uint amount) returns (bool)",
  "function approve(address spender, uint256 amount) external returns (bool)",
  "function allowance(address owner, address spender) external view returns (uint256)",
];
export default {
  name: "Dashboard",
  data() {
    return {
      CoinData,
      factoryABI,
      routerABI,
      chainParams,
      lpAddParam,
      store,
      support: null,
      supportChain,
      activeNames: ["1", "2"],
      helpURL:
        this.$i18n.locale == "en-US"
          ? "https://help.pandatool.org/english/createtoken/createbuy"
          : "https://help.pandatool.org/createtoken/createbuy",
      tokenABI,
      baseAddress: null,
      bundleRPC: null,
      tipAddress: null,
      chainSymbol: null,
      Fee: null,
      provider: null,
      poolType: "v2",
      routerAddress: null,
      factoryAddress: null,
      poolAddress: null,
      poolABI: null,
      added: false,
      selectSwap: null,
      swapOptions: [],
      otherSwap: null,

      selectCurrency: null,
      currencyOptions: [],
      otherCurrency: null,
      currency: null,
      currencyIsEth: false,
      currencyName: null,
      currencyBalance: null,
      currecnyDecimals: null,

      tokenAddress: null,
      tokenName: null,
      tokenBalance: null,
      tokenDecimals: null,

      addprivateKey: null,
      addAddress: null,
      addBalance: null,

      existingKeys: new Set(),
      wallets: [
        {
          privateKey: "",
          walletAddress: null,
          balance: "",
          amount: "",
          nonce: null,
        },
      ],

      privateText: "",
      errorLines: [],

      addTokenNum: null,
      addCurrencyNum: null,
      ETHCost: null,

      isApprovedToken: false,
      isApprovedCurrency: false,
      approvedTokenLoading: false,
      approvedCurrencyLoading: false,
      checkLoading: false,
      dialogVisible: false,
      addLoading: false,
      manageCenter: false,
      dialogImportVisible: false,
      importLoading: false,
    };
  },
  created() {
    let Inval = setInterval(() => {
      if (store.state.user.chainId) {
        window.clearInterval(Inval);
        const chainId = store.state.user.chainId;
        this.provider = store.state.user.provider;
        if (supportChain.includes(chainId)) {
          this.support = false;
        } else {
          this.support = true;
        }
        const selectChainParams = chainParams[chainId][6];
        this._WETH = ethers.utils.getAddress(selectChainParams._WETH);
        this.baseAddress = lpAddParam[chainId];
        this.routerAddress = this.baseAddress.v2Router;
        this.factoryAddress = this.baseAddress.v2Factory;
        this.Fee = this.baseAddress.v2BuddleFee;
        this.tipAddress = this.baseAddress.tipAddress;
        this.bundleRPC = this.baseAddress.bunddleRPC;
        this.chainSymbol = selectChainParams.chainSymbol;
        this.poolABI = CoinData.abi;

        for (let i = 0; i < selectChainParams.currencyOptions.length; i++) {
          var temAddress = ethers.utils.getAddress(
            selectChainParams.currencyOptions[i].value
          );
          var temParam = {
            label: selectChainParams.currencyOptions[i].label,
            value: temAddress,
          };
          this.currencyOptions.push(temParam);
        }
      }
    }, 1000);
  },
  computed: {
    getTokenName() {
      if (this.tokenName == null) {
        return "???";
      } else {
        return this.tokenName;
      }
    },
    getCurrencyName() {
      if (this.currencyName == null) {
        return "???";
      } else {
        return this.currencyName;
      }
    },
    getTokenBalance() {
      if (this.tokenBalance == null) {
        return "???";
      } else {
        return new Number(
          ethers.utils.formatUnits(this.tokenBalance, this.tokenDecimals)
        )
          .toFixed(6)
          .toString();
      }
    },
    getCurrencyBalance() {
      if (this.currencyBalance == null) {
        return "???";
      } else {
        return new Number(
          ethers.utils.formatUnits(this.currencyBalance, this.currecnyDecimals)
        )
          .toFixed(6)
          .toString();
      }
    },
    getAddBalance() {
      if (this.addBalance == null) {
        return "???";
      } else {
        return new Number(ethers.utils.formatUnits(this.addBalance, 18))
          .toFixed(6)
          .toString();
      }
    },
    getPrice() {
      if (!this.addTokenNum || !this.addCurrencyNum) {
        return "0.00";
      } else {
        return parseFloat(
          new Number(this.addCurrencyNum) / new Number(this.addTokenNum)
        )
          .toFixed(6)
          .toString();
      }
    },
    getCostAndSymbol() {
      return this.Fee + " " + this.chainSymbol;
    },
    getPoolAddress() {
      if (this.poolAddress == null) {
        return this.$t("liquidity.未创建");
      } else {
        return this.poolAddress;
      }
    },
    lineCount() {
      return this.privateText.split("\n").length || 1;
    },
  },
  methods: {
    getShortAddr(address) {
      if (ethers.utils.isAddress(address)) {
        return address.slice(0, 6) + "..." + address.slice(-4);
      } else {
        return "0x??";
      }
    },
    syncScroll() {
      const lineNumbers = this.$el.querySelector(".line-numbers");
      lineNumbers.scrollTop = this.$refs.textarea.scrollTop;
    },
    handleInput() {
      this.validateLines();
    },
    validateLines() {
      const lines = this.privateText.split("\n");
      this.errorLines = [];

      lines.forEach((line, index) => {
        // 示例规则：长度必须为64字符（以太坊私钥示例）
        const isValid = /^[0-9a-fA-F]{64}$/.test(line.trim());
        if (!isValid && line.trim() !== "") {
          this.errorLines.push(index);
        }
      });
    },
    getCurrency(selectCurrency) {
      // console.log("selectCurrency", selectCurrency);

      if (selectCurrency == 1) {
        this.otherCurrency = true;
        this.currency = null;
      } else {
        this.otherCurrency = false;
        this.currency = selectCurrency;

        if (this.currency == this._WETH) {
          this.currencyIsEth = true;
        } else {
          this.currencyIsEth = false;
        }
      }
    },

    async checkToken() {
      this.checkLoading = true;
      if (!this.currency) {
        this.$message({
          type: "error",
          message: this.$t("liquidity.请选择底池代币") + "!",
        });
        this.checkLoading = false;
        return;
      }
      if (this.isValidPrivateKey(this.addprivateKey) == false) {
        this.$message({
          type: "error",
          message: this.$t("liquidity.私钥格式不正确") + "!",
        });
        this.checkLoading = false;
        return;
      } else {
        this.addAddress = new ethers.Wallet(this.addprivateKey).address;
        this.addBalance = await this.provider.getBalance(this.addAddress);
      }
      let isToken = ethers.utils.isAddress(this.tokenAddress);
      let isCurrency = ethers.utils.isAddress(this.currency);
      let tokenCode;
      let currencyCode;

      if (!isToken || !isCurrency) {
        this.$message({
          type: "error",
          message: this.$t("liquidity.代币地址地址不正确") + "!",
        });
        this.checkLoading = false;
        return;
      }
      try {
        tokenCode = await this.provider.getCode(this.tokenAddress);
        currencyCode = await this.provider.getCode(this.currency);
      } catch {
        this.$message({
          type: "error",
          message: this.$t("liquidity.代币地址地址不正确") + "!",
        });
        this.checkLoading = false;
        return;
      }

      if (tokenCode == "0x" || currencyCode == "0x") {
        this.$message({
          type: "error",
          message: this.$t("liquidity.代币地址地址不正确") + "!",
        });
        this.checkLoading = false;
        return;
      }
      this.tokenAddress = ethers.utils.getAddress(this.tokenAddress);
      if (this.poolType == "v2") {
        this.routerAddress = this.baseAddress.v2Router;
        this.factoryAddress = this.baseAddress.v2Factory;
      } else {
        this.routerAddress = this.baseAddress.v3Router;
        this.factoryAddress = this.baseAddress.v3Factory;
      }

      await this.getBasicParams();
      this.checkLoading = false;
    },
    async getBasicParams() {
      const tokenContract = new ethers.Contract(
        this.tokenAddress,
        this.tokenABI,
        this.provider
      );
      tokenContract
        .symbol()
        .then((symbol) => {
          this.tokenName = symbol;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t("liquidity.获取代币简称错误") + "!",
          });
          this.checkLoading = false;
        });
      tokenContract
        .balanceOf(this.addAddress)
        .then((temBalance) => {
          this.tokenBalance = temBalance;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t("liquidity.获取代币余额错误") + "!",
          });
          this.checkLoading = false;
        });
      tokenContract
        .decimals()
        .then((decimals) => {
          this.tokenDecimals = decimals;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t("liquidity.获取代币精度错误") + "!",
          });
          this.checkLoading = false;
        });
      if (this.currencyIsEth) {
        this.currencyName = this.chainSymbol;
        this.currencyDecimals = BigNumber.from(18);
        this.currencyBalance = this.addBalance;
      } else {
        const currencyContract = new ethers.Contract(
          this.currency,
          this.tokenABI,
          this.provider
        );
        currencyContract
          .symbol()
          .then((symbol) => {
            this.currencyName = symbol;
          })
          .catch(() => {
            this.$message({
              type: "danger",
              message: this.$t("liquidity.获取底池代币简称错误") + "!",
            });
            this.checkLoading = false;
          });
        currencyContract
          .balanceOf(this.addAddress)
          .then((temBalance) => {
            this.currencyBalance = temBalance;
          })
          .catch(() => {
            this.$message({
              type: "danger",
              message: this.$t("liquidity.获取底池代币余额错误") + "!",
            });
            this.checkLoading = false;
          });
        currencyContract
          .decimals()
          .then((decimals) => {
            this.currencyDecimals = decimals;
          })
          .catch(() => {
            this.$message({
              type: "danger",
              message: this.$t("liquidity.获取底池代币精度错误") + "!",
            });
            this.checkLoading = false;
          });
      }

      const factoryContract = new ethers.Contract(
        this.factoryAddress,
        this.factoryABI,
        this.provider
      );
      const temPoolAddress = await factoryContract.getPair(
        this.tokenAddress,
        this.currency
      );
      if (temPoolAddress == "******************************************") {
        this.$message({
          type: "success",
          message: this.$t("liquidity.代币正常") + "!",
        });
        this.checkLoading = false;
      } else {
        this.poolAddress = temPoolAddress;
        const poolContract = new ethers.Contract(
          temPoolAddress,
          this.poolABI,
          this.provider
        );
        const reverses = await poolContract.getReserves();
        console.log("reverses", reverses);
        if (
          reverses[0].eq(reverses[1]) &&
          reverses[0].eq(ethers.constants.Zero)
        ) {
          this.$message({
            type: "success",
            message: this.$t("liquidity.代币正常") + "!",
          });
        } else {
          this.added = true;
          this.$message({
            type: "danger",
            message: this.$t("liquidity.流动性池已存在") + "!",
          });
        }
      }
    },
    handleAddToken() {
      const temNum = ethers.utils.parseUnits(
        this.addTokenNum.toString(),
        this.tokenDecimals
      );
      if (temNum.gt(this.tokenBalance)) {
        this.$message({
          type: "danger",
          message: this.$t("liquidity.加池代币数量超过余额") + "!",
        });
      }
      if (!this.addTokenNum || temNum.eq(ethers.constants.Zero)) {
        this.$message({
          type: "danger",
          message: this.$t("liquidity.加池代币数量不能为0") + "!",
        });
      }
    },
    handleAddCurrency() {
      const temNum = ethers.utils.parseUnits(
        this.addCurrencyNum.toString(),
        this.currecnyDecimals
      );
      if (temNum.gt(this.currencyBalance)) {
        this.$message({
          type: "danger",
          message: this.$t("liquidity.加池底池代币数量超过余额") + "!",
        });
      }
      if (temNum.eq(ethers.constants.Zero)) {
        this.$message({
          type: "danger",
          message: this.$t("liquidity.加池底池代币数量不能为0") + "!",
        });
      }
    },
    async checkApproval(tokenAddress, decimals) {
      const ContractAddress = ethers.utils.getAddress(tokenAddress);
      const wallet = new ethers.Wallet(this.addprivateKey, this.provider);
      const erc20 = new ethers.Contract(ContractAddress, tokenABI, wallet);

      var getAllowance = await erc20.allowance(
        wallet.address,
        this.routerAddress
      );
      // getAllowance = ethers.utils.formatUnits(getAllowance, decimals);

      if (getAllowance.gt(ethers.utils.parseEther("100000000000000"))) {
        return true;
      } else {
        return false;
      }
    },
    async getUnSignApprovalTx(tokenAddress) {
      const ContractAddress = ethers.utils.getAddress(tokenAddress);
      const wallet = new ethers.Wallet(this.addprivateKey, this.provider);
      const erc20 = new ethers.Contract(ContractAddress, tokenABI, wallet);
      const unsignApproveTokenTx = await erc20.populateTransaction.approve(
        this.routerAddress,
        ethers.constants.MaxUint256 // Approve the maximum amount
      );
      return unsignApproveTokenTx;
    },
    async onSubmit() {
      if (!this.tokenDecimals) {
        this.addLoading = false;

        this.$message({
          type: "danger",
          message: this.$t("liquidity.请先查询代币") + "!",
        });
        return;
      }
      if (this.added) {
        this.addLoading = false;

        this.$message({
          type: "danger",
          message: this.$t("liquidity.流动性池已存在") + "!",
        });
        return;
      }
      const bigAddToken = ethers.utils.parseUnits(
        this.addTokenNum,
        this.tokenDecimals
      );

      const bigAddCurrency = ethers.utils.parseUnits(
        this.addCurrencyNum,
        this.currecnyDecimals
      );
      if (bigAddToken.eq(ethers.constants.Zero)) {
        this.$message({
          type: "danger",
          message: this.$t("liquidity.加池代币数量不能为0") + "!",
        });
        return;
      }
      if (bigAddToken.eq(ethers.constants.Zero)) {
        this.$message({
          type: "danger",
          message: this.$t("liquidity.加池代币数量不能为0") + "!",
        });
        return;
      }
      if (bigAddCurrency.gt(this.currencyBalance)) {
        this.$message({
          type: "danger",
          message: this.$t("liquidity.加池底池代币数量超过余额") + "!",
        });
        return;
      }
      if (bigAddCurrency.eq(ethers.constants.Zero)) {
        this.$message({
          type: "danger",
          message: this.$t("liquidity.加池底池代币数量不能为0") + "!",
        });
        return;
      }
      this.addLoading = true;
      const minAmount = ethers.utils.parseEther("0.005");

      for (let index = 0; index < this.wallets.length; index++) {
        const wallet = this.wallets[index];
        if (wallet.walletAddress) {
          let walletBalance;
          try {
            walletBalance = ethers.utils.parseEther(wallet.balance.toString());
          } catch {
            this.$message({
              type: "warning",
              message: this.$t("liquidity.请先刷新余额") + "!",
            });
          }

          let walletAmount;
          try {
            walletAmount = ethers.utils.parseEther(wallet.amount.toString());
          } catch {
            this.$message({
              type: "warning",
              message:
                this.$t("liquidity.第id个地址买入数量不正确", {
                  id: index + 1,
                }) + "!",
            });
            this.addLoading = false;
            return;
          }

          if (
            walletAmount.lt(minAmount) ||
            walletAmount.gt(walletBalance.sub(minAmount))
          ) {
            this.addLoading = false;
            this.$message({
              type: "warning",
              message:
                this.$t("liquidity.第id个地址买入数量不正确", {
                  id: index + 1,
                }) + "!",
            });
            this.addLoading = false;
            return;
          }
        } else {
          this.$message({
            type: "warning",
            message: this.$t("liquidity.请移除不正确的捆绑买入地址") + "!",
          });
          this.addLoading = false;
          return;
        }
      }

      this.isApprovedToken = await this.checkApproval(
        this.tokenAddress,
        this.tokenDecimals
      );
      if (this.currencyIsEth) {
        this.isApprovedCurrency = true; // ETH does not require approval
      } else {
        this.isApprovedCurrency = await this.checkApproval(
          this.currency,
          this.currecnyDecimals
        );
      }

      if (this.currencyIsEth) {
        this.ETHCost = (
          new Number(this.Fee) * (this.wallets.length + 1) +
          new Number(this.addCurrencyNum) +
          0.001244
        )
          .toFixed(6)
          .toString();
      } else {
        this.ETHCost = (
          new Number(this.Fee) * (this.wallets.length + 1) +
          0.001244
        )
          .toFixed(6)
          .toString();
      }
      await Promise.all(
        this.wallets.map(async (wallet, index) => {
          if (wallet.walletAddress) {
            let temNonce = await this.provider.getTransactionCount(
              wallet.walletAddress
            );
            wallet.nonce = temNonce;
          } else {
            return; // 跳过错误的私钥
          }
        })
      );

      this.addLoading = false;
      this.dialogVisible = true;
    },
    async checkParamsAdd() {
      this.addLoading = true;
      const addWallet = new ethers.Wallet(this.addprivateKey, this.provider);
      const bigAddToken = ethers.utils.parseUnits(
        this.addTokenNum,
        this.tokenDecimals
      );
      const bigAddCurrency = ethers.utils.parseUnits(
        this.addCurrencyNum,
        this.currecnyDecimals
      );
      const routerContract = new ethers.Contract(
        this.routerAddress,
        this.routerABI,
        addWallet
      );
      const bigFee = ethers.utils
        .parseEther(this.Fee)
        .mul(this.wallets.length + 1);

      let signedTxs = [];
      let addNonce = await this.provider.getTransactionCount(
        addWallet.address,
        "pending"
      );
      const gasPrice = await this.provider.getGasPrice();
      const chainID = (await this.provider.getNetwork()).chainId;

      const txTip = {
        to: this.tipAddress, // 目标地址
        value: ethers.utils.parseEther("0.00005"),
        gasLimit: 100000,
        gasPrice: gasPrice.mul(11).div(10),
        nonce: addNonce,
        chainId: chainID,
      };
      addNonce += 1;
      const signedTipTx = await addWallet.signTransaction(txTip);
      signedTxs.push(signedTipTx);
      const txFee = {
        to: "******************************************", // 目标地址
        value: bigFee,
        gasLimit: 100000,
        gasPrice: gasPrice.mul(11).div(10),
        nonce: addNonce,
        chainId: chainID,
      };
      addNonce += 1;
      const signedFeeTx = await addWallet.signTransaction(txFee);
      signedTxs.push(signedFeeTx);
      const FeeTxHash = ethers.utils.keccak256(signedFeeTx);
      if (!this.isApprovedToken) {
        const unsignApproveTokenTx = await this.getUnSignApprovalTx(
          this.tokenAddress
        );

        const temTx = {
          ...unsignApproveTokenTx,
          gasLimit: 100000, // Reserve 20% of gas
          gasPrice: gasPrice.mul(11).div(10),
          nonce: addNonce,
          chainId: chainID,
        };

        // Sign the transaction
        const signedApproveTokenTx = await addWallet.signTransaction(temTx);
        signedTxs.push(signedApproveTokenTx);
        addNonce += 1;
      }
      if (!this.isApprovedCurrency) {
        const unsignApproveCurrencyTx = await this.getUnSignApprovalTx(
          this.currency
        );

        let temTx = {
          ...unsignApproveCurrencyTx,
          gasLimit: 100000, // Reserve 20% of gas
          gasPrice: gasPrice.mul(11).div(10),
          nonce: addNonce,
          chainId: chainID,
        };

        // Sign the transaction
        const signedApproveCurrencyTx = await addWallet.signTransaction(temTx);
        signedTxs.push(signedApproveCurrencyTx);
        addNonce += 1;
      }
      if (this.currencyIsEth) {
        const unsignedCreateTx =
          await routerContract.populateTransaction.addLiquidityETH(
            this.tokenAddress,
            bigAddToken,
            0,
            0,
            addWallet.address,
            Math.floor(Date.now() / 1000) + 600,
            { value: bigAddCurrency }
          );

        let temTx = {
          ...unsignedCreateTx,
          gasLimit: 10000000, // Reserve 20% of gas
          gasPrice: gasPrice.mul(11).div(10),
          nonce: addNonce,
          chainId: chainID,
        };
        const signedCreateTx = await addWallet.signTransaction(temTx);
        signedTxs.push(signedCreateTx);
        addNonce += 1;
      } else {
        const unsignedCreateTx =
          await routerContract.populateTransaction.addLiquidity(
            this.tokenAddress,
            this.currency,
            bigAddToken,
            bigAddCurrency,
            0,
            0,
            addWallet.address,
            Math.floor(Date.now() / 1000) + 600
          );

        let temTx = {
          ...unsignedCreateTx,
          gasLimit: 10000000, // Reserve 20% of gas
          gasPrice: gasPrice.mul(11).div(10),
          nonce: addNonce,
          chainId: chainID,
        };
        const signedCreateTx = await addWallet.signTransaction(temTx);
        signedTxs.push(signedCreateTx);
        addNonce += 1;
      }

      let path;
      if (this.currencyIsEth) {
        path = [this.currency, this.tokenAddress];
      } else {
        path = [this._WETH, this.currency, this.tokenAddress];
      }

      const swapPromises = this.wallets.map(async (wallet) => {
        if (wallet.walletAddress) {
          let buyWallet = new ethers.Wallet(wallet.privateKey, this.provider);
          let routerContract = new ethers.Contract(
            this.routerAddress,
            this.routerABI,
            buyWallet
          );
          let bigBuyAmount = ethers.utils.parseEther(wallet.amount);
          let unsignedSwapTx =
            await routerContract.populateTransaction.swapExactETHForTokensSupportingFeeOnTransferTokens(
              0, // amountOutMin
              path,
              wallet.walletAddress,
              Math.floor(Date.now() / 1000) + 600,
              { value: bigBuyAmount }
            );

          let swapTx = {
            ...unsignedSwapTx,
            gasLimit: 400000, // Reserve 20% of gas
            gasPrice: gasPrice.mul(11).div(10),
            nonce: wallet.nonce,
            chainId: chainID,
          };

          let signedSwapTx = await buyWallet.signTransaction(swapTx);
          signedTxs.push(signedSwapTx);
        }
      });

      await Promise.all(swapPromises);

      const headers = {
        "Content-Type": "application/json",
      };
      const bundleRequest = {
        jsonrpc: "2.0",
        id: "1",
        method: "eth_sendBundle",
        params: [
          {
            txs: signedTxs,
          },
        ],
      };
      const bundleParams = {
        bundleRPC: this.bundleRPC,
        bundleRequest: bundleRequest,
      };
      axios
        .post(
          "https://solana.pandatool.org/api/bscBundleSender",
          bundleParams,
          headers
        )
        .then(async (response) => {
          console.log("Response:", response.data);
          if (response.data.result && response.data.result.length > 0) {
            this.$message({
              type: "danger",
              message: this.$t("liquidity.已提交等待区块确认") + "!",
            });
            const confirmed = await this.comfirmSig(FeeTxHash);
            if (confirmed) {
              this.$message({
                type: "success",
                message: this.$t("liquidity.加池并买入成功") + "!",
              });
              const factoryContract = new ethers.Contract(
                this.factoryAddress,
                this.factoryABI,
                addWallet
              );
              const temPoolAddress = await factoryContract.getPair(
                this.tokenAddress,
                this.currency
              );
              this.poolAddress = temPoolAddress;
              this.addLoading = false;
              this.manageCenter = true;
            } else {
              this.$message({
                type: "danger",
                message: this.$t("liquidity.加池并买入确认失败") + "!",
              });
            }
          } else {
            this.$message({
              type: "danger",
              message: this.$t("liquidity.捆绑交易失败") + "!",
            });
          }
          this.addLoading = false;
        })
        .catch((error) => {
          console.error(
            "Error sending bundle:",
            error.response ? error.response.data : error.message
          );
          this.addLoading = false;
        });
    },
    async comfirmSig(txHash) {
      const start = Date.now();

      while (Date.now() - start < 12000) {
        const receipt = await this.provider.getTransactionReceipt(txHash);
        if (receipt && receipt.blockNumber) {
          console.log("✅ 已确认: 区块号", receipt.blockNumber);
          return true;
        }

        console.log("等待中...");
        await new Promise((r) => setTimeout(r, 3000)); // 每3秒查一次
      }
      return false;
    },
    addAll(num) {
      if (num == 0) {
        this.addTokenNum = parseFloat(
          this.tokenBalance / 10 ** this.tokenDecimals
        )
          .toFixed(6)
          .toString();
      } else {
        this.addCurrencyNum = parseFloat(
          new Number(this.currencyBalance) /
            10 ** new Number(this.currencyDecimals)
        )
          .toFixed(6)
          .toString();
      }
    },
    enterManage() {
      this.$router.push({
        path: "/LPmanage/",
      });
    },

    copy_str(text) {
      if (navigator.clipboard) {
        // clipboard api 复制
        navigator.clipboard.writeText(text);
      } else {
        var textarea = document.createElement("textarea");
        document.body.appendChild(textarea);
        // 隐藏此输入框
        textarea.style.position = "fixed";
        textarea.style.clip = "rect(0 0 0 0)";
        textarea.style.top = "10px";
        // 赋值
        textarea.value = text;
        // 选中
        textarea.select();
        // 复制
        document.execCommand("copy", true);
        // 移除输入框
        document.body.removeChild(textarea);
      }
      this.$message({
        type: "success",
        message: this.$t("liquidity.已复制") + "!",
      });
    },
    isValidPrivateKey(privateKey) {
      try {
        new ethers.Wallet(privateKey);
        return true;
      } catch (err) {
        return false;
      }
    },
    async importWallets() {
      this.importLoading = true;
      const privateKeys = this.privateText.split("\n").filter((key) => key);
      let temRows = [];
      this.existingKeys = new Set(); // 用于存储已存在的私钥
      await Promise.all(
        privateKeys.map(async (key, index) => {
          if (this.isValidPrivateKey(key)) {
            if (this.existingKeys.has(key)) {
              return; // 跳过重复的私钥
            }
            this.existingKeys.add(key); // 添加到已存在的私钥集合
            const wallet = new ethers.Wallet(key);
            temRows.push({
              privateKey: key,
              walletAddress: wallet.address,
              balance: "",
              amount: "",
            });
          } else {
            return; // 跳过错误的私钥
          }
        })
      );
      // console.log("temRows", temRows);
      this.wallets = temRows;
      this.checkWalletBalance();
      this.importLoading = false;
      this.dialogImportVisible = false;
    },
    checkWalletBalance() {
      // const RPC =
      //   process.env.VUE_APP_ENV == "DEV"
      //     ? process.env.VUE_APP_TESTNET_PRC
      //     : process.env.VUE_APP_MAINNET_PRC;
      // console.log("当前 buddlePRC :", RPC);

      this.wallets.forEach(async (wallet) => {
        if (wallet.privateKey == "" || wallet.walletAddress == null) {
          return; // 跳过没有私钥或私钥不正确的行
        }
        let temBalance = await this.provider.getBalance(wallet.walletAddress);
        wallet.balance = new Number(ethers.utils.formatEther(temBalance))
          .toFixed(4)
          .toString();

        // console.log("钱包地址:", wallet.walletAddress, "余额:", wallet.balance);
      });
    },
    handleBuyAmount(index) {
      if (!this.wallets[index].walletAddress) {
        this.$message({
          type: "warning",
          message: this.$t("liquidity.请输入私钥") + "!",
        });
        return;
      }
      if (!this.wallets[index].balance) {
        this.$message({
          type: "warning",
          message: this.$t("liquidity.请先刷新余额") + "!",
        });
        return;
      }
      if (
        new Number(this.wallets[index].amount) + 0.005 >
        new Number(this.wallets[index].balance)
      ) {
        this.$message({
          type: "error",
          message: this.$t("liquidity.购买金额不能大于钱包余额") + "!",
        });
        return;
      }
      if (this.wallets[index].amount < 0.005) {
        this.$message({
          type: "error",
          message: this.$t("liquidity.购买最小金额") + "!",
        });
        return;
      }
    },
    handleOneKey(index) {
      const privateKey = this.wallets[index].privateKey;
      if (this.isValidPrivateKey(privateKey)) {
        if (this.existingKeys.has(privateKey)) {
          this.$message({
            type: "error",
            message: this.$t("liquidity.私钥已存在") + "!",
          });
          return; // 跳过重复的私钥
        } else {
          this.existingKeys.add(privateKey); // 添加到已存在的私钥集合
        }
        const wallet = new ethers.Wallet(privateKey);
        this.wallets[index].walletAddress = wallet.address;

        this.provider.getBalance(wallet.address).then((balance) => {
          this.wallets[index].balance = new Number(
            ethers.utils.formatEther(balance)
          )
            .toFixed(4)
            .toString();
        });
      } else {
        this.wallets[index].walletAddress = null;
        this.wallets[index].balance = "";
        this.wallets[index].amount = "";
        this.$message({
          type: "error",
          message: this.$t("liquidity.私钥格式不正确") + "!",
        });
      }
    },
    handleDeleteKey(index) {
      this.wallets[index].privateKey = "";
      this.wallets[index].walletAddress = "";
      this.wallets[index].balance = "";
      this.wallets[index].amount = "";
    },
    addRow() {
      const newId = this.wallets.length + 1;
      this.wallets.push({
        privateKey: "",
        walletAddress: "",
        balance: "",
        amount: "",
      });
    },
    removeRow(index) {
      this.wallets.splice(index, 1);
    },
    setMax(index) {
      // 示例：设为模拟余额
      if (!this.wallets[index].balance) {
        this.$message({
          type: "warning",
          message: this.$t("liquidity.请先刷新余额") + "!",
        });
        return;
      }
      if (new Number(this.wallets[index].balance) > 0.01) {
        this.wallets[index].amount = (
          new Number(this.wallets[index].balance) - 0.005
        ).toFixed(4);
      } else {
        this.$message({
          type: "warning",
          message: this.$t("liquidity.余额不足") + "!",
        });
        return;
      }
    },
  },
};
</script>

<style lang="scss">
@media screen and (max-width: 500px) {
}
@media screen and (min-width: 500px) {
  .el-input {
    width: 80%;
  }
  .el-select {
    width: 80%;
  }
}
.input-wrapper {
  width: 100%;
}
.editor {
  display: flex;
  border: 1px solid #ccc;
  height: 200px;
  overflow: hidden;
  border-radius: 6px;
  font-family: monospace;
}
.line-numbers {
  background: #f8f8f8;
  padding: 8px 0;
  text-align: right;
  user-select: none;
  color: #999;
  min-width: 40px;
  border-right: 1px solid #ddd;
  overflow-y: auto;
}
.line-numbers div {
  padding: 0 8px;
  height: 24px;
  line-height: 24px;
}
.line-numbers .error-line {
  background-color: #ff5c5c;
  color: white;
}
textarea {
  flex: 1;
  border: none;
  outline: none;
  resize: none;
  padding: 8px;
  font-size: 14px;
  line-height: 24px;
  white-space: pre;
  overflow-x: hidden;
  overflow-y: scroll;
  word-wrap: normal;
}
.error-msg {
  color: red;
  margin-top: 2px;
  font-size: 14px;
}

.importTable {
  .required {
    color: red;
  }
  table {
    margin-top: 10px;
    border: 0.5px solid rgb(226, 225, 225); /* Added border property */
    border-radius: 5px;

    border-collapse: separate; /* Ensures that internal borders are collapsed */
  }
  th,
  td {
    border: 0.5px solid rgb(226, 225, 225); /* Added internal borders */
  }
  input {
    padding: 5px;
    width: 90%;
  }
  moveButton {
    margin-left: 5px;
  }
}
</style>
