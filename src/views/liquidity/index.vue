<template>
  <div class="app-container">
    <div class="inner">
      <el-alert
        v-show="support"
        :title="$t('liquidity.暂不支持此链')"
        type="error"
        :description="$t('liquidity.联系定制')"
        center
        :closable="false"
        show-icon
      >
      </el-alert>
      <el-header class="header" style="margin-bottom: 25px">
        <p>
          {{ $t("liquidity.创建流动池") }}
          <a
            style="
              margin-left: 10px;
              font-size: 12px;
              font-weight: 400;
              color: #409eff;
            "
            :href="helpURL"
            target="_blank"
            >{{ $t("coinRelease.common.教程") }}</a
          >
        </p>
        <p style="font-size: 14px; font-weight: 300">
          {{ $t("liquidity.创建说明") }}
        </p>
      </el-header>
      <el-main class="main">
        <el-form>
          <el-form-item :label="$t('liquidity.加池类型')">
            <el-radio-group v-model="poolType">
              <el-radio label="v2">{{ $t("liquidity.PancakeV2") }}</el-radio>
              <el-radio label="v3" disabled>{{
                $t("liquidity.PancakeV3稳定池")
              }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item :label="$t('liquidity.选择底池代币')">
            <el-select
              v-model="selectCurrency"
              @change="getCurrency"
              :placeholder="$t('liquidity.请选择')"
            >
              <el-option
                v-for="item in currencyOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
                <span style="float: left">{{ item.label }}</span>
              </el-option>
              <el-option
                :label="$t('liquidity.其他代币')"
                value="1"
              ></el-option>
            </el-select>
            <el-input
              v-if="otherCurrency"
              v-model="currency"
              placeholder="0x..."
              style="margin-top: 10px"
            />
          </el-form-item>
          <el-form-item :label="$t('liquidity.加池代币地址')">
            <el-input
              v-model="tokenAddress"
              :placeholder="$t('liquidity.请输入代币地址')"
            />
          </el-form-item>
          <el-row type="flex" justify="space-around">
            <el-button v-if="checkLoading" :loading="true" type="primary">{{
              $t("liquidity.请稍后")
            }}</el-button>

            <el-button v-else type="primary" @click="checkToken">{{
              $t("liquidity.查询代币")
            }}</el-button>
          </el-row>

          <el-form-item :label="$t('liquidity.加池数量')">
            <el-row>
              <el-tooltip placement="top" effect="light">
                <div style="font-size: 14px; font-weight: 300" slot="content">
                  {{ $t("liquidity.加池数量说明") }}<br /><br />
                  {{ $t("liquidity.请先查询代币获取代币信息") }}<br /><br />
                </div>

                <i class="el-icon-warning-outline" />
              </el-tooltip>
            </el-row>
            <el-row style="margin-top: 5px">
              <el-col :xs="4" :sm="3" style="margin-top: 10px"
                >{{ getTokenName }}:</el-col
              >

              <el-col :xs="24" :sm="10" style="margin-top: 10px">
                <el-input
                  v-model="addTokenNum"
                  @input="handleAddToken()"
                  oninput="value=value.replace(/[^0-9.]/g,'')"
                  placeholder="0.000000000000000001"
                >
                </el-input>

                <i
                  class="el-icon-wallet"
                  style="
                    margin-left: 10px;
                    font-size: 12px;
                    font-weight: 400;
                    color: #409eff;
                  "
                  @click="addAll(0)"
                  >{{ $t("liquidity.全部") }}</i
                >
              </el-col>
              <el-col
                :xs="12"
                :sm="5"
                style="
                  margin-top: 10px;
                  font-size: 12px;

                  margin-left: 10px;
                "
                >{{ $t("liquidity.余额") }}:{{ getTokenBalance }}
              </el-col>
            </el-row>
            <el-row style="margin-top: 5px">
              <el-col :xs="4" :sm="3" style="margin-top: 10px"
                >{{ getCurrencyName }}:</el-col
              >

              <el-col :xs="24" :sm="10" style="margin-top: 10px">
                <el-input
                  v-model="addCurrencyNum"
                  @input="handleAddCurrency()"
                  oninput="value=value.replace(/[^0-9.]/g,'')"
                  placeholder="0.000000000000000001"
                >
                </el-input>
                <i
                  class="el-icon-wallet"
                  style="
                    margin-left: 10px;
                    font-size: 12px;
                    font-weight: 400;
                    color: #409eff;
                  "
                  @click="addAll(1)"
                  >{{ $t("liquidity.全部") }}</i
                >
              </el-col>
              <el-col
                :xs="12"
                :sm="5"
                style="
                  margin-top: 10px;
                  font-size: 12px;

                  margin-left: 10px;
                "
                >{{ $t("liquidity.余额") }}:{{ getCurrencyBalance }}
              </el-col>
            </el-row>
          </el-form-item>

          <el-form-item :label="$t('liquidity.预估价格')">
            {{ getPrice }} {{ "  " }} {{ getCurrencyName }}
          </el-form-item>
          <el-form-item :label="$t('liquidity.授权')">
            <el-row>
              <el-tooltip placement="top" effect="light">
                <div style="font-size: 14px; font-weight: 300" slot="content">
                  {{ $t("liquidity.授权说明") }}<br /><br />

                  {{ $t("liquidity.修复方法需使用合约完成授权选择默认值即可")
                  }}<br /><br />
                </div>
                <i class="el-icon-warning-outline" />
              </el-tooltip>
            </el-row>
            <el-row type="flex" justify="space-around">
              <el-col :xs="8" :sm="4" style="margin-top: 10px">
                <el-button
                  v-if="approvedTokenLoading"
                  :loading="true"
                  type="primary"
                  plain
                  @click="approveToken"
                  size="medium"
                  >{{ $t("liquidity.正在授权") }}{{ getTokenName }}</el-button
                >
                <el-button
                  v-else-if="approvedToken"
                  type="success"
                  plain
                  disabled
                  size="medium"
                  >{{ getTokenName }}{{ $t("liquidity.已授权") }}</el-button
                >
                <el-button
                  v-else
                  type="primary"
                  plain
                  @click="approveToken"
                  size="medium"
                  >{{ $t("liquidity.授权") }}{{ getTokenName }}</el-button
                >
              </el-col>
              <el-col :xs="8" :sm="4" style="margin-top: 10px">
                <el-button
                  v-if="approvedCurrencyLoading"
                  :loading="true"
                  type="primary"
                  plain
                  size="medium"
                  >{{ $t("liquidity.正在授权")
                  }}{{ getCurrencyName }}</el-button
                >
                <el-button
                  v-else-if="approvedCurrency"
                  type="success"
                  plain
                  disabled
                  size="medium"
                  >{{ getCurrencyName }}{{ $t("liquidity.已授权") }}</el-button
                >
                <el-button
                  v-else
                  type="primary"
                  plain
                  @click="approveCurrency"
                  size="medium"
                  >{{ $t("liquidity.授权") }}{{ getCurrencyName }}</el-button
                >
              </el-col>
            </el-row>
          </el-form-item>
        </el-form>
        <p style="font-size: 14px; font-weight: 300">
          {{ $t("liquidity.有限制交易功能的代币") }}
        </p>
        <p style="font-size: 14px; font-weight: 300">
          {{ $t("liquidity.加池合约") }}:{{ this.lpManageAddress }}
          <i
            class="el-icon-copy-document"
            style="
              margin-left: 10px;
              font-size: 12px;
              font-weight: 400;
              color: #409eff;
            "
            @click="copy_str(lpManageAddress)"
            >{{ $t("tools.复制") }}</i
          >
        </p>

        <el-row type="flex" justify="space-around" style="margin-top: 20px">
          <div>
            <el-button v-if="addLoading" :loading="true" type="primary">{{
              $t("liquidity.请稍后")
            }}</el-button>
            <el-button v-else type="primary" @click="onSubmit">{{
              $t("liquidity.立即加池")
            }}</el-button>
            <span style="font-size: 12px; margin-left: 10px"
              >{{ $t("coinRelease.common.费用") }}:{{ getCostAndSymbol }}</span
            >
          </div>
        </el-row>
        <el-dialog
          :title="$t('liquidity.创建流动池')"
          :visible.sync="dialogVisible"
          :close-on-click-modal="false"
          show-close
        >
          <el-collapse v-model="activeNames">
            <el-collapse-item :title="$t('liquidity.预估消耗')" name="1">
              <div style="font-size: 14px; margin-top: 10px">
                <span style="color: red">{{ addTokenNum }} </span>
                {{ "  " + tokenName }}
              </div>

              <div
                v-show="!currencyIsEth"
                style="font-size: 14px; margin-top: 10px"
              >
                <span style="color: red">{{ addCurrencyNum }} </span>
                {{ "  " + currencyName }}
              </div>

              <div style="font-size: 14px; margin-top: 10px">
                <span style="color: red">{{ ETHCost }} </span>
                {{ "  " + chainSymbol }}
              </div>
              <div style="font-size: 14px; margin-top: 10px">
                {{ $t("liquidity.请确保钱包余额充足余额不足将")
                }}<span style="color: red">{{ $t("liquidity.加池失败") }}!</span
                >！
              </div>
            </el-collapse-item>
            <el-collapse-item :title="$t('liquidity.池子地址')" name="2">
              <div style="font-size: 16px; margin-top: 10px">
                {{ getPoolAddress }}
                <i
                  v-show="poolAddress != null"
                  class="el-icon-copy-document"
                  style="
                    margin-left: 10px;
                    font-size: 12px;
                    font-weight: 400;
                    color: #409eff;
                  "
                  @click="copy_str(poolAddress)"
                  >{{ $t("liquidity.复制") }}</i
                >
              </div>
            </el-collapse-item>
          </el-collapse>
          <span slot="footer" class="dialog-footer">
            <el-button
              v-show="manageCenter"
              type="primary"
              plain
              @click="enterManage"
              >{{ $t("liquidity.前往管理流动性") }}</el-button
            >
            <el-button v-if="addLoading" type="primary" :loading="true">{{
              $t("liquidity.请稍后")
            }}</el-button>

            <el-button v-else-if="manageCenter" type="primary" disabled>{{
              $t("liquidity.加池完成")
            }}</el-button>
            <el-button v-else type="primary" @click="checkParamsAdd">{{
              $t("liquidity.立即加池")
            }}</el-button>
          </span>
        </el-dialog>
      </el-main>
    </div>
  </div>
</template>

<script>
import CoinData from "@/contracts/PancakeERC20.json";
import lpManageABI from "@/contracts/lpManageABI.json";
import factoryABI from "@/contracts/PancakeFactory.json";
import lpAddParam from "@/contracts/lpAddParam.json";
import chainParams from "@/contracts/coinReleaseParams.json";
import store from "@/store";
const { ethers, BigNumber } = require("ethers");
const supportChain = [56, 97];
const tokenABI = [
  "function balanceOf(address owner) view returns (uint256)",
  "function decimals() view returns (uint8)",
  "function symbol() view returns (string)",
  "function transfer(address to, uint amount) returns (bool)",
  "function approve(address spender, uint256 amount) external returns (bool)",
  "function allowance(address owner, address spender) external view returns (uint256)",
];
export default {
  name: "Dashboard",
  data() {
    return {
      CoinData,
      factoryABI,
      lpManageABI,
      chainParams,
      lpAddParam,
      store,
      support: null,
      supportChain,
      activeNames: ["1", "2"],
      helpURL:
        this.$i18n.locale == "en-US"
          ? "https://help.pandatool.org/english/createtoken/createliquidity"
          : "https://help.pandatool.org/createtoken/createliquidity",
      tokenABI,
      baseAddress: null,
      chainSymbol: null,
      Fee: null,

      poolType: "v2",
      lpManageAddress: null,
      routerAddress: null,
      factoryAddress: null,
      poolAddress: null,
      poolABI: null,
      added: false,
      selectSwap: null,
      swapOptions: [],
      otherSwap: null,

      selectCurrency: null,
      currencyOptions: [],
      otherCurrency: null,
      currency: null,
      currencyIsEth: false,
      currencyName: null,
      currencyBalance: null,
      currecnyDecimals: null,

      tokenAddress: null,
      tokenName: null,
      tokenBalance: null,
      tokenDecimals: null,

      approvedAmount: 1000000000000,

      addTokenNum: null,
      addCurrencyNum: null,
      ETHCost: null,

      approvedToken: false,
      approvedCurrency: false,
      approvedTokenLoading: false,
      approvedCurrencyLoading: false,
      checkLoading: false,
      dialogVisible: false,
      addLoading: false,
      manageCenter: false,
    };
  },
  created() {
    let Inval = setInterval(() => {
      if (store.state.user.chainId) {
        window.clearInterval(Inval);
        const chainId = store.state.user.chainId;
        if (supportChain.includes(chainId)) {
          this.support = false;
        } else {
          this.support = true;
        }
        const selectChainParams = chainParams[chainId][6];
        this._WETH = ethers.utils.getAddress(selectChainParams._WETH);
        this.baseAddress = lpAddParam[chainId];
        this.routerAddress = this.baseAddress.v2Router;
        this.factoryAddress = this.baseAddress.v2Factory;
        this.Fee = this.baseAddress.v2Fee;
        // console.log("this.baseAddress", this.baseAddress);
        this.chainSymbol = selectChainParams.chainSymbol;
        this.lpManageAddress = lpAddParam[chainId].lpManage;
        this.poolABI = CoinData.abi;

        for (let i = 0; i < selectChainParams.currencyOptions.length; i++) {
          var temAddress = ethers.utils.getAddress(
            selectChainParams.currencyOptions[i].value
          );
          var temParam = {
            label: selectChainParams.currencyOptions[i].label,
            value: temAddress,
          };
          this.currencyOptions.push(temParam);
        }
      }
    }, 1000);
  },
  computed: {
    getTokenName() {
      if (this.tokenName == null) {
        return "???";
      } else {
        return this.tokenName;
      }
    },
    getCurrencyName() {
      if (this.currencyName == null) {
        return "???";
      } else {
        return this.currencyName;
      }
    },
    getTokenBalance() {
      if (this.tokenBalance == null) {
        return "???";
      } else {
        return new Number(
          ethers.utils.formatUnits(this.tokenBalance, this.tokenDecimals)
        )
          .toFixed(6)
          .toString();
      }
    },
    getCurrencyBalance() {
      if (this.currencyBalance == null) {
        return "???";
      } else {
        return new Number(
          ethers.utils.formatUnits(this.currencyBalance, this.currecnyDecimals)
        )
          .toFixed(6)
          .toString();
      }
    },
    getPrice() {
      if (!this.addTokenNum || !this.addCurrencyNum) {
        return "0.00";
      } else {
        return parseFloat(
          new Number(this.addCurrencyNum) / new Number(this.addTokenNum)
        )
          .toFixed(6)
          .toString();
      }
    },
    getCostAndSymbol() {
      return this.Fee + " " + this.chainSymbol;
    },
    getPoolAddress() {
      if (this.poolAddress == null) {
        return this.$t("liquidity.未创建");
      } else {
        return this.poolAddress;
      }
    },
  },
  methods: {
    getCurrency(selectCurrency) {
      // console.log("selectCurrency", selectCurrency);

      if (selectCurrency == 1) {
        this.otherCurrency = true;
        this.currency = null;
      } else {
        this.otherCurrency = false;
        this.currency = selectCurrency;

        if (this.currency == this._WETH) {
          this.currencyIsEth = true;
        } else {
          this.currencyIsEth = false;
        }
      }
    },

    async checkToken() {
      this.checkLoading = true;
      if (!this.currency) {
        this.$message({
          type: "error",
          message: this.$t("liquidity.请选择底池代币") + "!",
        });
        this.checkLoading = false;
        return;
      }
      const provider = store.state.user.provider;
      let isToken = ethers.utils.isAddress(this.tokenAddress);
      let isCurrency = ethers.utils.isAddress(this.currency);
      let tokenCode;
      let currencyCode;

      if (!isToken || !isCurrency) {
        this.$message({
          type: "error",
          message: this.$t("liquidity.代币地址地址不正确") + "!",
        });
        this.checkLoading = false;
        return;
      }
      try {
        tokenCode = await provider.getCode(this.tokenAddress);
        currencyCode = await provider.getCode(this.currency);
      } catch {
        this.$message({
          type: "error",
          message: this.$t("liquidity.代币地址地址不正确") + "!",
        });
        this.checkLoading = false;
        return;
      }

      if (tokenCode == "0x" || currencyCode == "0x") {
        this.$message({
          type: "error",
          message: this.$t("liquidity.代币地址地址不正确") + "!",
        });
        this.checkLoading = false;
        return;
      }

      this.tokenAddress = ethers.utils.getAddress(this.tokenAddress);
      if (this.poolType == "v2") {
        this.routerAddress = this.baseAddress.v2Router;
        this.factoryAddress = this.baseAddress.v2Factory;
        this.Fee = this.baseAddress.v2Fee;
      } else {
        this.routerAddress = this.baseAddress.v3Router;
        this.factoryAddress = this.baseAddress.v3Factory;
        this.Fee = this.baseAddress.v3Fee;
      }

      await this.getBasicParams();
      this.checkLoading = false;
    },
    async getBasicParams() {
      const provider = store.state.user.provider;
      const signer = provider.getSigner();
      const tokenContract = new ethers.Contract(
        this.tokenAddress,
        this.tokenABI,
        signer
      );
      tokenContract
        .symbol()
        .then((symbol) => {
          this.tokenName = symbol;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t("liquidity.获取代币简称错误") + "!",
          });
        });
      tokenContract
        .balanceOf(store.state.user.address)
        .then((temBalance) => {
          this.tokenBalance = temBalance;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t("liquidity.获取代币余额错误") + "!",
          });
        });
      tokenContract
        .decimals()
        .then((decimals) => {
          this.tokenDecimals = decimals;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t("liquidity.获取代币精度错误") + "!",
          });
        });
      if (this.currencyIsEth) {
        this.currencyName = this.chainSymbol;
        this.currencyDecimals = BigNumber.from(18);
        provider
          .getBalance(store.state.user.address)
          .then((temBalance) => {
            this.currencyBalance = temBalance;
          })
          .catch(() => {
            this.$message({
              type: "danger",
              message: this.$t("liquidity.获取底池代币余额错误") + "!",
            });
          });
      } else {
        const currencyContract = new ethers.Contract(
          this.currency,
          this.tokenABI,
          signer
        );
        currencyContract
          .symbol()
          .then((symbol) => {
            this.currencyName = symbol;
          })
          .catch(() => {
            this.$message({
              type: "danger",
              message: this.$t("liquidity.获取底池代币简称错误") + "!",
            });
          });
        currencyContract
          .balanceOf(store.state.user.address)
          .then((temBalance) => {
            this.currencyBalance = temBalance;
          })
          .catch(() => {
            this.$message({
              type: "danger",
              message: this.$t("liquidity.获取底池代币余额错误") + "!",
            });
          });
        currencyContract
          .decimals()
          .then((decimals) => {
            this.currencyDecimals = decimals;
          })
          .catch(() => {
            this.$message({
              type: "danger",
              message: this.$t("liquidity.获取底池代币精度错误") + "!",
            });
          });
      }

      const factoryContract = new ethers.Contract(
        this.factoryAddress,
        this.factoryABI,
        signer
      );
      const temPoolAddress = await factoryContract.getPair(
        this.tokenAddress,
        this.currency
      );
      if (temPoolAddress == "******************************************") {
        this.$message({
          type: "success",
          message: this.$t("liquidity.代币正常") + "!",
        });
      } else {
        this.poolAddress = temPoolAddress;
        const poolContract = new ethers.Contract(
          temPoolAddress,
          this.poolABI,
          signer
        );
        const reverses = await poolContract.getReserves();
        console.log("reverses", reverses);
        if (
          reverses[0].eq(reverses[1]) &&
          reverses[0].eq(ethers.constants.Zero)
        ) {
          this.$message({
            type: "success",
            message: this.$t("liquidity.代币正常") + "!",
          });
        } else {
          this.added = true;
          this.$message({
            type: "danger",
            message: this.$t("liquidity.流动性池已存在") + "!",
          });
        }
      }
    },
    handleAddToken() {
      const temNum = ethers.utils.parseUnits(
        this.addTokenNum.toString(),
        this.tokenDecimals
      );
      if (temNum.gt(this.tokenBalance)) {
        this.$message({
          type: "danger",
          message: this.$t("liquidity.加池代币数量超过余额") + "!",
        });
      }
      if (!this.addTokenNum || temNum.eq(ethers.constants.Zero)) {
        this.$message({
          type: "danger",
          message: this.$t("liquidity.加池代币数量不能为0") + "!",
        });
      }
    },
    handleAddCurrency() {
      const temNum = ethers.utils.parseUnits(
        this.addCurrencyNum.toString(),
        this.currecnyDecimals
      );
      if (temNum.gt(this.currencyBalance)) {
        this.$message({
          type: "danger",
          message: this.$t("liquidity.加池底池代币数量超过余额") + "!",
        });
      }
      if (temNum.eq(ethers.constants.Zero)) {
        this.$message({
          type: "danger",
          message: this.$t("liquidity.加池底池代币数量不能为0") + "!",
        });
      }
    },
    approveToken() {
      this.approvedTokenLoading = true;
      if (!this.tokenDecimals) {
        this.approvedTokenLoading = false;

        this.$message({
          type: "danger",
          message: this.$t("liquidity.请先查询代币") + "!",
        });
        return;
      }
      this.approve(this.tokenAddress, this.tokenDecimals)
        .then((rs) => {
          this.approvedToken = true;
          this.approvedTokenLoading = false;
        })
        .catch((error) => {
          console.log("错误!", error);
          this.$message({
            type: "danger",
            message: this.$t("liquidity.授权失败请重试"),
          });
          this.approvedTokenLoading = false;
        });
    },
    approveCurrency() {
      this.approvedCurrencyLoading = true;
      if (!this.tokenDecimals) {
        this.approvedCurrencyLoading = false;

        this.$message({
          type: "danger",
          message: this.$t("liquidity.请先查询代币") + "!",
        });
        return;
      }
      if (!this.currencyIsEth) {
        this.approve(this.currency, this.currecnyDecimals)
          .then(() => {
            this.approvedCurrency = true;
            this.approvedCurrencyLoading = false;
          })
          .catch((error) => {
            console.log("错误!", error);
            this.$message({
              type: "danger",
              message: this.$t("liquidity.授权失败请重试") + "!",
            });
            this.approvedCurrencyLoading = false;
          });
      } else {
        this.approvedCurrency = true;
        this.approvedCurrencyLoading = false;
      }
    },
    async approve(tokenAddress, decimals) {
      const abi = [
        "function balanceOf(address owner) view returns (uint256)",
        "function decimals() view returns (uint8)",
        "function symbol() view returns (string)",
        "function transfer(address to, uint amount) returns (bool)",
        "function approve(address spender, uint256 amount) external returns (bool)",
        "function allowance(address owner, address spender) external view returns (uint256)",
      ];
      const provider = store.state.user.provider;
      const signer = provider.getSigner();
      const ContractAddress = ethers.utils.getAddress(tokenAddress);
      const erc20 = new ethers.Contract(ContractAddress, abi, signer);

      var getAllowance = await erc20.allowance(
        store.state.user.address,
        this.lpManageAddress
      );
      getAllowance = ethers.utils.formatUnits(getAllowance, decimals);
      const hhh = ethers.utils.formatUnits(
        BigNumber.from("10000000000000000000"),
        decimals
      );

      if (getAllowance >= this.approvedAmount) {
        return true;
      } else {
        var unlimited =
          "115792089237316195423570985008687907853269984665640564039457584007913129639935";
        const tx = await erc20.approve(this.lpManageAddress, unlimited);
        this.$message({
          type: "success",
          message: this.$t("liquidity.已提交等待区块确认") + "!",
        });
        await tx.wait();
        this.$message({
          type: "success",
          message: this.$t("liquidity.授权成功") + "!",
        });
        return true;
      }
    },
    async onSubmit() {
      this.addLoading = true;
      if (!this.tokenDecimals) {
        this.addLoading = false;

        this.$message({
          type: "danger",
          message: this.$t("liquidity.请先查询代币") + "!",
        });
        return;
      }
      if (this.added) {
        this.addLoading = false;

        this.$message({
          type: "danger",
          message: this.$t("liquidity.流动性池已存在") + "!",
        });
        return;
      }
      const bigAddToken = ethers.utils.parseUnits(
        this.addTokenNum,
        this.tokenDecimals
      );

      const bigAddCurrency = ethers.utils.parseUnits(
        this.addCurrencyNum,
        this.currecnyDecimals
      );
      if (bigAddToken.gt(this.tokenBalance)) {
        this.$message({
          type: "danger",
          message: this.$t("liquidity.加池代币数量超过余额") + "!",
        });
        this.addLoading = false;
        return;
      }
      if (bigAddToken.eq(ethers.constants.Zero)) {
        this.$message({
          type: "danger",
          message: this.$t("liquidity.加池代币数量不能为0") + "!",
        });
        this.addLoading = false;
        return;
      }
      if (bigAddCurrency.gt(this.currencyBalance)) {
        this.$message({
          type: "danger",
          message: this.$t("liquidity.加池底池代币数量超过余额") + "!",
        });
        this.addLoading = false;
        return;
      }
      if (bigAddCurrency.eq(ethers.constants.Zero)) {
        this.$message({
          type: "danger",
          message: this.$t("liquidity.加池底池代币数量不能为0") + "!",
        });
        this.addLoading = false;
        return;
      }
      const provider = store.state.user.provider;
      const signer = provider.getSigner();
      const tokenContract = new ethers.Contract(
        this.tokenAddress,
        this.tokenABI,
        signer
      );

      var getAllowanceToken = await tokenContract.allowance(
        store.state.user.address,
        this.lpManageAddress
      );

      if (getAllowanceToken.lt(bigAddToken)) {
        this.addLoading = false;
        this.$message({
          type: "danger",
          message: this.$t("liquidity.加池代币未授权") + "!",
        });
        this.addLoading = false;
        return;
      }
      const lpManageContract = new ethers.Contract(
        this.lpManageAddress,
        this.lpManageABI,
        signer
      );
      const bigFee = ethers.utils.parseEther(this.Fee);
      let overrides;
      const GasPrice = await provider.getGasPrice();
      if (this.currencyIsEth) {
        overrides = { value: bigAddCurrency.add(bigFee) };
      } else {
        overrides = { value: bigFee };
      }
      let estimateGas;
      try {
        estimateGas = await lpManageContract.estimateGas.addLiquidityToken(
          this.routerAddress,
          this.tokenAddress,
          this.currency,
          bigAddToken,
          bigAddCurrency,
          this.currencyIsEth,
          overrides
        );
      } catch {
        this.addLoading = false;
        this.$message({
          type: "danger",
          message: this.$t("liquidity.预估GAS失败") + "!",
        });
        return;
      }

      if (this.currencyIsEth) {
        this.ETHCost = (
          new Number(this.Fee) +
          new Number(this.addCurrencyNum) +
          new Number(ethers.utils.formatEther(GasPrice.mul(estimateGas)))
        )
          .toFixed(6)
          .toString();
      } else {
        this.ETHCost = (
          new Number(this.Fee) +
          new Number(ethers.utils.formatEther(GasPrice.mul(estimateGas)))
        )
          .toFixed(6)
          .toString();
        const currencyContract = new ethers.Contract(
          this.currency,
          this.tokenABI,
          signer
        );
        var getAllowanceCurrency = await currencyContract.allowance(
          store.state.user.address,
          this.lpManageAddress
        );

        if (getAllowanceCurrency.lt(bigAddCurrency)) {
          this.addLoading = false;
          this.$message({
            type: "danger",
            message: this.$t("liquidity.底池代币未授权") + "!",
          });
          return;
        }
      }
      this.addLoading = false;

      this.dialogVisible = true;
    },
    async checkParamsAdd() {
      this.addLoading = true;
      const provider = store.state.user.provider;
      const signer = provider.getSigner();

      const bigAddToken = ethers.utils.parseUnits(
        this.addTokenNum,
        this.tokenDecimals
      );
      const bigAddCurrency = ethers.utils.parseUnits(
        this.addCurrencyNum,
        this.currecnyDecimals
      );
      const lpManageContract = new ethers.Contract(
        this.lpManageAddress,
        this.lpManageABI,
        signer
      );
      const bigFee = ethers.utils.parseEther(this.Fee);
      let overrides;
      if (this.currencyIsEth) {
        overrides = { value: bigAddCurrency.add(bigFee) };
      } else {
        overrides = { value: bigFee };
      }
      try {
        const addRs = await lpManageContract.addLiquidityToken(
          this.routerAddress,
          this.tokenAddress,
          this.currency,
          bigAddToken,
          bigAddCurrency,
          this.currencyIsEth,
          overrides
        );
        this.$message({
          type: "success",
          message: this.$t("liquidity.已提交等待区块确认") + "!",
        });
        await addRs.wait();
        this.$message({
          type: "success",
          message: this.$t("liquidity.加池成功") + "!",
        });
        const factoryContract = new ethers.Contract(
          this.factoryAddress,
          this.factoryABI,
          signer
        );
        const temPoolAddress = await factoryContract.getPair(
          this.tokenAddress,
          this.currency
        );
        this.poolAddress = temPoolAddress;
        this.addLoading = false;
        this.manageCenter = true;
      } catch (error) {
        console.log("错误!", error);
        this.$message({
          type: "danger",
          message: this.$t("liquidity.加池失败") + "!",
        });
        this.addLoading = false;
      }
    },
    addAll(num) {
      if (num == 0) {
        this.addTokenNum = parseFloat(
          this.tokenBalance / 10 ** this.tokenDecimals
        )
          .toFixed(6)
          .toString();
      } else {
        this.addCurrencyNum = parseFloat(
          new Number(this.currencyBalance) /
            10 ** new Number(this.currencyDecimals)
        )
          .toFixed(6)
          .toString();
      }
    },
    enterManage() {
      this.$router.push({
        path: "/LPmanage/",
      });
    },

    copy_str(text) {
      if (navigator.clipboard) {
        // clipboard api 复制
        navigator.clipboard.writeText(text);
      } else {
        var textarea = document.createElement("textarea");
        document.body.appendChild(textarea);
        // 隐藏此输入框
        textarea.style.position = "fixed";
        textarea.style.clip = "rect(0 0 0 0)";
        textarea.style.top = "10px";
        // 赋值
        textarea.value = text;
        // 选中
        textarea.select();
        // 复制
        document.execCommand("copy", true);
        // 移除输入框
        document.body.removeChild(textarea);
      }
      this.$message({
        type: "success",
        message: this.$t("liquidity.已复制") + "!",
      });
    },
  },
};
</script>

<style scoped>
@media screen and (max-width: 500px) {
}
@media screen and (min-width: 500px) {
  .el-input {
    width: 80%;
  }
  .el-select {
    width: 80%;
  }
}
</style>
