<template>
  <div class="app-container">
    <div class="inner">
      <el-alert
        v-show="support"
        :title="$t('liquidity.暂不支持此链')"
        type="error"
        :description="$t('liquidity.联系定制')"
        center
        :closable="false"
        show-icon
      >
      </el-alert>
      <el-header class="header" style="margin-bottom: 25px">
        <p>
          {{ $t("liquidity.管理流动性") }}
          <a
            style="
              margin-left: 10px;
              font-size: 12px;
              font-weight: 400;
              color: #409eff;
            "
            :href="helpURL"
            target="_blank"
            >{{ $t("coinRelease.common.教程") }}</a
          >
        </p>
        <p style="font-size: 14px; font-weight: 300">
          {{ $t("liquidity.管理说明") }}
        </p>
      </el-header>
      <div class="liquidity-container">
        <!-- 类型选择区域 -->

        <el-row
          class="type-switch"
          type="flex"
          justify="space-between"
          :gutter="10"
        >
          <el-col :xs="24" :sm="14">
            <div>
              {{ $t("liquidity.流动性类型") }}
              :
              <el-radio-group v-model="poolType" size="small">
                <el-radio-button label="V2">{{
                  $t("liquidity.PancakeV2")
                }}</el-radio-button>
                <el-radio-button disabled label="V3">{{
                  $t("liquidity.PancakeV3稳定池")
                }}</el-radio-button>
              </el-radio-group>
            </div>
          </el-col>

          <el-col :xs="24" :sm="10" class="text-right">
            <el-button
              size="small"
              type="info"
              plain
              @click="fetchDialogVisible = !fetchDialogVisible"
              >{{ $t("liquidity.未找到您的池子") }}?</el-button
            >
          </el-col>
        </el-row>
        <el-dialog
          :title="$t('liquidity.查找流动性')"
          :visible.sync="fetchDialogVisible"
          :close-on-click-modal="false"
          show-close
        >
          <el-form>
            <el-form-item :label="$t('liquidity.流动性类型')">
              <el-radio-group v-model="poolType">
                <el-radio label="V2">{{ $t("liquidity.PancakeV2") }}</el-radio>
                <el-radio label="V3" disabled>{{
                  $t("liquidity.PancakeV3稳定池")
                }}</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item :label="$t('liquidity.选择底池代币')">
              <el-select
                v-model="selectCurrency"
                @change="getCurrency"
                :placeholder="$t('liquidity.请选择')"
              >
                <el-option
                  v-for="item in currencyOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                  <span style="float: left">{{ item.label }}</span>
                </el-option>
                <el-option
                  :label="$t('liquidity.其他代币')"
                  value="1"
                ></el-option>
              </el-select>
              <el-input
                v-if="otherCurrency"
                v-model="currency"
                placeholder="0x..."
                style="margin-top: 10px"
              />
            </el-form-item>
            <el-form-item :label="$t('liquidity.加池代币地址')">
              <el-input
                v-model="tokenAddress"
                :placeholder="$t('liquidity.请输入代币地址')"
              />
            </el-form-item>
          </el-form>

          <span slot="footer" class="dialog-footer">
            <el-button
              v-show="goCreate"
              type="primary"
              plain
              @click="enterCreatePool"
              >{{ $t("liquidity.前往创建流动性") }}</el-button
            >
            <el-button v-if="checkLoading" :loading="true" type="primary">{{
              $t("liquidity.请稍后")
            }}</el-button>
            <el-button v-else type="primary" @click="checkPool">{{
              $t("liquidity.查询流动性")
            }}</el-button>
          </span>
        </el-dialog>

        <div v-if="!LPlistLoading && LPlist.length === 0" class="noLiquidity">
          {{ $t("liquidity.当前地址未发现任何流动性池") }}
        </div>

        <el-card
          v-else
          class="liquidity-card"
          v-for="(item, index) in LPlist"
          :key="index"
        >
          <el-row :gutter="20" class="card-row">
            <el-col :xs="12" :sm="4" class="item">
              <div class="label">{{ $t("liquidity.代币对") }}</div>
              <div class="LPValue">
                {{ item.token0Symbol }} - {{ item.token1Symbol }}
              </div>
            </el-col>

            <el-col :xs="12" :sm="7" class="item">
              <div class="label">{{ $t("liquidity.池子地址") }}</div>
              <div class="LPValue">
                {{ getShortAddr(item.contractAddress) }}
                <i
                  class="el-icon-copy-document"
                  style="
                    margin-left: 2px;
                    font-size: 12px;
                    font-weight: 400;
                    color: #409eff;
                  "
                  @click="copy_str(item.contractAddress)"
                  >{{ $t("liquidity.复制") }}</i
                >
              </div>
            </el-col>

            <el-col :xs="12" :sm="4" class="item">
              <div class="label">
                {{ $t("liquidity.LP数量") }}
                <el-tooltip placement="top" effect="light">
                  <div style="font-size: 14px; font-weight: 300" slot="content">
                    {{ $t("liquidity.LP说明") }}<br /><br />

                    {{ $t("liquidity.LP是流动性的权益凭证") }}<br /><br />
                  </div>
                  <i class="el-icon-warning-outline" />
                </el-tooltip>
              </div>
              <div class="LPValue">
                {{ getLPBalance(item.balance, item.decimals) }}
              </div>
            </el-col>

            <el-col :xs="12" :sm="3" class="item">
              <div class="label">{{ $t("liquidity.锁池") }}</div>

              <el-button
                v-if="lockLoading"
                :loading="true"
                size="small"
                plain
              ></el-button>
              <el-button v-else @click="checkLockLP(item)" size="small" plain
                >🔒</el-button
              >
            </el-col>

            <el-col :xs="12" :sm="3" class="item">
              <div class="label">{{ $t("liquidity.添加") }}</div>
              <el-button
                v-if="addLoading"
                :loading="true"
                size="small"
                plain
              ></el-button>
              <el-button v-else @click="checkAddLP(item)" size="small" plain
                >➕️</el-button
              >
            </el-col>

            <el-col :xs="12" :sm="3" class="item">
              <div class="label">{{ $t("liquidity.移除") }}</div>
              <el-button
                v-if="removeLoading"
                :loading="true"
                size="small"
                plain
              ></el-button>
              <el-button v-else @click="checkRemoveLP(item)" size="small" plain
                >➖</el-button
              >
            </el-col>
          </el-row>
        </el-card>
        <div
          v-loading="LPlistLoading"
          :element-loading-text="$t('liquidity.正在加载')"
          class="spinner"
        ></div>
        <el-dialog
          :title="$t('liquidity.锁池')"
          :visible.sync="lockDialogVisible"
          :close-on-click-modal="false"
          show-close
        >
          <el-form>
            <p style="font-size: 14px; color: #606266; font-weight: 700">
              {{ $t("liquidity.锁池百分比") }}
            </p>
            <el-slider :min="1" :max="100" v-model="removePercent" show-input>
            </el-slider>
            <el-form-item :label="$t('liquidity.锁池数量')">
              {{ getRemoveLPAmount() + " "
              }}{{ LPItem == null ? "??" : LPItem.symbol }}
            </el-form-item>
            <el-form-item :label="$t('liquidity.解锁日期')">
              <el-date-picker
                v-model="unlockTime"
                type="datetime"
                value-format="timestamp"
                :picker-options="pickerOptions"
                :placeholder="$t('liquidity.请选择解锁日期')"
              />
            </el-form-item>
            <el-form-item :label="$t('liquidity.锁池标题')">
              <el-input
                style="width: 70%"
                v-model="lockTitle"
                type="text"
                :placeholder="$t('liquidity.请输入锁池标题')"
              />
            </el-form-item>
            <el-form-item :label="$t('liquidity.LP代币授权')">
              <el-button
                v-if="approvedLPLoading"
                :loading="true"
                type="info"
                plain
                size="small"
                >{{ $t("liquidity.正在授权") + " "
                }}{{ LPItem == null ? "??" : LPItem.symbol }}</el-button
              >
              <el-button
                v-else-if="approvedLP"
                type="success"
                plain
                disabled
                size="small"
                >{{ LPItem == null ? "??" : LPItem.symbol
                }}{{ " " + $t("liquidity.已授权") }}</el-button
              >
              <el-button
                v-else
                type="primary"
                plain
                @click="approveLP"
                size="small"
                >{{ $t("liquidity.授权") + " "
                }}{{ LPItem == null ? "??" : LPItem.symbol }}</el-button
              >
            </el-form-item>
          </el-form>

          <span slot="footer" class="dialog-footer">
            <el-button v-if="lockLoading" type="primary" :loading="true">{{
              $t("liquidity.请稍后")
            }}</el-button>

            <el-button
              v-else-if="actionDone && !lockLoading"
              type="info"
              plain
              disabled
              >{{ $t("liquidity.锁池完成") }}</el-button
            >
            <el-button v-else type="primary" @click="submitLock">{{
              $t("liquidity.确认锁池")
            }}</el-button>
          </span>
        </el-dialog>
        <el-dialog
          :title="$t('liquidity.移除流动性')"
          :visible.sync="removeDialogVisible"
          :close-on-click-modal="false"
          show-close
        >
          <el-form>
            <p style="font-size: 14px; color: #606266; font-weight: 700">
              {{ $t("liquidity.移除百分比") }}
            </p>
            <el-slider :min="1" :max="100" v-model="removePercent" show-input>
            </el-slider>
            <el-form-item :label="$t('liquidity.移除数量')">
              {{ getRemoveLPAmount() }}
            </el-form-item>
            <el-form-item :label="$t('liquidity.预计获取数量')">
              <div style="width: 100%">
                {{ getTokenAmount(0) + " " }}
                <span style="color: #409eff; font-weight: 600">
                  {{ LPItem == null ? "??" : LPItem.token0Symbol }}
                </span>
                +
                {{ "    " + getTokenAmount(1) + " " }}
                <span style="color: #409eff; font-weight: 600">
                  {{ LPItem == null ? "??" : LPItem.token1Symbol }}
                </span>
              </div>
            </el-form-item>

            <el-form-item :label="$t('liquidity.接收钱包')">
              <el-input v-model="recipient" placeholder="0x..." />
            </el-form-item>
            <el-form-item :label="$t('liquidity.LP代币授权')">
              <el-button
                v-if="approvedLPLoading"
                :loading="true"
                type="info"
                plain
                size="small"
                >{{ $t("liquidity.正在授权") + " "
                }}{{ LPItem == null ? "??" : LPItem.symbol }}</el-button
              >
              <el-button
                v-else-if="approvedLP"
                type="success"
                plain
                disabled
                size="small"
                >{{ LPItem == null ? "??" : LPItem.symbol
                }}{{ " " + $t("liquidity.已授权") }}</el-button
              >
              <el-button
                v-else
                type="primary"
                plain
                @click="approveLP"
                size="small"
                >{{ $t("liquidity.授权") + " "
                }}{{ LPItem == null ? "??" : LPItem.symbol }}</el-button
              >
            </el-form-item>
          </el-form>

          <p>
            {{ $t("liquidity.税费代币的预估获取数量可能出现偏差") }}
          </p>
          <p>
            {{ $t("liquidity.有限制撤池功能的代币") }}
          </p>
          <p>
            {{ $t("liquidity.加池合约") }}:{{ this.lpManageAddress
            }}<i
              class="el-icon-copy-document"
              style="
                margin-left: 2px;
                font-size: 12px;
                font-weight: 400;
                color: #409eff;
              "
              @click="copy_str(this.lpManageAddress)"
              >{{ $t("liquidity.复制") }}</i
            >
          </p>
          <span slot="footer" class="dialog-footer">
            <el-button v-if="removeLoading" type="primary" :loading="true">{{
              $t("liquidity.请稍后")
            }}</el-button>

            <el-button
              v-else-if="actionDone && !removeLoading"
              type="info"
              plain
              disabled
              >{{ $t("liquidity.撤池完成") }}</el-button
            >
            <el-button v-else type="primary" @click="submitRemove">{{
              $t("liquidity.确认撤池")
            }}</el-button>
          </span>
        </el-dialog>
        <el-dialog
          :title="$t('liquidity.添加流动性')"
          :visible.sync="addDialogVisible"
          :close-on-click-modal="false"
          show-close
        >
          <el-form>
            <el-form-item :label="$t('liquidity.当前预估价格')">
              <span v-if="switchPrice"
                >{{ getPrice10 }} {{ getToken0Name }} /
                {{ getToken1Name }}</span
              >
              <span v-else
                >{{ getPrice01 }} {{ getToken1Name }} /
                {{ getToken0Name }}</span
              >

              <el-button
                class="el-icon-sort"
                size="mini"
                style="
                  margin-left: 6px;
                  font-size: 12px;
                  font-weight: 400;
                  color: #409eff;
                "
                @click="switchPrice = !switchPrice"
                >{{ $t("liquidity.转换价格") }}</el-button
              >
            </el-form-item>

            <el-form-item :label="$t('liquidity.加池数量')">
              <el-row>
                <el-tooltip placement="top" effect="light">
                  <div style="font-size: 14px; font-weight: 300" slot="content">
                    {{ $t("liquidity.加池代币需按照当前价格比例添加")
                    }}<br /><br />
                  </div>

                  <i class="el-icon-warning-outline" />
                </el-tooltip>
              </el-row>
              <el-row :gutter="20" style="margin-top: 5px">
                <el-col :xs="5" :sm="4" style="margin-top: 10px"
                  >{{ getToken0Name }}:</el-col
                >

                <el-col :xs="18" :sm="18" style="margin-top: 10px">
                  <el-input
                    v-model="addToken0Num"
                    @input="handleAddToken0()"
                    oninput="value=value.replace(/[^0-9.]/g,'')"
                    placeholder="0.000000000000000001"
                  >
                  </el-input>
                </el-col>
                <el-col
                  :xs="12"
                  :sm="12"
                  style="
                    margin-top: 10px;
                    font-size: 12px;

                    margin-left: 2px;
                  "
                  >{{ $t("liquidity.余额") }}:
                  {{ getToken0Balance }}
                </el-col>
              </el-row>
              <el-row :gutter="20" style="margin-top: 5px">
                <el-col :xs="5" :sm="4" style="margin-top: 10px"
                  >{{ getToken1Name }}:</el-col
                >

                <el-col :xs="18" :sm="18" style="margin-top: 10px">
                  <el-input
                    v-model="addToken1Num"
                    @input="handleAddToken1()"
                    oninput="value=value.replace(/[^0-9.]/g,'')"
                    placeholder="0.000000000000000001"
                  >
                  </el-input>
                </el-col>
                <el-col
                  :xs="12"
                  :sm="12"
                  style="
                    margin-top: 10px;
                    font-size: 12px;

                    margin-left: 2px;
                  "
                  >{{ $t("liquidity.余额") }}:
                  {{ getToken1Balance }}
                </el-col>
              </el-row>
            </el-form-item>
            <el-form-item :label="$t('liquidity.授权')">
              <el-row>
                <el-tooltip placement="top" effect="light">
                  <div style="font-size: 14px; font-weight: 300" slot="content">
                    {{ $t("liquidity.授权说明") }}<br /><br />

                    {{ $t("liquidity.修复方法需使用合约完成授权选择默认值即可")
                    }}<br /><br />
                  </div>
                  <i class="el-icon-warning-outline" />
                </el-tooltip>
              </el-row>
              <el-row type="flex" justify="space-around">
                <el-col :xs="4" :sm="4" style="margin-top: 10px">
                  <el-button
                    v-if="approvedToken0Loading"
                    :loading="true"
                    type="primary"
                    plain
                    size="medium"
                    >{{ $t("liquidity.正在授权")
                    }}{{ " " + getToken0Name }}</el-button
                  >
                  <el-button
                    v-else-if="approvedToken0"
                    type="success"
                    plain
                    disabled
                    size="medium"
                    >{{ getToken0Name + " "
                    }}{{ $t("liquidity.已授权") }}</el-button
                  >
                  <el-button
                    v-else
                    type="primary"
                    plain
                    @click="approveToken(0)"
                    size="medium"
                    >{{ $t("liquidity.授权")
                    }}{{ " " + getToken0Name }}</el-button
                  >
                </el-col>
                <el-col :xs="4" :sm="4" style="margin-top: 10px">
                  <el-button
                    v-if="approvedToken1Loading"
                    :loading="true"
                    type="primary"
                    plain
                    size="medium"
                    >{{ $t("liquidity.正在授权")
                    }}{{ " " + getToken1Name }}</el-button
                  >
                  <el-button
                    v-else-if="approvedToken1"
                    type="success"
                    plain
                    disabled
                    size="medium"
                    >{{ getToken1Name + " "
                    }}{{ $t("liquidity.已授权") }}</el-button
                  >
                  <el-button
                    v-else
                    type="primary"
                    plain
                    @click="approveToken(1)"
                    size="medium"
                    >{{ $t("liquidity.授权")
                    }}{{ " " + getToken1Name }}</el-button
                  >
                </el-col>
              </el-row>
            </el-form-item>
          </el-form>
          <p>
            {{ $t("liquidity.有限制加池功能的代币") }}
          </p>
          <p>
            {{ $t("liquidity.加池合约") }}:{{ this.lpManageAddress }}
            <i
              class="el-icon-copy-document"
              style="
                margin-left: 2px;
                font-size: 12px;
                font-weight: 400;
                color: #409eff;
              "
              @click="copy_str(lpManageAddress)"
              >{{ $t("tools.复制") }}</i
            >
          </p>
          <span slot="footer" class="dialog-footer">
            <el-button v-if="addLoading" type="primary" :loading="true">{{
              $t("liquidity.请稍后")
            }}</el-button>

            <el-button
              v-else-if="actionDone && !addLoading"
              type="info"
              plain
              disabled
              >{{ $t("liquidity.加池完成") }}</el-button
            >
            <el-button v-else type="primary" @click="submitAdd">{{
              $t("liquidity.立即加池")
            }}</el-button>
          </span>
        </el-dialog>

        <div class="note" v-if="LPlistLoading" style="margin-top: 140px">
          ☝️{{ $t("liquidity.添加或移除流动性时", { fee: getCostAndSymbol })
          }}<br />
          {{ $t("liquidity.请确保钱包内有不少于") }}
          <span class="highlight">{{ getCostAndSymbol }}</span>
        </div>
        <div class="note" v-else style="margin-top: 40px">
          ☝️{{ $t("liquidity.添加或移除流动性时", { fee: getCostAndSymbol })
          }}<br />
          {{ $t("liquidity.请确保钱包内有不少于") }}
          <span class="highlight">{{ getCostAndSymbol }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import CoinData from "@/contracts/PancakeERC20.json";
import lpManageABI from "@/contracts/lpManageABI.json";
import factoryABI from "@/contracts/PancakeFactory.json";
import lpAddParam from "@/contracts/lpAddParam.json";
import chainParams from "@/contracts/coinReleaseParams.json";
import store from "@/store";
import axios from "axios";
const { ethers, BigNumber } = require("ethers");
const supportChain = [56, 97];
const tokenABI = [
  "function balanceOf(address owner) view returns (uint256)",
  "function decimals() view returns (uint8)",
  "function symbol() view returns (string)",
  "function transfer(address to, uint amount) returns (bool)",
  "function approve(address spender, uint256 amount) external returns (bool)",
  "function allowance(address owner, address spender) external view returns (uint256)",
];
export default {
  name: "LiquidityList",
  data() {
    return {
      CoinData,
      factoryABI,
      lpManageABI,
      chainParams,
      lpAddParam,
      store,
      support: null,
      supportChain,
      activeNames: ["1"],
      helpURL:
        this.$i18n.locale == "en-US"
          ? "https://help.pandatool.org/english/createtoken/lpmanage"
          : "https://help.pandatool.org/createtoken/lpmanage",
      tokenABI,
      baseAddress: null,
      chainSymbol: null,
      Fee: null,
      ALCHEMY_URL: null,

      poolType: "V2",
      lpManageAddress: null,
      routerAddress: null,
      factoryAddress: null,
      pinkLockAddr: null,
      poolAddress: null,
      poolABI: null,
      tokenisETH: 2,
      LPlistLoading: true,
      LPlist: [],
      LPItem: null,
      removePercent: 50,
      lockPercent: 100,
      unlockTime: null,
      lockTitle: null,
      recipient: null,
      poolReverses: { reverse0: null, reverse1: null },
      switchPrice: true,
      selectCurrency: null,
      currencyOptions: [],
      otherCurrency: null,
      currency: null,
      currencyIsEth: false,
      currencyName: null,
      currencyBalance: null,
      currecnyDecimals: null,

      tokenAddress: null,
      tokenName: null,
      tokenBalance: null,
      tokenDecimals: null,

      approvedAmount: 1000000000000,

      addToken0Num: null,
      addToken1Num: null,
      ETHCost: null,

      approvedLP: false,
      approvedLPLoading: false,
      approvedToken0: false,
      approvedToken1: false,
      approvedToken0Loading: false,
      approvedToken1Loading: false,
      checkLoading: false,
      removeDialogVisible: false,
      removeLoading: false,
      addDialogVisible: false,
      addLoading: false,
      fetchDialogVisible: false,
      fetchLoading: false,
      lockDialogVisible: false,
      lockLoading: false,
      actionDone: false,
      goCreate: false,
      pickerOptions: {
        disabledDate(time) {
          // 禁用明日以前的时间
          return time.getTime() < Date.now() + 86400; // 加一天的时间戳
        },
      },
    };
  },
  created() {
    let Inval = setInterval(() => {
      if (store.state.user.chainId) {
        window.clearInterval(Inval);
        const chainId = store.state.user.chainId;
        if (supportChain.includes(chainId)) {
          this.support = false;
        } else {
          this.support = true;
        }
        const selectChainParams = chainParams[chainId][6];
        this._WETH = ethers.utils.getAddress(selectChainParams._WETH);
        this.baseAddress = lpAddParam[chainId];
        this.routerAddress = this.baseAddress.v2Router;
        this.factoryAddress = this.baseAddress.v2Factory;
        this.Fee = this.baseAddress.AddRemoveFee;
        this.pinkLockAddr = this.baseAddress.pinkLockAddr;
        this.recipient = store.state.user.address;

        // console.log("this.baseAddress", this.baseAddress);
        this.chainSymbol = selectChainParams.chainSymbol;
        this.lpManageAddress = lpAddParam[chainId].lpManage;
        this.poolABI = CoinData.abi;
        this.ALCHEMY_URL = this.baseAddress.ALCHEMY_URL;
        for (let i = 0; i < selectChainParams.currencyOptions.length; i++) {
          var temAddress = ethers.utils.getAddress(
            selectChainParams.currencyOptions[i].value
          );
          var temParam = {
            label: selectChainParams.currencyOptions[i].label,
            value: temAddress,
          };
          this.currencyOptions.push(temParam);
        }
        this.getERC20TokenBalances(store.state.user.address);
      }
    }, 1000);
  },
  computed: {
    getPrice01() {
      if (
        this.poolReverses.reverse0 == null ||
        this.poolReverses.reverse1 == null
      ) {
        return "0.00";
      } else {
        return parseFloat(
          new Number(
            ethers.utils.formatUnits(
              this.poolReverses.reverse0,
              this.LPItem.token0Decimals
            )
          ) /
            new Number(
              ethers.utils.formatUnits(
                this.poolReverses.reverse1,
                this.LPItem.token1Decimals
              )
            )
        )
          .toFixed(6)
          .toString();
      }
    },
    getPrice10() {
      if (
        this.poolReverses.reverse0 == null ||
        this.poolReverses.reverse1 == null
      ) {
        return "0.00";
      } else {
        return parseFloat(
          new Number(
            ethers.utils.formatUnits(
              this.poolReverses.reverse1,
              this.LPItem.token1Decimals
            )
          ) /
            new Number(
              ethers.utils.formatUnits(
                this.poolReverses.reverse0,
                this.LPItem.token0Decimals
              )
            )
        )
          .toFixed(6)
          .toString();
      }
    },
    getToken0Name() {
      if (this.LPItem == null) {
        return "???";
      } else {
        return this.LPItem.token0Symbol;
      }
    },
    getToken1Name() {
      if (this.LPItem == null) {
        return "???";
      } else {
        return this.LPItem.token1Symbol;
      }
    },

    getToken0Balance() {
      if (this.LPItem == null || this.LPItem.token0Balance == null) {
        return "???";
      } else {
        return new Number(
          ethers.utils.formatUnits(
            this.LPItem.token0Balance,
            this.LPItem.token0Decimals
          )
        )
          .toFixed(6)
          .toString();
      }
    },
    getToken1Balance() {
      if (this.LPItem == null || this.LPItem.token1Balance == null) {
        return "???";
      } else {
        return new Number(
          ethers.utils.formatUnits(
            this.LPItem.token1Balance,
            this.LPItem.token1Decimals
          )
        )
          .toFixed(6)
          .toString();
      }
    },
    getCostAndSymbol() {
      return this.Fee + " " + this.chainSymbol;
    },
    getPoolAddress() {
      if (this.poolAddress == null) {
        return this.$t("liquidity.未创建");
      } else {
        return this.poolAddress;
      }
    },
  },
  methods: {
    getTokenAmount(index) {
      if (this.LPItem == null || this.poolReverses.reverse0 == null) {
        return "???";
      }
      if (
        this.LPItem.balance == null ||
        this.LPItem.balance.eq(ethers.constants.Zero) ||
        this.removePercent == null
      ) {
        return "0.00";
      }

      if (index == 0) {
        return new Number(
          ethers.utils.formatUnits(
            this.poolReverses.reverse0.mul(this.removePercent).div(100),
            this.LPItem.token0Decimals
          )
        )
          .toFixed(6)
          .toString();
      } else {
        return new Number(
          ethers.utils.formatUnits(
            this.poolReverses.reverse1.mul(this.removePercent).div(100),
            this.LPItem.token1Decimals
          )
        )
          .toFixed(6)
          .toString();
      }
    },
    getRemoveLPAmount() {
      if (this.LPItem == null || this.removePercent == null) {
        return "???";
      } else {
        return new Number(
          ethers.utils.formatUnits(
            this.LPItem.balance.mul(this.removePercent).div(100)
          )
        )
          .toFixed(6)
          .toString();
      }
    },
    getLPBalance(balance, decimals) {
      if (balance == null) {
        return "???";
      } else {
        return new Number(ethers.utils.formatUnits(balance, decimals))
          .toFixed(6)
          .toString();
      }
    },
    getShortAddr(address) {
      if (ethers.utils.isAddress(address)) {
        return address.slice(0, 5) + "..." + address.slice(-5);
      } else {
        return "0x??";
      }
    },
    async getERC20TokenBalances(address) {
      // console.log("address", address);
      // console.log("this.ALCHEMY_URL", this.ALCHEMY_URL);
      const provider = store.state.user.provider;
      const signer = provider.getSigner();
      if (!this.ALCHEMY_URL) {
        this.$message({
          type: "error",
          message: this.$t("liquidity.暂不支持此链") + "!",
        });
        return;
      }
      const headers = {
        "Content-Type": "application/json",
      };
      this.LPlist = [];
      try {
        const response = await axios.post(this.ALCHEMY_URL, {
          jsonrpc: "2.0",
          headers: headers,
          id: 1,
          method: "alchemy_getTokenBalances",
          params: [address],
        });

        const balances = response.data.result.tokenBalances;
        // console.log("response.data.result", response.data.result);

        // 遍历结果，处理空余额或格式化输出
        for (const token of balances) {
          const contractAddress = token.contractAddress;
          const rawBalance = token.tokenBalance;
          // console.log("contractAddress", contractAddress);
          // console.log("rawBalance", rawBalance);
          if (
            rawBalance ===
            "******************************************000000000000000000000000"
          ) {
            continue; // 跳过余额为0的代币
          }
          if (rawBalance === null) {
            continue;
          }
          try {
            const response_meta = await axios.post(this.ALCHEMY_URL, {
              jsonrpc: "2.0",
              id: 1,
              headers: headers,
              method: "alchemy_getTokenMetadata",
              params: [contractAddress],
            });
            const metadata = response_meta.data.result;
            if (metadata.symbol == "Cake-LP") {
              const poolContract = new ethers.Contract(
                contractAddress,
                this.poolABI,
                signer
              );
              const token0 = await poolContract.token0();
              const token1 = await poolContract.token1();
              let temToken0Symbol;
              let temToken1Symbol;
              let temtoken0Decimals;
              let temtoken1Decimals;
              try {
                const response_meta = await axios.post(this.ALCHEMY_URL, {
                  jsonrpc: "2.0",
                  id: 1,
                  headers: headers,
                  method: "alchemy_getTokenMetadata",
                  params: [token0],
                });
                const metadata = response_meta.data.result;
                temToken0Symbol = metadata.symbol;
                temtoken0Decimals = metadata.decimals;
              } catch (error) {
                temToken0Symbol = "???";

                console.error("❌ 获取token0失败:", error);
              }
              try {
                const response_meta = await axios.post(this.ALCHEMY_URL, {
                  jsonrpc: "2.0",
                  id: 1,
                  headers: headers,
                  method: "alchemy_getTokenMetadata",
                  params: [token1],
                });
                const metadata = response_meta.data.result;
                temToken1Symbol = metadata.symbol;
                temtoken1Decimals = metadata.decimals;
              } catch (error) {
                temToken1Symbol = "???";
                console.error("❌ 获取token0失败:", error);
              }
              const temLP = {
                contractAddress: contractAddress,
                symbol: metadata.symbol,
                name: metadata.name,
                decimals: metadata.decimals,
                balance: ethers.BigNumber.from(rawBalance),
                token0Address: token0,
                token0Symbol: temToken0Symbol,
                token0Decimals: temtoken0Decimals,
                token0Balance: null,
                token1Address: token1,
                token1Symbol: temToken1Symbol,
                token1Decimals: temtoken1Decimals,
                token1Balance: null,
              };
              if (temLP.token0Address == this._WETH) {
                this.tokenisETH = 0;
                temLP.token0Symbol = this.chainSymbol;
                this.currencyIsEth = true;
              } else if (temLP.token1Address == this._WETH) {
                this.tokenisETH = 1;
                temLP.token1Symbol = this.chainSymbol;
                this.currencyIsEth = true;
              } else {
                this.tokenisETH = 2;
                this.currencyIsEth = false;
              }
              this.LPlist.push(temLP);
            }
          } catch (error) {
            this.$message({
              type: "error",
              message: this.$t("liquidity.查询失败检查网络") + "!",
            });
            console.error("❌ 解析余额数据失败:", error);
            this.LPlistLoading = false;
            return;
          }
        }
        // console.log("LPlist", this.LPlist);
        this.LPlistLoading = false;
        console.log(`✅ 查询完成，共 ${balances.length} 个代币`);
      } catch (error) {
        this.$message({
          type: "error",
          message: this.$t("liquidity.查询失败检查网络") + "!",
        });
        console.error("❌ 查询失败:", error?.response?.data || error.message);
        this.LPlistLoading = false;
        return;
      }
    },
    async checkPool() {
      this.checkLoading = true;
      if (!this.currency) {
        this.$message({
          type: "error",
          message: this.$t("liquidity.请选择底池代币") + "!",
        });
        this.checkLoading = false;
        return;
      }
      const provider = store.state.user.provider;
      const signer = provider.getSigner();
      let isToken = ethers.utils.isAddress(this.tokenAddress);
      let isCurrency = ethers.utils.isAddress(this.currency);
      let tokenCode;
      let currencyCode;

      if (!isToken || !isCurrency) {
        this.$message({
          type: "error",
          message: this.$t("liquidity.代币地址地址不正确") + "!",
        });
        this.checkLoading = false;
        return;
      }
      try {
        tokenCode = await provider.getCode(this.tokenAddress);
        currencyCode = await provider.getCode(this.currency);
      } catch {
        this.$message({
          type: "error",
          message: this.$t("liquidity.代币地址地址不正确") + "!",
        });
        this.checkLoading = false;
        return;
      }

      if (tokenCode == "0x" || currencyCode == "0x") {
        this.$message({
          type: "error",
          message: this.$t("liquidity.代币地址地址不正确") + "!",
        });
        this.checkLoading = false;
        return;
      }

      this.tokenAddress = ethers.utils.getAddress(this.tokenAddress);
      this.currency = ethers.utils.getAddress(this.currency);
      const factoryContract = new ethers.Contract(
        this.factoryAddress,
        this.factoryABI,
        signer
      );
      const temPoolAddress = await factoryContract.getPair(
        this.tokenAddress,
        this.currency
      );
      if (temPoolAddress == "******************************************") {
        this.$message({
          type: "danger",
          message: this.$t("liquidity.请先创建流动性") + "!",
        });
        this.goCreate = true;
        this.checkLoading = false;
        return;
      } else {
        this.poolAddress = temPoolAddress;
        const poolContract = new ethers.Contract(
          temPoolAddress,
          this.poolABI,
          signer
        );
        const reverses = await poolContract.getReserves();
        if (
          reverses[0].eq(reverses[1]) &&
          reverses[0].eq(ethers.constants.Zero)
        ) {
          this.$message({
            type: "danger",
            message: this.$t("liquidity.请先创建流动性") + "!",
          });
          this.goCreate = true;
          this.checkLoading = false;
          return;
        } else {
          this.$message({
            type: "success",
            message: this.$t("liquidity.流动性池正常") + "!",
          });
          await this.getBasicParams();
          this.checkLoading = false;
          this.fetchDialogVisible = false;
        }
      }
    },
    async getBasicParams() {
      const provider = store.state.user.provider;
      const signer = provider.getSigner();
      const tokenContract = new ethers.Contract(
        this.tokenAddress,
        this.tokenABI,
        signer
      );
      try {
        this.tokenName = await tokenContract.symbol();
        this.tokenDecimals = await tokenContract.decimals();
      } catch (error) {
        this.$message({
          type: "danger",
          message: this.$t("liquidity.获取代币资料错误") + "!",
        });
        return;
      }
      try {
        if (this.currencyIsEth) {
          this.currencyName = this.chainSymbol;
          this.currencyDecimals = BigNumber.from(18);
        } else {
          const currencyContract = new ethers.Contract(
            this.currency,
            this.tokenABI,
            signer
          );
          this.currencyName = await currencyContract.symbol();
          this.currecnyDecimals = await currencyContract.decimals();
        }
      } catch (error) {
        this.$message({
          type: "danger",
          message: this.$t("liquidity.获取代币资料错误") + "!",
        });
        return;
      }

      const poolContract = new ethers.Contract(
        this.poolAddress,
        this.poolABI,
        signer
      );
      const temPoolBalance = await poolContract.balanceOf(
        store.state.user.address
      );
      if (this.currencyIsEth) {
        this.tokenisETH = 1;
      } else {
        this.tokenisETH = 2;
      }
      let temLPItem = {
        contractAddress: this.poolAddress,
        symbol: "Cake-LP",
        name: "Pancake LPs",
        decimals: 18,
        balance: temPoolBalance,
        token0Address: this.tokenAddress,
        token0Symbol: this.tokenName,
        token0Decimals: this.tokenDecimals,
        token0Balance: null,
        token1Address: this.currency,
        token1Symbol: this.currencyName,
        token1Decimals: this.currecnyDecimals,
        token1Balance: null,
      };
      this.LPItem = temLPItem;
      this.LPlist = [temLPItem];
    },
    getCurrency(selectCurrency) {
      console.log("selectCurrency", selectCurrency);

      if (selectCurrency == 1) {
        this.otherCurrency = true;
        this.currency = null;
      } else {
        this.otherCurrency = false;
        this.currency = selectCurrency;

        if (this.currency == this._WETH) {
          this.currencyIsEth = true;
        } else {
          this.currencyIsEth = false;
        }
      }
    },

    handleAddToken0() {
      const temNum = ethers.utils.parseUnits(
        this.addToken0Num.toString(),
        this.LPItem.token0Decimals
      );
      if (temNum.gt(this.LPItem.token0Balance)) {
        this.$message({
          type: "danger",
          message: this.$t("liquidity.加池代币数量超过余额") + "!",
        });
      }
      if (!this.addToken0Num || temNum.eq(ethers.constants.Zero)) {
        this.$message({
          type: "danger",
          message: this.$t("liquidity.加池代币数量不能为0") + "!",
        });
      }
      this.addToken1Num = new Number(
        ethers.utils.formatUnits(
          temNum
            .mul(this.poolReverses.reverse1)
            .div(this.poolReverses.reverse0),
          this.LPItem.token1Decimals
        )
      )
        .toFixed(6)
        .toString();
    },
    handleAddToken1() {
      const temNum = ethers.utils.parseUnits(
        this.addToken1Num.toString(),
        this.LPItem.token1Decimals
      );
      if (temNum.gt(this.LPItem.token1Balance)) {
        this.$message({
          type: "danger",
          message: this.$t("liquidity.加池代币数量超过余额") + "!",
        });
      }
      if (temNum.eq(ethers.constants.Zero)) {
        this.$message({
          type: "danger",
          message: this.$t("liquidity.加池代币数量不能为0") + "!",
        });
      }
      this.addToken0Num = new Number(
        ethers.utils.formatUnits(
          temNum
            .mul(this.poolReverses.reverse0)
            .div(this.poolReverses.reverse1),
          this.LPItem.token0Decimals
        )
      )
        .toFixed(6)
        .toString();
    },
    approveLP() {
      this.approvedLPLoading = true;
      if (!this.LPItem) {
        this.approvedLPLoading = false;

        this.$message({
          type: "danger",
          message: this.$t("liquidity.请先查询代币获取代币信息") + "!",
        });
        return;
      }
      this.approve(this.LPItem.contractAddress, this.LPItem.decimals)
        .then((rs) => {
          this.approvedLP = true;
          this.approvedLPLoading = false;
        })
        .catch((error) => {
          console.log("错误!", error);
          this.$message({
            type: "danger",
            message: this.$t("liquidity.授权失败请重试"),
          });
          this.approvedLPLoading = false;
        });
    },
    approveToken(num) {
      if (num == 0) {
        if (this.tokenisETH == 0) {
          this.approvedToken0 = true;
          this.approvedToken0Loading = false;
          return;
        }
        this.approvedToken0Loading = true;
        this.approve(this.LPItem.token0Address, this.LPItem.token0Decimals)
          .then((rs) => {
            this.approvedToken0 = true;
            this.approvedToken0Loading = false;
          })
          .catch((error) => {
            console.log("错误!", error);
            this.$message({
              type: "danger",
              message: this.$t("liquidity.授权失败请重试"),
            });
            this.approvedToken0Loading = false;
          });
      } else {
        if (this.tokenisETH == 1) {
          this.approvedToken1 = true;
          this.approvedToken1Loading = false;
          return;
        }
        this.approvedToken1Loading = true;
        this.approve(this.LPItem.token1Address, this.LPItem.token1Decimals)
          .then((rs) => {
            this.approvedToken1 = true;
            this.approvedToken1Loading = false;
          })
          .catch((error) => {
            console.log("错误!", error);
            this.$message({
              type: "danger",
              message: this.$t("liquidity.授权失败请重试") + "!",
            });
            this.approvedToken1Loading = false;
          });
      }
    },

    async approve(tokenAddress, decimals) {
      const abi = [
        "function balanceOf(address owner) view returns (uint256)",
        "function decimals() view returns (uint8)",
        "function symbol() view returns (string)",
        "function transfer(address to, uint amount) returns (bool)",
        "function approve(address spender, uint256 amount) external returns (bool)",
        "function allowance(address owner, address spender) external view returns (uint256)",
      ];
      const provider = store.state.user.provider;
      const signer = provider.getSigner();
      const ContractAddress = ethers.utils.getAddress(tokenAddress);
      const erc20 = new ethers.Contract(ContractAddress, abi, signer);

      var getAllowance = await erc20.allowance(
        store.state.user.address,
        this.lpManageAddress
      );
      // getAllowance = ethers.utils.formatUnits(getAllowance, decimals);
      const bigApproveAmount = ethers.utils.parseUnits(
        "10000000000000000000",
        decimals
      );
      console.log("getAllowance", getAllowance.toString());
      console.log("bigApproveAmount", bigApproveAmount.toString());
      if (getAllowance.gt(bigApproveAmount)) {
        return true;
      } else {
        var unlimited =
          "115792089237316195423570985008687907853269984665640564039457584007913129639935";
        const tx = await erc20.approve(this.lpManageAddress, unlimited);
        this.$message({
          type: "success",
          message: this.$t("liquidity.已提交等待区块确认") + "!",
        });
        await tx.wait();
        this.$message({
          type: "success",
          message: this.$t("liquidity.授权成功") + "!",
        });
        return true;
      }
    },
    async checkAddLP(selcetItem) {
      this.addLoading = true;
      if (!selcetItem.contractAddress) {
        this.$message({
          type: "danger",
          message: this.$t("liquidity.查询失败检查网络") + "!",
        });
        this.addLoading = false;
        return;
      }
      const provider = store.state.user.provider;
      const signer = provider.getSigner();
      const bigApproveAmount = ethers.utils.parseEther(
        "1000000000000000000000"
      );
      if (selcetItem.token0Address == this._WETH) {
        this.tokenisETH = 0;
        this.currencyIsEth = true;
        this.approvedToken0 = true;
        try {
          selcetItem.token0Balance = await provider.getBalance(
            store.state.user.address
          );

          const Token = new ethers.Contract(
            selcetItem.token1Address,
            this.tokenABI,
            signer
          );
          selcetItem.token1Balance = await Token.balanceOf(
            store.state.user.address
          );
          var getAllowanceToken = await Token.allowance(
            store.state.user.address,
            this.lpManageAddress
          );

          if (getAllowanceToken.lt(selcetItem.token1Balance)) {
            this.approvedToken1 = false;
          } else {
            this.approvedToken1 = true;
          }
        } catch {
          this.$message({
            type: "danger",
            message: this.$t("liquidity.获取代币余额错误") + "!",
          });
          this.addLoading = false;
          return;
        }
      } else if (selcetItem.token1Address == this._WETH) {
        this.tokenisETH = 1;
        this.currencyIsEth = true;
        this.approvedToken1 = true;
        try {
          selcetItem.token1Balance = await provider.getBalance(
            store.state.user.address
          );

          const Token = new ethers.Contract(
            selcetItem.token0Address,
            this.tokenABI,
            signer
          );
          selcetItem.token0Balance = await Token.balanceOf(
            store.state.user.address
          );
          var getAllowanceToken = await Token.allowance(
            store.state.user.address,
            this.lpManageAddress
          );

          if (getAllowanceToken.lt(selcetItem.token0Balance)) {
            this.approvedToken0 = false;
          } else {
            this.approvedToken0 = true;
          }
        } catch {
          this.$message({
            type: "danger",
            message: this.$t("liquidity.获取代币余额错误") + "!",
          });
          this.addLoading = false;
          return;
        }
      } else {
        this.tokenisETH = 2;
        this.currencyIsEth = false;
        try {
          const Token0 = new ethers.Contract(
            selcetItem.token0Address,
            this.tokenABI,
            signer
          );
          selcetItem.token0Balance = await Token0.balanceOf(
            store.state.user.address
          );
          var getAllowanceToken0 = await Token0.allowance(
            store.state.user.address,
            this.lpManageAddress
          );

          if (getAllowanceToken0.lt(selcetItem.token0Balance)) {
            this.approvedToken0 = false;
          } else {
            this.approvedToken0 = true;
          }
          const Token1 = new ethers.Contract(
            selcetItem.token1Address,
            this.tokenABI,
            signer
          );
          selcetItem.token1Balance = await Token1.balanceOf(
            store.state.user.address
          );
          var getAllowanceToken1 = await Token1.allowance(
            store.state.user.address,
            this.lpManageAddress
          );

          if (getAllowanceToken1.lt(selcetItem.token1Balance)) {
            this.approvedToken1 = false;
          } else {
            this.approvedToken1 = true;
          }
        } catch {
          this.$message({
            type: "danger",
            message: this.$t("liquidity.获取代币余额错误") + "!",
          });
          this.addLoading = false;
          return;
        }
      }
      try {
        const poolContract = new ethers.Contract(
          selcetItem.contractAddress,
          this.poolABI,
          signer
        );
        const reverses = await poolContract.getReserves();

        this.poolReverses.reverse0 = reverses[0];
        this.poolReverses.reverse1 = reverses[1];
      } catch (error) {
        console.log("error", error);
        this.addLoading = false;
        return;
      }

      this.LPItem = selcetItem;
      this.addLoading = false;
      this.actionDone = false;
      this.addDialogVisible = true;
    },
    async submitAdd() {
      this.addLoading = true;
      if (this.addToken0Num == null || this.addToken1Num == null) {
        this.$message({
          type: "danger",
          message: this.$t("liquidity.加池代币数量不能为0") + "!",
        });
        this.addLoading = false;
        return;
      }

      const provider = store.state.user.provider;
      const signer = provider.getSigner();

      const bigAddToken0 = ethers.utils.parseUnits(
        this.addToken0Num,
        this.LPItem.token0Decimals
      );
      const bigAddToken1 = ethers.utils.parseUnits(
        this.addToken1Num,
        this.LPItem.token1Decimals
      );
      if (bigAddToken0.gt(this.LPItem.token0Balance)) {
        this.$message({
          type: "danger",
          message: this.$t("liquidity.加池代币数量超过余额") + "!",
        });
        this.addLoading = false;
        return;
      }
      if (bigAddToken0.eq(ethers.constants.Zero)) {
        this.$message({
          type: "danger",
          message: this.$t("liquidity.加池代币数量不能为0") + "!",
        });
        this.addLoading = false;
        return;
      }
      if (bigAddToken1.gt(this.LPItem.token1Balance)) {
        this.$message({
          type: "danger",
          message: this.$t("liquidity.加池代币数量超过余额") + "!",
        });
        this.addLoading = false;
        return;
      }
      if (bigAddToken1.eq(ethers.constants.Zero)) {
        this.$message({
          type: "danger",
          message: this.$t("liquidity.加池代币数量不能为0") + "!",
        });
        this.addLoading = false;
        return;
      }

      try {
        const token0Contract = new ethers.Contract(
          this.LPItem.token0Address,
          this.tokenABI,
          signer
        );
        const token1Contract = new ethers.Contract(
          this.LPItem.token1Address,
          this.tokenABI,
          signer
        );
        console.log("this.tokenisETH", this.tokenisETH);
        if (this.tokenisETH == 0) {
          let getAllowanceToken1 = await token1Contract.allowance(
            store.state.user.address,
            this.lpManageAddress
          );
          if (getAllowanceToken1.lt(this.LPItem.token1Balance)) {
            this.$message({
              type: "danger",
              message: this.$t("liquidity.加池代币未授权") + "!",
            });
            this.addLoading = false;
            return;
          }
        } else if (this.tokenisETH == 1) {
          let getAllowanceToken0 = await token0Contract.allowance(
            store.state.user.address,
            this.lpManageAddress
          );
          if (getAllowanceToken0.lt(this.LPItem.token0Balance)) {
            this.$message({
              type: "danger",
              message: this.$t("liquidity.加池代币未授权") + "!",
            });
            this.addLoading = false;
            return;
          }
        } else {
          let getAllowanceToken0 = await token0Contract.allowance(
            store.state.user.address,
            this.lpManageAddress
          );
          if (getAllowanceToken0.lt(this.LPItem.token0Balance)) {
            this.$message({
              type: "danger",
              message: this.$t("liquidity.加池代币未授权") + "!",
            });
            this.addLoading = false;
            return;
          }
          let getAllowanceToken1 = await token1Contract.allowance(
            store.state.user.address,
            this.lpManageAddress
          );
          if (getAllowanceToken1.lt(this.LPItem.token1Balance)) {
            this.$message({
              type: "danger",
              message: this.$t("liquidity.加池代币未授权") + "!",
            });
            this.addLoading = false;
            return;
          }
        }
      } catch {
        this.$message({
          type: "danger",
          message: this.$t("liquidity.加池代币未授权") + "!",
        });
        this.addLoading = false;
        return;
      }

      const lpManageContract = new ethers.Contract(
        this.lpManageAddress,
        this.lpManageABI,
        signer
      );
      const bigFee = ethers.utils.parseEther(this.Fee);
      let overrides;

      if (this.tokenisETH == 0) {
        overrides = { value: bigAddToken0.add(bigFee) };
      } else if (this.tokenisETH == 1) {
        overrides = { value: bigAddToken1.add(bigFee) };
      } else {
        overrides = { value: bigFee };
      }
      try {
        let addRs;
        if (this.tokenisETH == 0) {
          addRs = await lpManageContract.addLiquidityToken(
            this.routerAddress,
            this.LPItem.token1Address,
            this.LPItem.token0Address,
            bigAddToken1,
            bigAddToken0,
            this.currencyIsEth,
            overrides
          );
        } else {
          addRs = await lpManageContract.addLiquidityToken(
            this.routerAddress,

            this.LPItem.token0Address,
            this.LPItem.token1Address,

            bigAddToken0,
            bigAddToken1,
            this.currencyIsEth,
            overrides
          );
        }
        // addRs = await lpManageContract.addLiquidityToken(
        //   this.routerAddress,
        //   this.tokenAddress,
        //   this.currency,
        //   bigAddToken0,
        //   bigAddToken1,
        //   this.currencyIsEth,
        //   overrides
        // );
        this.$message({
          type: "success",
          message: this.$t("liquidity.已提交等待区块确认") + "!",
        });
        await addRs.wait();
        this.$message({
          type: "success",
          message: this.$t("liquidity.加池成功") + "!",
        });
        this.addLoading = false;
        this.actionDone = true;
      } catch (error) {
        console.log("错误!", error);
        this.$message({
          type: "danger",
          message: this.$t("liquidity.加池失败") + "!",
        });
        this.addLoading = false;
      }
    },
    async checkRemoveLP(selcetItem) {
      this.removeLoading = true;
      if (!selcetItem.contractAddress) {
        this.$message({
          type: "danger",
          message: this.$t("liquidity.查询失败检查网络") + "!",
        });
        this.removeLoading = false;
        return;
      }

      if (selcetItem.token0Address == this._WETH) {
        this.tokenisETH = 0;
        selcetItem.token0Symbol = this.chainSymbol;
        this.currencyIsEth = true;
      } else if (selcetItem.token1Address == this._WETH) {
        this.tokenisETH = 1;
        selcetItem.token1Symbol = this.chainSymbol;
        this.currencyIsEth = true;
      } else {
        this.tokenisETH = 2;
        this.currencyIsEth = false;
      }
      this.removeLoading = true;
      try {
        const provider = store.state.user.provider;
        const signer = provider.getSigner();
        const poolContract = new ethers.Contract(
          selcetItem.contractAddress,
          this.poolABI,
          signer
        );
        const reverses = await poolContract.getReserves();

        this.poolReverses.reverse0 = reverses[0];
        this.poolReverses.reverse1 = reverses[1];
        const getAllowanceLP = await poolContract.allowance(
          store.state.user.address,
          this.lpManageAddress
        );
        if (getAllowanceLP.lt(selcetItem.balance)) {
          this.approvedLP = false;
        } else {
          this.approvedLP = true;
        }
      } catch (error) {
        console.log("error", error);
        this.removeLoading = false;
        return;
      }

      this.LPItem = selcetItem;
      this.removeLoading = false;
      this.actionDone = false;
      this.removeDialogVisible = true;
    },
    async submitRemove() {
      this.removeLoading = true;
      const provider = store.state.user.provider;
      const signer = provider.getSigner();
      let isAddress = ethers.utils.isAddress(this.recipient);

      if (!isAddress) {
        this.$message({
          type: "error",
          message: this.$t("liquidity.收币地址不正确") + "!",
        });
        this.removeLoading = false;
        return;
      }

      try {
        let recipientCode = await provider.getCode(this.recipient);
        if (recipientCode != "0x") {
          this.$message({
            type: "error",
            message: this.$t("liquidity.收币地址不正确") + "!",
          });
          this.removeLoading = false;
          return;
        }
      } catch {
        this.$message({
          type: "error",
          message: this.$t("liquidity.收币地址不正确") + "!",
        });
        this.removeLoading = false;
        return;
      }

      const poolContract = new ethers.Contract(
        this.LPItem.contractAddress,
        this.poolABI,
        signer
      );
      const getAllowanceLP = await poolContract.allowance(
        store.state.user.address,
        this.lpManageAddress
      );
      if (getAllowanceLP.lt(this.LPItem.balance)) {
        this.$message({
          type: "error",
          message: this.$t("liquidity.请先授权LP") + "!",
        });
        this.removeLoading = false;
        return;
      }
      const bigRemoveLP = this.LPItem.balance.mul(this.removePercent).div(100);

      const lpManageContract = new ethers.Contract(
        this.lpManageAddress,
        this.lpManageABI,
        signer
      );
      const bigFee = ethers.utils.parseEther(this.Fee);
      let overrides = { value: bigFee };
      try {
        const removeRs = await lpManageContract.removeLiquidityToken(
          this.routerAddress,
          this.LPItem.contractAddress,
          this.recipient,
          this.LPItem.token0Address,
          this.LPItem.token1Address,
          bigRemoveLP,
          this.currencyIsEth,
          overrides
        );
        this.$message({
          type: "success",
          message: this.$t("liquidity.已提交等待区块确认") + "!",
        });
        await removeRs.wait();
        this.$message({
          type: "success",
          message: this.$t("liquidity.撤池成功") + "!",
        });

        this.removeLoading = false;
        this.actionDone = true;
      } catch (error) {
        console.log("错误!", error);
        this.$message({
          type: "danger",
          message: this.$t("liquidity.撤池失败") + "!",
        });
        this.removeLoading = false;
      }
    },
    async checkLockLP(selcetItem) {
      this.lockLoading = true;
      if (!selcetItem.contractAddress) {
        this.$message({
          type: "danger",
          message: this.$t("liquidity.查询失败检查网络") + "!",
        });
        this.lockLoading = false;
        return;
      }

      this.lockLoading = true;
      try {
        const provider = store.state.user.provider;
        const signer = provider.getSigner();
        const poolContract = new ethers.Contract(
          selcetItem.contractAddress,
          this.poolABI,
          signer
        );

        const getAllowanceLP = await poolContract.allowance(
          store.state.user.address,
          this.lpManageAddress
        );
        if (getAllowanceLP.lt(selcetItem.balance)) {
          this.approvedLP = false;
        } else {
          this.approvedLP = true;
        }
      } catch (error) {
        console.log("error", error);
        this.lockLoading = false;
        return;
      }

      this.LPItem = selcetItem;
      this.lockLoading = false;
      this.actionDone = false;
      this.lockDialogVisible = true;
    },
    async submitLock() {
      this.lockLoading = true;
      const provider = store.state.user.provider;
      const signer = provider.getSigner();

      if (!this.unlockTime) {
        this.$message({
          type: "error",
          message: this.$t("liquidity.解锁时间不正确") + "!",
        });
        this.lockLoading = false;
        return;
      }
      console.log("this.unlockTime", this.unlockTime);
      const unlockTime = Math.floor(new Number(this.unlockTime) / 1000);
      console.log("unlockTime", unlockTime);
      const poolContract = new ethers.Contract(
        this.LPItem.contractAddress,
        this.poolABI,
        signer
      );

      const getAllowanceLP = await poolContract.allowance(
        store.state.user.address,
        this.lpManageAddress
      );

      if (getAllowanceLP.lt(this.LPItem.balance)) {
        this.$message({
          type: "error",
          message: this.$t("liquidity.请先授权LP") + "!",
        });
        this.lockLoading = false;
        return;
      }
      const bigLockLP = this.LPItem.balance.mul(this.removePercent).div(100);

      const lpManageContract = new ethers.Contract(
        this.lpManageAddress,
        this.lpManageABI,
        signer
      );
      const bigFee = ethers.utils.parseEther(this.Fee);
      let overrides = { value: bigFee };
      try {
        const lockRs = await lpManageContract.lockToken(
          this.pinkLockAddr,
          this.LPItem.contractAddress,
          bigLockLP,
          unlockTime,
          true,
          this.lockTitle ? this.lockTitle : " ",
          overrides
        );
        this.$message({
          type: "success",
          message: this.$t("liquidity.已提交等待区块确认") + "!",
        });
        await lockRs.wait();
        this.$message({
          type: "success",
          message: this.$t("liquidity.锁池成功") + "!",
        });

        this.lockLoading = false;
        this.actionDone = true;
      } catch (error) {
        console.log("错误!", error);
        this.$message({
          type: "danger",
          message: this.$t("liquidity.锁池失败") + "!",
        });
        this.lockLoading = false;
      }
    },
    addAll(num) {
      if (num == 0) {
        this.addToken0Num = parseFloat(
          this.tokenBalance / 10 ** this.tokenDecimals
        )
          .toFixed(6)
          .toString();
      } else {
        this.addToken1Num = parseFloat(
          new Number(this.currencyBalance) /
            10 ** new Number(this.currencyDecimals)
        )
          .toFixed(6)
          .toString();
      }
    },
    enterCreatePool() {
      this.$router.push({
        path: "/createliquidity/",
      });
    },

    copy_str(text) {
      if (navigator.clipboard) {
        // clipboard api 复制
        navigator.clipboard.writeText(text);
      } else {
        var textarea = document.createElement("textarea");
        document.body.appendChild(textarea);
        // 隐藏此输入框
        textarea.style.position = "fixed";
        textarea.style.clip = "rect(0 0 0 0)";
        textarea.style.top = "10px";
        // 赋值
        textarea.value = text;
        // 选中
        textarea.select();
        // 复制
        document.execCommand("copy", true);
        // 移除输入框
        document.body.removeChild(textarea);
      }
      this.$message({
        type: "success",
        message: this.$t("liquidity.已复制") + "!",
      });
    },
  },
};
</script>

<style scoped>
.text-right {
  padding: 5px;
  text-align: right;
}
.noLiquidity {
  margin-top: 80px;
  margin-left: 6%;
  text-align: center;

  font-size: 16px;
  color: #999;
}
@media (max-width: 768px) {
  .text-right {
    text-align: left; /* 移动端改为左对齐（也可以继续右对齐） */
  }
  .noLiquidity {
    margin-top: 80px;
    margin-left: 1%;
    text-align: center;

    font-size: 16px;
    color: #999;
  }
}
.liquidity-container {
  padding: 20px;
  max-width: 900px;
  margin: auto;
}

.liquidity-card {
  margin-bottom: 15px;
}

.item {
  margin-top: 15px;
  text-align: center;
}

.label {
  font-size: 14px;
  color: #888;
  margin-bottom: 5px;
}

.LPValue {
  padding-top: 8px;
  font-size: 16px;
  font-weight: bold;
}

.note {
  font-size: 14px;
  color: #666;
  text-align: center;

  line-height: 1.6;
}

.highlight {
  color: red;
  font-weight: bold;
}

.spinner {
  margin-top: 60px;
  margin-bottom: 60px;
}
.type-switch {
  margin: 15px 0 15px;
  font-size: 14px;
  color: #444;
}
</style>
