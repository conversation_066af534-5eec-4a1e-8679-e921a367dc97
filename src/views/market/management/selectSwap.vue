<template>
  <div class="selectSwap">
    <el-card>
      <div style="margin-bottom: 2rem">
        <el-link
          :href="helpUrl"
          target="_brank"
          type="danger"
          >{{ $t("market.点击查看手把手教程") }}</el-link
        >
      </div>
      <el-row :gutter="24">
        <el-col :span="12">
          <div>
            <label class="swap-label">{{ $t("market.选择公链") }}</label>
          </div>
          <el-select
            v-model="SelectSwapform.chainId"
            placeholder="请选择"
            style="margin-top: 10px"
            filterable
            :loading="SLoading"
          >
            <el-option
              v-for="(item, index) in chainOptions"
              :key="index"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-col>
        <el-col :span="12">
          <div>
            <label class="swap-label">{{ $t("market.选择交易所") }}</label>
          </div>
          <el-select
            v-model="SelectSwapform.swap"
            :placeholder="$t('market.请选择')"
            style="margin-top: 10px"
          >
            <el-option
              v-for="item in swapOptions"
              :key="item.chainId"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-col>
      </el-row>
      <el-row style="margin-top: 10px">
        <el-col v-show="false">
          <div>
            <label class="swap-label">{{ $t("market.网络节点") }}</label>
          </div>
          <el-input v-model="SelectSwapform.rpc" style="margin-top: 10px">
          </el-input>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
// import swapJson from "./swap.json"
export default {
  props: {
    value: {
      type: Object,
      default: () => ({}),
    },
    swapJson: {
      type: Object,
    },
  },
  data() {
    return {
      helpUrl: `https://help.pandatool.org/${
        this.$i18n.locale == "en-US" ? "english/" : ""
      }tools/operate`,
      SLoading: true,
      chainOptions: [],
      swapOptions: [],
    };
  },
  created() {
    for (var o in this.swapJson) {
      var chainItem = { label: this.swapJson[o].name, value: o };
      this.chainOptions.push(chainItem);
    }
    this.SelectSwapform.chainId = this.chainOptions[0].value;
    for (var s in this.swapJson[this.SelectSwapform.chainId].swap) {
      var sItem = {
        label: this.swapJson[this.SelectSwapform.chainId].swap[s].name,
        value: s,
        chainId: s,
      };
      this.swapOptions.push(sItem);
    }
    // console.log(this.swapOptions)
    this.SelectSwapform.rpc = this.swapJson[this.SelectSwapform.chainId].rpc;
    this.testMultipleRPCUrls(
      this.swapJson[this.SelectSwapform.chainId].rpcs
    ).then((res) => {
      this.SelectSwapform.rpc = res;
    });
    this.SelectSwapform.swap = this.swapOptions[0].value;
  },
  computed: {
    SelectSwapform: {
      get() {
        return this.value;
      },
      set(value) {
        this.$emit("input", value);
      },
    },
  },
  watch: {
    "SelectSwapform.chainId"() {
      this.SelectSwapform.rpc = this.swapJson[this.SelectSwapform.chainId].rpc;
      this.SLoading = true;
      this.testMultipleRPCUrls(
        this.swapJson[this.SelectSwapform.chainId].rpcs
      ).then((res) => {
        this.SelectSwapform.rpc = res;
        this.SLoading = false;
      });
      this.swapOptions = [];
      for (var s in this.swapJson[this.SelectSwapform.chainId].swap) {
        var sItem = {
          label: this.swapJson[this.SelectSwapform.chainId].swap[s].name,
          value: s,
          chainId: s,
        };
        this.swapOptions.push(sItem);
      }
      this.SelectSwapform.swap = this.swapOptions[0].value;
    },
    "SelectSwapform.swap"(v) {
      // console.log(this.SelectSwapform.chainId)
      // console.log(this.SelectSwapform.swap)
      this.SelectSwapform.rpc = this.swapJson[this.SelectSwapform.chainId].rpc;
      this.SLoading = true;
      this.testMultipleRPCUrls(
        this.swapJson[this.SelectSwapform.chainId].rpcs
      ).then((res) => {
        this.SelectSwapform.rpc = res;
        this.SLoading = false;
      });
      this.swapOptions = [];
      for (var s in this.swapJson[this.SelectSwapform.chainId].swap) {
        var sItem = {
          label: this.swapJson[this.SelectSwapform.chainId].swap[s].name,
          value: s,
          chainId: s,
        };
        this.swapOptions.push(sItem);
      }
      this.SelectSwapform.swap = v;
    },
  },
  methods: {
    async checkRpcEndpoint(rpcUrl) {
      const provider = new ethers.providers.JsonRpcProvider(rpcUrl);
      this.provider = provider;
      try {
        const network = await provider.getNetwork();
        console.log("RPC 端点连接成功！");
        console.log("网络名称：", network.name);
        console.log("网络ID：", network.chainId);
        // this.$message({
        //   type: "success",
        //   message: network.name + " RPC端点连接成功！",
        // });
        // this.SLoading = false
        return true;
      } catch (error) {
        // this.$message({
        //   type: "error",
        //   message: network.name + " RPC无法连接网络请更换!",
        // });

        console.error("无法连接到 RPC 端点:", error);
        console.log("切换网络");
        // this.SLoading = false
        return false;
      }
    },
    async testMultipleRPCUrls(urls) {
      const testResults = [];
      const data = {
        jsonrpc: "2.0",
        method: "eth_getBlockByNumber",
        params: ["latest", false],
        id: 1,
      };
      // 测试每个URL的响应时间
      for (const url of urls) {
        const startTime = new Date().getTime();

        try {
          const response = await fetch(url, {
            method: "POST",
            headers: {
              "Content-Type": "application/json", // 指定请求内容类型为 JSON
            },
            body: JSON.stringify(data), // 将 JavaScript 对象转换为 JSON 字符串
          });

          if (!response.ok) {
            throw new Error("Network response was not ok");
          }

          const endTime = new Date().getTime();
          const elapsedTime = endTime - startTime;
          testResults.push({ url, responseTime: elapsedTime });
        } catch (error) {
          console.error(`Error for URL ${url}:`, error);
          testResults.push({ url, responseTime: Infinity }); // 表示出错时将时间设置为无穷大
        }
      }

      // 找到最快的响应时间
      let fastestUrl = null;
      let fastestResponseTime = Infinity;

      for (const result of testResults) {
        if (result.responseTime < fastestResponseTime) {
          fastestResponseTime = result.responseTime;
          fastestUrl = result.url;
        }
      }
      console.log("Fastest RPC URL:", fastestUrl);
      return fastestUrl;
    },
  },
};
</script>

<style>
.el-select .el-input {
  width: 160px;
}
.input-with-select .el-input-group__prepend {
  background-color: #fff;
}
.swap-label {
  font-size: 15px;
  color: #606266;
}
.selectSwap {
  width: 100%;
}
</style>
