import { ethers } from "ethers";

export const externalMethod = () => {
  console.log("This is an external method");
};

export const erc20TokenInfo = async (contractAddress, rpc) => {
  const ABI = [
    "function decimals() view returns (uint8)",
    "function symbol() view returns (string)",
  ];
  const provider = new ethers.providers.JsonRpcProvider(rpc);
  const erc20 = new ethers.Contract(contractAddress, ABI, provider);
  const res = { decimals: 18, symbol: "" };
  res.decimals = await erc20.decimals();
  res.symbol = await erc20.decimals();
  return res;
};

export const arrayIndex = (length,order=true) => {
  const startingNumber = 0; // This is the starting number of the sequence
  const myArray = Array.from({ length }, (_, index) => startingNumber + index);
  if (!order){
    myArray.sort(() => Math.random() - 0.5);
  }
  return myArray;
};

export const randomTime = (m,n) => {
  if (m > n) {
    const temp = m;
    m = n;
    n = temp;
  }
  const randomNumber = (Math.random() * (n - m) + m).toFixed(2)
  return randomNumber*1000
};
