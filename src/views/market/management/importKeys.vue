<template>
  <div class="import-keys">
    <!-- Form -->
    <el-card>
      <el-button
        class="import-botton"
        type="primary"
        @click="dialogFormVisible = true"
        >{{$t('market.导入刷单钱包私钥')}}</el-button
      >
      <p style="font-size: 1rem;">{{$t("market.池子地址")}}:{{ pAddress.substring(0, 7) + '...' + pAddress.substring(35) }}</p>
      <p style="font-size: 1rem;">{{$t('market.当前价格')}}:{{ nPrice }} {{ " " }} {{ sym }} </p>
    </el-card>
    <el-dialog
      :title="$t('market.输入私钥')"
      :visible.sync="dialogFormVisible"
      width="80%"
    >
      <el-form :model="form">
        <el-form-item>
          <el-input
            :autosize="{ minRows: 15, maxRows: 15 }"
            type="textarea"
            v-model="form.text"
            autocomplete="off"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-row :gutter="24">
            <el-col :span="12">
              <el-button
                @click="dialogFormVisible = false"
                style="margin-left: 70%"
                >{{$t("market.取消")}}</el-button
              ></el-col
            >
            <el-col :span="12">
              <el-button type="primary" @click="importWallet"
                >{{$t("market.导入")}}</el-button
              ></el-col
            >
          </el-row>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { ethers } from "ethers";
export default {
  props: {
    value: {
      type: Array,
      required: true,
      default: () => ({}),
    },
    poolAddress: {
      type: String,
      required: true,
    },
    nowPrice: {
      type: String,
      required: true,
    },
    symble:{
      type: String,
      required: true,
    }
  },
  data() {
    return {
      dialogTableVisible: false,
      dialogFormVisible: false,
      form: {
        text: "0x9933d5792e3a13a26776ed574b1e399ea12ecd584cff6ceae571a6b8e0e291ff\n0x1550786a70b0f5ef4dcbdd43bc1840b919e817d2e333bc598d02dcbeb7c156b1",
      },
      pAddress: "",
      nPrice: "",
      sym:''
    };
  },
  methods: {
    importWallet() {
      const keysText = this.form.text.split("\n");
      const keys = [...new Set(keysText)];
      const addressData = [];
      try {
        keys.forEach((key) => {
          if (
            key.length ==
              "0x1550786a70b0f5ef4dcbdd43bc1840b919e817d2e333bc598d02dcbeb7c156b1"
                .length ||
            key.length ==
              "1550786a70b0f5ef4dcbdd43bc1840b919e817d2e333bc598d02dcbeb7c156b1"
                .length
          ) {
            const pubKey = new ethers.Wallet(key).publicKey;
            const address = ethers.utils.computeAddress(pubKey);
            // console.log(address);
            addressData.push({
              address: address,
              showAddress:  address.substring(2, 4) + '...' + address.substring(37),
              prvKey: key,
              ETHBalance: "",
              tokenBalance: "",
              USDTBalance: "",
              approvedToken: "",
              approvedCost: "",
            });
          }
        });
      } catch (error) {
        this.$notify.error({
          title: this.$t("market.错误"),
          message: this.$t("market.钱包格式错误"),
        });
        // console.log(error)
      }

      this.addressData = addressData;
      this.dialogFormVisible = false;
    },
  },
  computed: {
    addressData: {
      get() {
        return this.value;
      },
      set(value) {
        this.$emit("input", value);
      },
    },
  },
  watch: {
    poolAddress(v) {
      this.pAddress = v;
    },
    nowPrice(v) {
      this.nPrice = v;
    },
    symble(v){
      // console.log(v)
      this.sym = v;
    }
  },
};
</script>
<style>
.import-botton {
  margin-left: 33%;
}
</style>
