<template>
  <div class="wallet-list-class">
    <el-card>
      <div>
        <el-button type="primary" :loading="reqloading" @click="reqWallet"
          >{{$t('market.walletList.刷新钱包')}}</el-button
        >
      </div>
      <el-table :data="tableData" height="250" border style="width: 100%">
        <el-table-column prop="showAddress" :label="$t('market.walletList.钱包地址')"> </el-table-column>
        <el-table-column prop="ETHBalance" :label="$t('market.walletList.原生代币')"> </el-table-column>
        <el-table-column prop="USDTBalance" :label="tokenSymbol + $t('market.walletList.余额')">
        </el-table-column>
        <el-table-column prop="tokenBalance" :label="$t('market.walletList.代币余额')">
        </el-table-column>
        <el-table-column prop="approvedCost" :label="$t('market.walletList.花费代币授权')">
        </el-table-column>
        <el-table-column prop="approvedToken" :label="$t('market.walletList.代币授权')">
        </el-table-column>
      </el-table>
      <p style="margin: 4px; font-size: small">
        {{$t("market.walletList.原生代币总余额")}}:{{ totalETHAmount }}
      </p>
      <p style="margin: 4px; font-size: small">
        {{ tokenSymbol }}{{$t("market.walletList.总余额")}}:{{ totalUSDTAmount }}
      </p>
      <p style="margin: 4px; font-size: small">
        {{ tugouSymbol }}{{$t("market.walletList.总余额")}}:{{ totalERCAmount }}
      </p>
    </el-card>
  </div>
</template>

<script>
import swapJson from "./swap.json";
import { ethers, BigNumber } from "ethers";
export default {
  props: {
    addressData: {
      type: Array,
      required: true,
    },
    ModelForm: {
      type: Object,
      required: true,
    },
    SelectSwapform: {
      type: Object,
    },
  },
  data() {
    return {
      tableData: this.addressData,
      tokenDecimals: 18,
      reqloading: false,
      tokenSymbol: "",
      tugouSymbol: "",
      totalETHAmount: 0,
      totalERCAmount: 0,
      totalUSDTAmount: 0,
      Erc20PairTokendecimals: 18,
    };
  },
  methods: {
    reqWallet() {
      if (this.addressData.length == 0) {
        this.$notify({
          title: this.$t("market.walletList.提示"),
          message: this.$t("market.walletList.m1"),
        });
        return;
      }
      // console.log(this.ModelForm)
      if (this.ModelForm.contractAddress == "") {
        this.$notify({
          title: this.$t("market.walletList.提示"),
          message: this.$t("market.walletList.m2"),
        });
        return;
      }
      // console.log(this.mForm);
      // console.log(this.chainOptions);
      this.reqloading = true;
      const abi = [
        "function balanceOf(address owner) view returns (uint256)",
        "function decimals() view returns (uint8)",
        "function symbol() view returns (string)",
        "function approve(address spender, uint256 amount) external returns (bool)",
        "function allowance(address owner, address spender) external view returns (uint256)",
      ];
      const pairAddress =
        swapJson[this.SelectSwapform.chainId].swap[this.SelectSwapform.swap]
          .tokens[this.ModelForm.poolType];
      const routerAddress =
        swapJson[this.SelectSwapform.chainId].swap[this.SelectSwapform.swap]
          .router;
      // console.log(pairAddress)
      const provider = new ethers.providers.JsonRpcProvider(
        this.SelectSwapform.rpc
      );
      const contractAddress = ethers.utils.getAddress(
        this.ModelForm.contractAddress
      );

      const Erc20PairToken = new ethers.Contract(pairAddress, abi, provider);
      const Erc20Token = new ethers.Contract(contractAddress, abi, provider);
      this.totalETHAmount = 0;
      this.totalERCAmount = 0;
      this.totalUSDTAmount = 0;
      Erc20Token.decimals().then((res) => {
        // 余额是 BigNumber (in wei); 格式化为 ether 字符串
        this.tokenDecimals = res;
      });
      Erc20Token.symbol().then((res) => {
        // 余额是 BigNumber (in wei); 格式化为 ether 字符串
        this.tugouSymbol = res;
      });
      Erc20PairToken.symbol().then((res) => {
        // 余额是 BigNumber (in wei); 格式化为 ether 字符串
        this.tokenSymbol = res;
      });
      Erc20PairToken.decimals().then((res) => {
        // 余额是 BigNumber (in wei); 格式化为 ether 字符串
        this.Erc20PairTokendecimals = res;
        console.log(this.Erc20PairTokendecimals);
      });
      this.addressData.forEach((row, index) => {
        const address = row.address;
        provider.getBalance(address).then((balance) => {
          this.totalETHAmount += new Number(balance.toString() / 10 ** 18);
          this.addressData[index].ETHBalance = parseFloat(
            new Number(balance.toString()) / 10 ** 18
          ).toFixed(18);
        });

        Erc20Token.balanceOf(address).then((balance) => {
          // 余额是 BigNumber (in wei); 格式化为 ether 字符串
          this.totalERCAmount += new Number(
            balance.toString() / 10 ** this.tokenDecimals
          );

          const TokenBalance = parseFloat(
            new Number(balance.toString()) / 10 ** this.tokenDecimals
          ).toFixed(this.tokenDecimals);
          this.addressData[index].tokenBalance = TokenBalance;
        });
        Erc20Token.allowance(address, routerAddress).then((amount) => {
          if (amount.gt(ethers.constants.MaxUint256.div(2))) {
            this.addressData[index].approvedToken = "是";
          } else {
            this.addressData[index].approvedToken = "否";
          }
        });
        Erc20PairToken.balanceOf(address).then((balance) => {
          // 余额是 BigNumber (in wei); 格式化为 ether 字符串
          // let decimals = 18;
          // if (this.ModelForm.poolType == "USDC") {
          //   decimals = 6;
          // }
          this.totalUSDTAmount += new Number(
            balance.toString() / 10 ** this.Erc20PairTokendecimals
          );
          const USDTBalance = parseFloat(
            new Number(balance.toString()) / 10 ** this.Erc20PairTokendecimals
          ).toFixed(this.Erc20PairTokendecimals);
          this.addressData[index].USDTBalance = USDTBalance;
        });
        Erc20PairToken.allowance(address, routerAddress).then((amount) => {
          if (amount.gt(ethers.constants.MaxUint256.div(2))) {
            this.addressData[index].approvedCost = "是";
          } else {
            this.addressData[index].approvedCost = "否";
          }
        });
      });
      setTimeout(() => {
        this.reqloading = false;
      }, 5000);
    },
  },
  watch: {
    addressData(newVal) {
      this.tableData = newVal.slice();
    },
  },
};
</script>
