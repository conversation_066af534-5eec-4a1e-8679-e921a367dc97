<template>
  <div class="console-class">
    <el-card>
      <el-form>
        <el-form-item :label="$t('market.日志窗口')">
          <el-input type="textarea" :readonly="true" :rows="15" v-model="textarea" > </el-input>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
export default {
  props: {
    logs: {
      type: String,
      required: true,
    },
  },
  data() {
    return {
      textarea: this.logs,
    };
  },
  watch: {
    logs(newVal) {
      // console.log(newVal)
      this.textarea = newVal;
    },
  },
};
</script>
<style>
.console-class{
    margin-top: 10px;
}
.margins{
    margin-top: 10px;
}
.el-textarea__inner{
  color:crimson !important;
}
</style>