<template>
  <div class="model-class">
    <el-card>
      <el-form
        ref="form"
        :model="ModelForm"
        label-position="top"
        @submit.prevent="checkPool"
      >
        <el-form-item :label="$t('market.model.钱包使用方式')">
          <el-radio-group v-model="ModelForm.userWalletOrder">
            <el-radio :label="true">{{$t("market.model.顺序")}}</el-radio>
            <el-radio :label="false">{{$t("market.model.随机")}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item  :label="$t('market.model.模式')" >
          <el-radio-group v-model="ModelForm.model">
            <el-radio :label="1">{{$t("market.model.拉盘")}}</el-radio>
            <el-radio :label="2">{{$t("market.model.砸盘")}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="$t('market.model.代币合约地址')">
          <el-input v-model="ModelForm.contractAddress"></el-input>
        </el-form-item>
        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item :label="$t('market.model.池子类型')">
              <el-select
                v-model="ModelForm.poolType"
                :placeholder="$t('market.model.请选择池子类型')"
              >
                <el-option
                  v-for="(item, i) in tokensOptions"
                  :key="i"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="show ? $t('market.model.获得代币') : $t('market.model.花费代币')">
              <el-select
                v-model="ModelForm.poolType"
                :placeholder="$t('market.model.请选择花费代币')"
              >
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item :label="$t('market.model.目标价格')">
          <el-input v-model="ModelForm.price"></el-input>
        </el-form-item>
        <el-form-item :label="show ? $t('market.model.卖出计算方式') : $t('market.model.买入计算方式')">
          <el-radio-group v-model="ModelForm.sellMode">
            <el-radio v-if="!show" :label="1">{{$t("market.model.数量")}}</el-radio>
            <!-- <el-radio v-if="!show" :label="2">数量</el-radio> -->
            <el-radio v-if="show" :label="1">{{$t("market.model.数量")}}</el-radio>
            <!-- <el-radio v-if="show" :label="2">金额</el-radio> -->
            <el-radio v-if="show" :label="3">{{$t("market.model.百分比")}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="sellAmountLabel">
          <!-- <el-input v-model="ModelForm.sellAmount"></el-input> -->
          <el-row :gutter="24">
            <el-col :span="11">
              <el-input
                v-model="ModelForm.sellAmount1"
              ></el-input>
            </el-col>
            <el-col :span="2" style="text-align: center">~</el-col>
            <el-col :span="11">
              <el-input
                v-model="ModelForm.sellAmount2"
              ></el-input>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item :label="$t('market.model.时间间隔')">
          <el-row :gutter="24">
            <el-col :span="11">
              <el-input-number
                v-model="ModelForm.time.s"
                :precision="2"
                :step="0.1"
                :max="10000"
              ></el-input-number>
            </el-col>
            <el-col :span="2" style="text-align: center">~</el-col>
            <el-col :span="11">
              <el-input-number
                v-model="ModelForm.time.e"
                :precision="2"
                :step="0.1"
                :max="10000"
              ></el-input-number>
            </el-col>
          </el-row>
        </el-form-item>
        <el-button @click="checkPool" :loading="checkPoolLoading" type="primary"
          >{{$t('market.model.查池子')}}</el-button
        >
      </el-form>
    </el-card>
  </div>
</template>
<script>
export default {
  props: {
    swapJson: {
      type: Object,
    },
    SelectSwapform: {
      type: Object,
    },
    checkPoolLoading: {
      type: Boolean,
      require: true,
    },
    vipShow:false
  },
  data() {
    return {
      ModelForm: {
        // contractAddress: "******************************************",
        contractAddress: "******************************************",
        price: "",
        poolType: 0,
        costToken: "",
        sellAmount: 1,
        sellAmount1: 1,
        sellAmount2: 1,
        userWalletOrder: true, //钱包使用顺序 true 顺序 false 随机
        sellMode: 1,
        model: 1,
        type: "",
        time: { s: 1, e: 2 },
      },
      show: false,
      sellAmountLabel: this.$t("market.model.金额范围"),
      tokensOptions: [],
    };
  },
  methods: {
    checkPool() {
      if(!this.vipShow){
        this.$notify.error({
          title: this.$t("market.错误"),
          message: this.$t('market.model.您还不是VIP'),
        });
        return
      }
      this.$emit("form-submitted", this.ModelForm);
    },
    getSellAmount(m, n) {
      n = parseFloat(n)
      m = parseFloat(m)
      if (m > n) {
        const temp = m;
        m = n;
        n = temp;
      }
      const randomNumber = (Math.random() * (n - m) + m).toFixed(8);
      return randomNumber
    },
  },
  created() {
    var tokens =
      this.swapJson[this.SelectSwapform.chainId].swap[this.SelectSwapform.swap]
        .tokens;
    for (var t in tokens) {
      var temp = { label: t, value: t };
      this.tokensOptions.push(temp);
    }
    this.ModelForm.poolType = this.tokensOptions[0].value;
    this.ModelForm.costToken = this.tokensOptions[0].value;
  },
  watch: {
    "ModelForm.sellMode"(v) {
      if (v == 1) {
        this.sellAmountLabel = this.$t("market.model.金额范围");
      } else if (v == 2) {
        this.sellAmountLabel = this.$t("market.model.数量范围") 
      } else {
        this.sellAmountLabel = this.$t("market.model.百分比");
      }
    },
    "ModelForm.model"(v) {
      this.show = !this.show;
      this.ModelForm.sellMode = 1;
      this.ModelForm.sellMode = this.ModelForm.sellMode;
    },
    "SelectSwapform.chainId"() {
      // console.log(this.SelectSwapform.chainId);
      this.tokensOptions = [];
      var tokens =
        this.swapJson[this.SelectSwapform.chainId].swap[
          this.SelectSwapform.swap
        ].tokens;
      console.log(tokens);
      for (var t in tokens) {
        var temp = { label: t, value: t, chainId: this.SelectSwapform.chainId };
        this.tokensOptions.push(temp);
      }
      this.ModelForm.poolType = this.tokensOptions[0].value;
      this.ModelForm.costToken = this.tokensOptions[0].value;
    },
    "SelectSwapform.swap"(v) {
      // console.log(this.SelectSwapform.chainId)
      // console.log(this.SelectSwapform.swap)
      this.tokensOptions = [];
      var tokens =
        this.swapJson[this.SelectSwapform.chainId].swap[
          this.SelectSwapform.swap
        ].tokens;
      console.log(tokens);
      for (var t in tokens) {
        var temp = { label: t, value: t, chainId: this.SelectSwapform.chainId };
        this.tokensOptions.push(temp);
      }
      this.ModelForm.poolType = this.tokensOptions[0].value;
      this.ModelForm.costToken = this.tokensOptions[0].value;
    },
    "ModelForm.sellAmount1"() {
      this.ModelForm.sellAmount = this.getSellAmount(this.ModelForm.sellAmount1,this.ModelForm.sellAmount2);
      // console.log(this.ModelForm.sellAmount )
    },
    "ModelForm.sellAmount2"() {
      this.ModelForm.sellAmount = this.getSellAmount(this.ModelForm.sellAmount1,this.ModelForm.sellAmount2);
      // console.log(this.ModelForm.sellAmount )
    },
  },
};
</script>
<style>
.model-class {
  margin-top: 10px;
}
</style>
