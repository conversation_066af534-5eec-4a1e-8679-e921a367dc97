<template>
  <div class="app-container">
    <el-alert
      v-show="chainErr"
      :title="$t('market.请切换至币安链')"
      type="error"
      :description="$t('market.请切换至币安链')"
      center
      :closable="false"
      show-icon
    >
    </el-alert>
    <el-card>
      <el-row v-if="!vipShow">
        <el-col :span="24"
          ><div
            style="display: flex; align-items: center; justify-content: center"
          >
            <p>{{ $t("market.该工具需开通会员") }}</p>
          </div></el-col
        >
      </el-row>
      <el-row v-if="vipShow">
        <el-col :span="24"
          ><div
            style="display: flex; align-items: center; justify-content: center"
          >
            <p>{{ vipDate }}</p>
          </div></el-col
        >
      </el-row>
      <el-row
        style="display: flex; align-items: center; justify-content: center"
        v-if="!vipShow"
      >
        <el-radio-group v-model="vipType">
          <el-radio :label="1"
            >{{ $t("market.一天") }}{{ feeDay }} BNB</el-radio
          >
          <el-radio :label="2"
            >{{ $t("market.一周") }}{{ feeWeek }} BNB</el-radio
          >
          <el-radio :label="3"
            >{{ $t("market.一月") }}{{ feeMonth }} BNB</el-radio
          >
          <el-radio :label="4"
            >{{ $t("market.永久") }}{{ forever }} BNB</el-radio
          >
        </el-radio-group>
      </el-row>
      <el-row
        v-if="!vipShow"
        style="
          display: flex;
          align-items: center;
          justify-content: center;
          margin-top: 1rem;
        "
      >
        <!-- <el-button type="primary">下一步</el-button> -->
        <el-button @click="buyVip" type="primary">{{
          $t("market.购买")
        }}</el-button>
      </el-row>
    </el-card>
    <el-row :gutter="24">
      <el-col :xs="24" :sm="12">
        <SelectSwap v-model="SelectSwapform" :swapJson="swapJson"></SelectSwap>
        <ModelCom
          :swapJson="swapJson"
          :SelectSwapform="SelectSwapform"
          @form-submitted="checkPool"
          :checkPoolLoading="checkPoolLoading"
          :vipShow="vipShow"
        ></ModelCom>
      </el-col>
      <el-col :xs="24" :sm="12">
        <ImportKeys
          v-model="addressData"
          :poolAddress="poolAddress"
          :nowPrice="nowPrice"
          :symble="ModelForm.poolType"
        ></ImportKeys>
        <WalletListCom
          :addressData="addressData"
          :ModelForm="ModelForm"
          :SelectSwapform="SelectSwapform"
          :logs="logs"
        ></WalletListCom>
        <el-card>
          <el-row class="margins">
            <el-col
              ><el-button
                :loading="approveCostLoading"
                @click="approveCost"
                type="primary"
                >1.{{ $t("market.花费代币授权") }}</el-button
              ></el-col
            >
            <el-col
              ><el-button
                :loading="approveTokenLoading"
                @click="approveToken"
                class="margins"
                type="primary"
                >2.{{ $t("market.代币授权") }}</el-button
              ></el-col
            >
            <el-col class="margins">
              <el-button
                class="margins"
                :loading="switchs"
                @click="startTask"
                type="primary"
                >{{ $t("market.开始") }}</el-button
              >
              <el-button type="primary" @click="stopTask">{{
                $t("market.停止")
              }}</el-button>
              <el-button type="primary" @click="clearLogs">{{
                $t("market.清空日志")
              }}</el-button>
            </el-col>
          </el-row>
        </el-card>
        <ConsoleCom :logs="logs"></ConsoleCom>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import SelectSwap from "./selectSwap.vue";
import ImportKeys from "./importKeys.vue";
import swapJson from "./swap.json";
import ModelCom from "./model.vue";
import ConsoleCom from "./console.vue";
import WalletListCom from "./walletList.vue";
import { ethers, BigNumber } from "ethers";
import ERC20 from "@/contracts/MostSimple.json";
import PancakeERC20 from "@/contracts/PancakeERC20.json";
import PancakeFactory from "@/contracts/PancakeFactory.json";
import PancakeRouter from "@/contracts/PancakeRouter.json";
import { arrayIndex, randomTime } from "./function";
import vipAbi from "@/contracts/vip.abi.json";
import store from "@/store";
// const vipContractAddress = "******************************************";
// const vipChainId = 97;
const vipContractAddress = "******************************************";
const vipChainId = 56;
export default {
  components: { SelectSwap, ImportKeys, ModelCom, ConsoleCom, WalletListCom },
  data() {
    return {
      SelectSwapform: {
        chainId: "",
        swap: "",
        rpc: "",
      },
      ModelForm: {
        contractAddress: "",
        price: 0,
        poolType: "",
        costToken: "",
        userWalletOrder: true, //钱包使用顺序 true 顺序 false 随机
        sellAmount: 0,
        sellMode: 1,
        model: 1,
        type: "",
        time: { s: 1, e: 2 },
      },
      swapJson: swapJson,
      addressData: [],
      logs: "",
      poolAddress: "xx",
      nowPrice: "",
      switchs: false, //开关
      approveTokenLoading: false,
      approveCostLoading: false,
      checkPoolLoading: false,
      tokenDecimal: 18,
      tokenSymbol: "",
      //vip区
      vipShow: false,
      vipType: 1,
      feeDay: "0.1",
      feeWeek: "0.4",
      feeMonth: "1.0",
      forever: "3.0",
      buyDisable: false,
      vipDate: this.$t("market.未开通VIP"),
      chainErr: false,
      pairDecimal: 18,
      tokenADecimals: 18,
      tokenBDecimals: 18,
    };
  },
  methods: {
    startTask() {
      const provider = store.state.user.provider;
      // const signer = provider.getSigner(store.state.user.address);

      if (!this.checkParams()) {
        return;
      }
      if (this.ModelForm.price <= 0) {
        this.addLogs(this.$t("market.请设置目标价格"));
        return;
      }
      this.switchs = true;
      this.task()
        .then(() => {
          this.switchs = false;
        })
        .catch((err) => {
          // this.switchs = false;
          console.log(err);
          this.notify(this.$t("market.网络错误请检查网络节点"), 2);
          this.addLogs(err.message);
        });
    },

    async task() {
      const sleep = (delay) =>
        new Promise((resolve) => setTimeout(resolve, delay));
      // this.addLogs(``)
      const routerABI = PancakeRouter;
      const pairABI = PancakeERC20.abi;
      const provider = new ethers.providers.JsonRpcProvider(
        this.SelectSwapform.rpc
      );
      const contractAddress = ethers.utils.getAddress(
        this.ModelForm.contractAddress
      );
      const pairAddress =
        swapJson[this.SelectSwapform.chainId].swap[this.SelectSwapform.swap]
          .tokens[this.ModelForm.poolType];
      const Erc20PairToken = new ethers.Contract(
        pairAddress,
        ERC20.abi,
        provider
      );
      const Erc20Token = new ethers.Contract(
        contractAddress,
        ERC20.abi,
        provider
      );
      const router = new ethers.Contract(
        swapJson[this.SelectSwapform.chainId].swap[
          this.SelectSwapform.swap
        ].router,
        routerABI,
        provider
      );

      const pool = new ethers.Contract(this.poolAddress, pairABI, provider);
      const gasPrice = await provider.getGasPrice();
      console.log("gasPrice:", gasPrice.toNumber());
      var round = 1;
      var next = round + 1;
      while (this.switchs) {
        let index = arrayIndex(
          this.addressData.length,
          this.ModelForm.userWalletOrder
        );
        for (var i in index) {
          // console.log(this.addressData[index[i]]);
          if (!this.switchs) {
            this.addLogs(this.$t("market.交易停止"));
            break;
          }
          const count = index[i] + 1;
          this.addLogs(
            //`第${count}个钱包:${this.addressData[index[i]].address}开始交易`
            this.$t("market.Log1", {
              count: count,
              address: this.addressData[index[i]].address,
            })
          );
          this.ModelForm.sellAmount = this.getSellAmount(
            this.ModelForm.sellAmount1,
            this.ModelForm.sellAmount2
          );
          const op = this.operate();
          const deadline = new Date().getTime() + 30000;
          //根据操作计算交易金额
          let buyAmount;
          console.log("this.ModelForm.sellMode", this.ModelForm.sellMode);
          //拉盘
          let fuckDecimal = 18;
          if (this.ModelForm.model == 1) {
            fuckDecimal = this.tokenADecimals;
          } else if (this.ModelForm.model == 2) {
            fuckDecimal = this.tokenBDecimals;
          }

          if (this.ModelForm.sellMode == 1) {
            buyAmount = ethers.utils.parseUnits(
              this.ModelForm.sellAmount.toString(),
              fuckDecimal
            );
          } else if (this.ModelForm.sellMode == 2) {
            buyAmount = ethers.utils.parseUnits(
              this.ModelForm.sellAmount.toString(),
              fuckDecimal
            );
          } else {
            console.log(this.ModelForm.sellAmount);
            const balanceOfToken = await Erc20Token.balanceOf(
              this.addressData[index[i]].address
            );
            if (Number(this.ModelForm.sellAmount) >= 100) {
              buyAmount = balanceOfToken;
            } else {
              // var amount =
              //   (this.addressData[index[i]].tokenBalance *
              //     this.ModelForm.sellAmount) /
              //   100;
              buyAmount = balanceOfToken
                .div(100)
                .mul(Math.floor(this.ModelForm.sellAmount));
              // buyAmount = ethers.utils.parseUnits(
              //   amount.toString(),
              //   this.tokenDecimal
              // );
            }
          }
          let sleepTime = randomTime(
            this.ModelForm.time.s,
            this.ModelForm.time.e
          );

          //根据用户操作选择交易方法
          const toAddress = this.addressData[index[i]].address;
          const wallet = new ethers.Wallet(
            this.addressData[index[i]].prvKey,
            provider
          );
          const contractWithSigner = router.connect(wallet);
          // const gasPrice = ethers.utils.parseUnits("1", "gwei")
          // const gasLimit = ethers.utils.parseUnits("1","gwei")
          const amountIn = buyAmount;
          if (amountIn.lte(0)) {
            this.addLogs(this.$t("market.交易金额为0跳过"));
            continue;
          }
          let tx;
          this.addLogs(this.$t(`开始执行交易`));
          console.log("amount In", amountIn.toString());
          try {
            switch (op.func) {
              case "swapExactTokensForTokens":
                // console.log(op.path)
                // console.log(amountIn.toString())
                tx =
                  await contractWithSigner.swapExactTokensForTokensSupportingFeeOnTransferTokens(
                    amountIn,
                    233,
                    op.path,
                    toAddress,
                    deadline,
                    {
                      gasPrice: gasPrice,
                    }
                  );
                console.log("swapExactTokensForTokens");
                break;
              case "swapTokensForExactTokens":
                console.log(op.path);
                if (this.ModelForm.model == 1) {
                  const pairBalance = await Erc20PairToken.balanceOf(toAddress);
                  tx = await contractWithSigner.swapTokensForExactTokens(
                    amountIn,
                    pairBalance.div(2),
                    op.path,
                    toAddress,
                    deadline,
                    {
                      gasPrice: gasPrice,
                    }
                  );
                } else {
                  console.log("砸！");
                  const erc20Balance = await Erc20Token.balanceOf(toAddress);
                  tx = await contractWithSigner.swapTokensForExactTokens(
                    amountIn,
                    erc20Balance.div(2),
                    op.path,
                    toAddress,
                    deadline,
                    {
                      gasPrice: gasPrice,
                    }
                  );
                  //砸盘
                }
                console.log("swapTokensForExactTokens");
                break;
              case "swapExactETHForTokens":
                // console.log(op.path)
                tx =
                  await contractWithSigner.swapExactETHForTokensSupportingFeeOnTransferTokens(
                    233,
                    op.path,
                    toAddress,
                    deadline,
                    {
                      value: amountIn,
                      gasPrice: gasPrice,
                    }
                  );
                console.log("swapExactETHForTokens");
                break;
              case "swapTokensForExactETH":
                tx = await contractWithSigner.swapTokensForExactETH(
                  0,
                  amountIn,
                  op.path,
                  toAddress,
                  deadline,
                  {
                    gasPrice: gasPrice,
                  }
                );
                console.log("swapTokensForExactETH");
                break;
              case "swapExactTokensForETH":
                tx =
                  await contractWithSigner.swapExactTokensForETHSupportingFeeOnTransferTokens(
                    amountIn,
                    233,
                    op.path,
                    toAddress,
                    deadline,
                    { gasPrice: gasPrice }
                  );
                console.log("swapExactTokensForETH");
                break;
              case "swapETHForExactTokens":
                const amountMax = await provider.getBalance(toAddress);
                // console.log(amountMax.div(2).toString())
                tx = await contractWithSigner.swapETHForExactTokens(
                  amountIn,
                  op.path,
                  toAddress,
                  deadline,
                  { value: amountMax.div(2), gasPrice: gasPrice }
                );
                console.log("swapETHForExactTokens");
                break;
              default:
                tx = "";
                console.log("error");
            }
            this.addLogs(this.$t("market.交易已发出等待打包确认"));
            await tx.wait();
            this.addLogs(`${this.$t("market.打包确认交易哈希")}: ${tx.hash}`);
            const reserves = await pool.getReserves();
            // console.log(reserves)
            this.addLogs(`${this.$t("market.交易前价格")}` + this.nowPrice);
            this.addLogs(`${this.$t("market.开始查价格")}`);
            const AmountIn = BigNumber.from(
              (10 ** this.tokenDecimal).toString()
            );
            const price = await this.GetPrice(
              AmountIn,
              reserves[0],
              reserves[1]
            );
            this.nowPrice = ethers.utils.formatUnits(
              price.toString(),
              this.pairDecimal
            );
            this.addLogs(`${this.$t("market.交易后价格")}:` + this.nowPrice);
            // console.log("nowPrice",this.nowPrice)

            // 更新钱包余额
            var balance = await Erc20Token.balanceOf(toAddress);
            this.addressData[index[i]].tokenBalance = parseFloat(
              new Number(balance.toString()) / 10 ** this.tokenDecimal
            ).toFixed(this.tokenDecimal);

            var balance = await Erc20PairToken.balanceOf(toAddress);
            this.addressData[index[i]].USDTBalance = parseFloat(
              new Number(balance.toString()) / 10 ** this.pairDecimal
            ).toFixed(this.pairDecimal);

            var balance = await provider.getBalance(toAddress);
            this.addressData[index[i]].ETHBalance = parseFloat(
              new Number(balance.toString()) / 10 ** this.pairDecimal
            ).toFixed(this.pairDecimal);
            var message = `${this.$t("market.Log2", {
              count: count,
              address: this.addressData[index[i]].address,
            })} \n`;
            message += `${this.$t("market.交易哈希")} ${tx.hash} \n`;
            message += `${this.$t("market.Log3", {
              sleepTime: sleepTime / 1000,
            })}`;
            this.addLogs(message);
            // 检查价格是否达到目标价格
            this.autoStopTask();
          } catch (error) {
            console.log(error);
            if (error.message.includes("TRANSFER_FROM_FAILED")) {
              // this.switchs = false;
              this.addLogs(
                this.$t("market.交易需要先授权如果已授权请确保代币余额充足") +
                  error.message
              );
              continue;
            } else {
              this.addLogs(
                this.$t(
                  "market.交易错误，1、钱包确保有足够的代币和手续费(足够请忽略)。2、请检查您的网络"
                ) + `${error.message}`
              );
              this.addLogs(this.$t("market.继续下一个钱包"));
              // this.switchs = false;
              continue;
            }
          }
          // 更新价格

          await sleep(sleepTime);
        }

        this.addLogs(`${this.$t("market.Log4", { round: round })}`);
        round += 1;
        await sleep(1000);
      }
      this.addLogs(this.$t("market.交易已暂停"));
    },

    stopTask() {
      if (this.switchs) {
        this.switchs = false;
        this.addLogs(this.$t("market.正在停止交易"));
      }
    },
    approveCost() {
      const swap =
        swapJson[this.SelectSwapform.chainId].swap[this.SelectSwapform.swap];
      var isETH = false;

      if (swap.WETH == this.ModelForm.poolType) {
        isETH = true;
      }
      if (isETH) {
        this.addLogs(`${this.$t("market.该类型代币无需授权可以交易")}`);
        return;
      }
      if (this.addressData.length == 0) {
        this.notify(this.$t("market.请导入钱包"), 2);
        return;
      }
      if (this.addressData[0].ETHBalance == "") {
        this.notify(this.$t("market.请刷新钱包"), 2);
        return;
      }
      this.approveCostLoading = true;
      const costAddress =
        swapJson[this.SelectSwapform.chainId].swap[this.SelectSwapform.swap]
          .tokens[this.ModelForm.poolType];
      this.Approval(costAddress, 0)
        .then(() => {
          this.notify(this.$t("market.授权成功"));
          this.approveCostLoading = false;
        })
        .catch((e) => {
          this.notify(
            this.$t("market.请确认钱包手续费充足（续费充足请忽略）") +
              `${e.message}`,
            2
          );
          console.log("错误类型" + e);
          this.approveCostLoading = false;
        });
    },
    approveToken() {
      if (this.addressData.length == 0) {
        this.notify(this.$t("market.请导入钱包"));
        return;
      }
      if (this.addressData[0].ETHBalance == "") {
        this.notify(this.$t("market.请刷新钱包"));
        return;
      }
      this.approveTokenLoading = true;
      const address = ethers.utils.getAddress(this.ModelForm.contractAddress);
      this.Approval(address, 1)
        .then(() => {
          this.notify(this.$t("market.授权成功"));
          this.approveTokenLoading = false;
        })
        .catch((e) => {
          this.notify(
            this.$t("market.请确认钱包手续费充足（续费充足请忽略）") +
              `${e.message}`,
            2
          );
          console.log(e);
          this.approveTokenLoading = false;
        });
    },
    checkPool(formData) {
      // externalMethod();
      this.ModelForm = formData;
      // console.log(this.ModelForm);
      this.checkPoolLoading = true;
      this.getPool()
        .then(() => {
          this.notify(this.$t("market.池子查询完成"));
          this.checkPoolLoading = false;
        })
        .catch((err) => {
          this.checkPoolLoading = false;
          console.log(err);
          this.notify(this.$t("market.出现错误") + err.message, 2);
          this.addLogs(this.$t("market.出现错误") + err.message);
        });
      // const routerABI = this.PancakeRouter;
    },
    async getPool() {
      const swap =
        swapJson[this.SelectSwapform.chainId].swap[this.SelectSwapform.swap];

      const tokenPair = swap.tokens[this.ModelForm.poolType]; //池子类型
      const factoryAddress = swap.factoryAddress; //factoryAddress
      // console.log(tokenPair)

      const pairABI = PancakeERC20.abi;
      const factoryABI = PancakeFactory;
      const provider = new ethers.providers.JsonRpcProvider(
        this.SelectSwapform.rpc
      );
      
      const contractAddress = ethers.utils.getAddress(
        this.ModelForm.contractAddress
      );
      console.log("PancakeFactory", PancakeFactory);
       console.log("ERC20", ERC20);
      console.log("factoryABI", factoryABI);
      const factory = new ethers.Contract(factoryAddress, factoryABI, provider);
      
      const tokenA = new ethers.Contract(tokenPair, ERC20.abi, provider);
      
      const decimalsRes = await tokenA.decimals();
      this.pairDecimal = decimalsRes.toNumber();
      this.tokenADecimals = decimalsRes.toNumber();
      console.log("decimalsRes", this.pairDecimal);

      console.log(contractAddress)
      console.log(tokenPair)
      const pairAddress = await factory.getPair(
        contractAddress, //代币地址
        tokenPair
      );
      this.poolAddress = pairAddress;
      console.log(pairAddress);
      if (pairAddress == "******************************************") {
        this.notify(this.$t("market.池子错误请确认您已经添加好了池子"), 2);
        // this.addLogs("池子错误请确认您已经添加好了池子");
        throw new Error(this.$t("market.池子错误请确认您已经添加好了池子"));
      }

      const pool = new ethers.Contract(pairAddress, pairABI, provider);
      console.log("pairAddress", pairAddress);
      const reserves = await pool.getReserves();
      const abi = [
        "function balanceOf(address owner) view returns (uint256)",
        "function decimals() view returns (uint8)",
        "function symbol() view returns (string)",
        "function transfer(address to, uint amount) returns (bool)",
        "function approve(address spender, uint256 amount) external returns (bool)",
        "function allowance(address owner, address spender) external view returns (uint256)",
      ];
      // 当前价格
      const erc20 = new ethers.Contract(contractAddress, abi, provider);
      const tokenDecimals = await erc20.decimals();
      // this.addLogs(tokenDecimals)
      this.tokenSymbol = await erc20.symbol();
      this.tokenDecimal = tokenDecimals;

      const AmountIn = BigNumber.from((10 ** tokenDecimals).toString());

      const price = await this.GetPrice(AmountIn, reserves[0], reserves[1]);
      const token1 = await pool.token1();
      const token0 = await pool.token0();

      // this.addLogs(price)
      this.addLogs(
        this.$t("market.当前价格") +
          ethers.utils.formatUnits(price.toString(), this.pairDecimal)
      );
      this.nowPrice = ethers.utils.formatUnits(
        price.toString(),
        this.pairDecimal
      );
      // console.log('当前价格:',price.toString()/10**tokenDecimals)
    },
    async GetPrice(AmmountIn, ReservesIn, ReservesOut) {
      const pairABI = PancakeERC20.abi;
      const provider = new ethers.providers.JsonRpcProvider(
        this.SelectSwapform.rpc
      );
      const routerABI = PancakeRouter;
      const router = new ethers.Contract(
        swapJson[this.SelectSwapform.chainId].swap[
          this.SelectSwapform.swap
        ].router,
        routerABI,
        provider
      );
      const pairAddress = this.poolAddress;
      const pool = new ethers.Contract(pairAddress, pairABI, provider);
      const token1 = await pool.token1();

      const contractAddress = ethers.utils.getAddress(
        this.ModelForm.contractAddress
      );
      const c = ethers.utils.getAddress(token1);
      if (c != contractAddress) {
        let bigPrice = await router.getAmountOut(
          AmmountIn,
          ReservesIn,
          ReservesOut
        );
        return bigPrice;
      } else {
        let bigPrice = await router.getAmountIn(
          AmmountIn,
          ReservesIn,
          ReservesOut
        );
        return bigPrice;
      }
    },

    async Approval(tokenAddress, type = 1) {
      const routerAddress =
        swapJson[this.SelectSwapform.chainId].swap[this.SelectSwapform.swap]
          .router;
      const provider = new ethers.providers.JsonRpcProvider(
        this.SelectSwapform.rpc
      );
      const gasPrice = await provider.getGasPrice();
      const ERC20Token = new ethers.Contract(tokenAddress, ERC20.abi, provider);
      const MAX =
        "115792089237316195423570985008687907853269984665640564039457584007913129639935";

      this.addLogs(`${tokenAddress}`);
      const sleep = (delay) =>
        new Promise((resolve) => setTimeout(resolve, delay));
      for (let i = 0; i < this.addressData.length; i++) {
        //授权代币 type=1
        if (this.addressData[i].approvedToken == "是" && type == 1) {
          continue;
        }
        //授权花费代币 type=0
        if (this.addressData[i].approvedCost == "是" && type == 0) {
          continue;
        }
        var count = i + 1;
        const wallet = new ethers.Wallet(this.addressData[i].prvKey, provider);
        const contractWithSigner = ERC20Token.connect(wallet);
        const tx = await contractWithSigner.approve(routerAddress, MAX, {
          gasPrice: gasPrice,
        });
        this.addLogs(this.$t("market.Log5", { count: count }) + tx.hash);
        this.addLogs(this.$t("market.正在等待矿工打包"));
        // console.log("result", tx.hash);
        await tx.wait();
        this.addLogs(this.addressData[i].address + this.$t("market.授权完成"));
        await sleep(500);
      }
      this.addLogs(this.$t("market.授权完成"));
    },
    async buyVip() {
      this.buyDisable = true;
      const chainId = store.state.user.chainId;
      if (chainId != vipChainId) {
        this.notify(this.$t("market.请切换至币安链"), 2);
        return;
      }
      const provider = store.state.user.provider;
      const gasPrice = await provider.getGasPrice();
      const signer = provider.getSigner(store.state.user.address);
      const vipContract = new ethers.Contract(
        vipContractAddress,
        vipAbi,
        signer
      );
      // console.log(ethers.utils.parseUnits(this.feeDay))
      try {
        let res = null;
        if (this.vipType == 1) {
          res = await vipContract.payForValidTime(store.state.user.address, 1, {
            value: ethers.utils.parseUnits(this.feeDay),
            gasPrice: gasPrice,
          });
        }
        if (this.vipType == 2) {
          res = await vipContract.payForValidTime(store.state.user.address, 2, {
            value: ethers.utils.parseUnits(this.feeWeek),
            gasPrice: gasPrice,
          });
        }
        if (this.vipType == 3) {
          res = await vipContract.payForValidTime(store.state.user.address, 3, {
            value: ethers.utils.parseUnits(this.feeMonth),
            gasPrice: gasPrice,
          });
        }
        if (this.vipType == 4) {
          res = await vipContract.payForValidTime(store.state.user.address, 4, {
            value: ethers.utils.parseUnits(this.forever),
            gasPrice: gasPrice,
          });
        }
        if (res) {
          await provider.waitForTransaction(res.hash, 1);
          this.buyDisable = false;
          this.notify(res.hash, 1);
          const usersVaildTime = await vipContract.UsersVaildTime(
            store.state.user.address
          );
          const currentTimestampMillis = new Date().getTime();
          const currentTimestampSeconds = Math.floor(
            currentTimestampMillis / 1000
          );
          if (
            usersVaildTime.gt(0) &&
            usersVaildTime.sub(currentTimestampSeconds) > 0
          ) {
            this.vipDate =
              this.$t("market.到期时间") +
              this.formatUnixTimestamp(usersVaildTime.toString());
            // console.log(usersVaildTime.toString());
            this.vipShow = true;
          }
          return;
        } else {
          this.notify(this.$t("market.错误404"), 2);
        }
      } catch (e) {
        this.buyDisable = false;
        console.log(e);
        this.notify(this.$t("market.出现错误，请检查您的网络或者余额"), 2);
      }

      this.buyDisable = false;
      // console.log(res);
    },
    //是否达到停止价格
    autoStopTask() {
      // 砸盘
      console.log(this.nowPrice, this.ModelForm.price,this.ModelForm.model)
      if (this.ModelForm.model == 2) {
        if (parseFloat(this.nowPrice) < parseFloat(this.ModelForm.price)) {
          this.switchs = false;
          this.addLogs(this.$t("market.达到目标价格程序即将停止"));
        }
      }
      //拉盘
      if (this.ModelForm.model == 1) {
        if (parseFloat(this.nowPrice) > parseFloat(this.ModelForm.price)) {
          // console.log(this.nowPrice)
          // console.log(this.ModelForm.price)
          this.switchs = false;
          this.addLogs(this.$t("market.达到目标价格程序即将停止"));
        }
      }
    },
    operate() {
      const swap =
        swapJson[this.SelectSwapform.chainId].swap[this.SelectSwapform.swap];
      // const rpc = this.SelectSwapform.rpc
      var isETH = false;
      const tokenAddress = ethers.utils.getAddress(
        this.ModelForm.contractAddress
      );
      const pairAddress = swap.tokens[this.ModelForm.poolType];
      if (swap.WETH == this.ModelForm.poolType) {
        isETH = true;
      }
      var message = "";
      if (this.ModelForm.model == 1) {
        const path = [tokenAddress, pairAddress];
        ///拉盘 path[0] tokenAddress path[1] pairAddress

        if (this.ModelForm.sellMode == 1) {
          //固定金额买入
          message +=
            this.$t("market.买入金额") + this.ModelForm.sellAmount + this.ModelForm.poolType;
          this.addLogs(message);
          if (!isETH) {
            var func = "swapExactTokensForTokens";
            const ePath = [pairAddress, tokenAddress];
            return { func: func, path: ePath };
          }
          const ePath = [pairAddress, tokenAddress];
          var func = "swapExactETHForTokens";
          return { func: func, path: ePath };
        } else if (this.ModelForm.sellMode == 2) {
          //固定数量买入
          message += this.$t("market.买入数量") + this.ModelForm.sellAmount + this.tokenSymbol;
          this.addLogs(message);
          if (!isETH) {
            const ePath = [pairAddress, tokenAddress];
            var func = "swapTokensForExactTokens";
            return { func: func, path: ePath };
          }
          const ePath = [pairAddress, tokenAddress];
          var func = "swapETHForExactTokens";
          return { func: func, path: ePath };
        }
      } else {
        const path = [pairAddress, tokenAddress];
        var func = "";
        //砸盘 path[0] pairAddress  path[1] tokenAddress
        if (this.ModelForm.sellMode == 1) {
          message += this.$t("market.卖出数量") + this.ModelForm.sellAmount + this.tokenSymbol;
          //卖出固定数量代币
          this.addLogs(message);
          if (!isETH) {
            const ePath = [tokenAddress, pairAddress];
            var func = "swapExactTokensForTokens";
            return { func: func, path: ePath };
          }
          const ePath = [tokenAddress, pairAddress];
          func = "swapExactTokensForETH";
          return { func: func, path: ePath };
        } else if (this.ModelForm.sellMode == 3) {
          //按当前持有量的百分比卖出
          message +=
            this.$t("market.按钱包持有量的") +
            this.ModelForm.sellAmount +
            "% "+this.$t("market.卖出") +
            this.tokenSymbol;
          this.addLogs(message);
          if (!isETH) {
            var func = "swapExactTokensForTokens";
            const ePath = [tokenAddress, pairAddress];
            return { func: func, path: ePath };
          }
          const ePath = [tokenAddress, pairAddress];
          func = "swapExactTokensForETH";
          return { func: func, path: ePath };
        } else if (this.ModelForm.sellMode == 2) {
          message +=
            this.$t("market.卖出价值") +":" +
            this.ModelForm.sellAmount +
            this.ModelForm.poolType +
            
            this.tokenSymbol;
          message += this.$t("market.注意如果持有的代币价值不足兑换可能会失败");
          //卖出固定金额代币
          this.addLogs(message);
          if (!isETH) {
            const ePath = [tokenAddress, pairAddress];
            var func = "swapTokensForExactTokens";
            return { func: func, path: ePath };
          }
          const ePath = [tokenAddress, pairAddress];
          func = "swapTokensForExactETH";
          return { func: func, path: ePath };
        }
      }
    },

    checkParams() {
      // console.log(this.ModelForm);
      if (this.ModelForm.contractAddress == "") {
        this.notify(this.$t("market.先查池子"), 2);
        return false;
      }
      if (
        isNaN(this.ModelForm.sellAmount) ||
        parseFloat(this.ModelForm.sellAmount) <= 0
      ) {
        this.notify(this.$t("market.请输入正确的卖出数量"), 2);
        return false;
      }
      if (
        isNaN(this.ModelForm.price) ||
        parseFloat(this.ModelForm.price) <= 0
      ) {
        this.notify(this.$t("market.请输入正确的目标价格"), 2);
        return false;
      }
      if (this.ModelForm.model == 1) {
        //拉盘的目标价格不能小于等于当前价格
        if (parseFloat(this.ModelForm.price) <= parseFloat(this.nowPrice)) {
          this.notify(this.$t("market.拉盘的目标价格不能小于等于当前价格"), 2);
          return false;
        }
      } else {
        //砸盘的目标价格不能大于当前价格
        if (parseFloat(this.ModelForm.price) >= parseFloat(this.nowPrice)) {
          this.notify(this.$t("market.砸盘的目标价格不能大于当前价格"), 2);
          return false;
        }
      }
      return true;
    },
    clearLogs() {
      this.logs = "";
    },
    addLogs(c) {
      const currentDate = new Date();
      const formattedTime = currentDate.toLocaleString();
      this.logs += formattedTime + " : " + c + " \n";
    },
    getSellAmount(m, n) {
      n = parseFloat(n);
      m = parseFloat(m);
      if (m > n) {
        const temp = m;
        m = n;
        n = temp;
      }
      const randomNumber = (Math.random() * (n - m) + m).toFixed(5);
      return randomNumber;
    },
    notify(message, type = 1) {
      if (type == 1) {
        this.$notify({
          title: this.$t("market.成功"),
          message: message,
          type: "success",
        });
        return;
      }
      if (type == 2) {
        this.$notify.error({
          title: this.$t("market.错误"),
          message: message,
        });
        return;
      }
      if (type == 3) {
        this.$notify({
          title: this.$t("market.成功"),
          message: message,
          duration: 0,
        });
      }
    },
    formatUnixTimestamp(unixTimestamp) {
      // 创建 Date 对象，将 UNIX 时间戳转换为毫秒
      const date = new Date(unixTimestamp * 1000);

      // 获取年、月、日、时、分、秒
      const year = date.getFullYear();
      const month = (date.getMonth() + 1).toString().padStart(2, "0");
      const day = date.getDate().toString().padStart(2, "0");
      const hours = date.getHours().toString().padStart(2, "0");
      const minutes = date.getMinutes().toString().padStart(2, "0");
      const seconds = date.getSeconds().toString().padStart(2, "0");

      // 拼接成格式化的字符串
      const formattedDate = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;

      return formattedDate;
    },
  },
  created() {
    let Inval = setInterval(async () => {
      if (store.state.user.address) {
        window.clearInterval(Inval);
      }
      const chainId = store.state.user.chainId;
      console.log(chainId);
      if (chainId != vipChainId) {
        this.chainErr = true;
        return;
      }
      // const address = store.state.user.address;
      this.chainErr = false;
      const provider = new ethers.providers.Web3Provider(window.ethereum);
      const signer = provider.getSigner(store.state.user.address);
      const vipContract = new ethers.Contract(
        vipContractAddress,
        vipAbi,
        signer
      );
      const fee1 = await vipContract.validFee1();
      const fee2 = await vipContract.validFee2();
      const fee3 = await vipContract.validFee3();
      const ulimitedFee = await vipContract.ulimitedFee();
      this.feeDay = ethers.utils.formatEther(fee1);
      this.feeWeek = ethers.utils.formatEther(fee2);
      this.feeMonth = ethers.utils.formatEther(fee3);
      this.forever = ethers.utils.formatEther(ulimitedFee);
      const usersVaildTime = await vipContract.UsersVaildTime(
        store.state.user.address
      );
      // console.log(usersVaildTime)
      const currentTimestampMillis = new Date().getTime();
      const currentTimestampSeconds = Math.floor(currentTimestampMillis / 1000);

      if (
        usersVaildTime.gt(0) &&
        usersVaildTime.sub(currentTimestampSeconds) > 0
      ) {
        this.vipDate =
          this.$t("market.到期时间") + this.formatUnixTimestamp(usersVaildTime.toString());
        this.vipShow = true;
      }
    }, 500);
  },
};
</script>
