{"1": {"chainId": 1, "name": "Ethereum", "rpc": "https://eth-mainnet.g.alchemy.com/v2/********************************", "rpcs": ["https://eth-mainnet.public.blastapi.io", "https://eth-pokt.nodies.app", "https://eth.drpc.org", "https://eth.llamarpc.com"], "swap": {"pancake": {"name": "Uniswap", "WETH": "ETH", "factoryAddress": "******************************************", "router": "******************************************", "tokens": {"ETH": "******************************************", "USDT": "******************************************"}}}}, "97": {"chainId": 97, "name": "BSCTestnet", "rpc": "https://bsc-testnet-rpc.publicnode.com", "rpcs": ["https://bsc-testnet-rpc.publicnode.com", "https://data-seed-prebsc-1-s2.binance.org:8545", "https://data-seed-prebsc-1-s2.bnbchain.org:8545", "https://endpoints.omniatech.io/v1/bsc/testnet/public", "https://data-seed-prebsc-2-s1.bnbchain.org:8545", "https://data-seed-prebsc-2-s2.bnbchain.org:8545", "https://bsc-testnet.blockpi.network/v1/rpc/public"], "swap": {"pancakeTest": {"name": "Pancake(测试)", "WETH": "tBNB", "factoryAddress": "******************************************", "router": "******************************************", "tokens": {"tBNB": "******************************************", "tBUSD": "******************************************"}}}}, "56": {"chainId": 56, "name": "BSC", "rpc": "https://bsc-dataseed2.defibit.io", "rpcs": ["https://binance.llamarpc.com", "https://rpc-bsc.48.club", "https://bsc-dataseed.binance.org", "https://endpoints.omniatech.io/v1/bsc/mainnet/public", "https://bsc-dataseed1.ninicoin.io", "https://bsc-dataseed2.ninicoin.io", "https://bsc-dataseed1.defibit.io", "https://bsc-dataseed2.defibit.io", "https://bsc-dataseed3.defibit.io", "https://bsc-dataseed4.defibit.io", "https://weathered-fluent-meme.bsc.quiknode.pro/cb398d472213a36aca802a790910bdbeb6de9ee3/"], "swap": {"pancake": {"name": "Pancake V2", "WETH": "BNB", "factoryAddress": "******************************************", "router": "******************************************", "tokens": {"BNB": "******************************************", "BUSD": "******************************************", "USDT": "******************************************"}}}}, "1116": {"chainId": 1116, "name": "CORE", "rpc": "https://rpc.coredao.org", "rpcs": ["https://rpc.coredao.org"], "swap": {"IceCreamswap": {"name": "IceCreamswap", "WETH": "CORE", "factoryAddress": "******************************************", "router": "******************************************", "tokens": {"CORE": "******************************************", "USDT(45F1)": "******************************************", "USDT(5817)": "******************************************"}}, "LFGswap": {"name": "LFGswap", "WETH": "CORE", "factoryAddress": "******************************************", "router": "******************************************", "tokens": {"CORE": "******************************************", "USDT(45F1)": "******************************************", "USDT(5817)": "******************************************"}}, "Archerswap": {"name": "Archerswap", "WETH": "CORE", "factoryAddress": "******************************************", "router": "******************************************", "tokens": {"CORE": "******************************************", "USDT(45F1)": "******************************************", "USDT(5817)": "******************************************"}}}}, "8453": {"chainId": 8453, "name": "BASE", "rpc": "https://base.llamarpc.com", "rpcs": ["https://base.llamarpc.com", "https://base-pokt.nodies.app", "https://base.meowrpc.com", "https://endpoints.omniatech.io/v1/base/mainnet/public", "https://base-rpc.publicnode.com", "https://mainnet.base.org"], "swap": {"uiswap": {"name": "uniswap", "WETH": "ETH", "factoryAddress": "******************************************", "router": "******************************************", "tokens": {"ETH": "******************************************", "USDC": "******************************************"}}}}, "42161": {"chainId": 42161, "name": "ARB", "rpc": "https://arbitrum.llamarpc.com", "rpcs": ["https://arbitrum-one-rpc.publicnode.com", "https://arbitrum.llamarpc.com", "https://arbitrum.meowrpc.com", "https://arb-mainnet-public.unifra.io", "https://arbitrum-one-rpc.publicnode.com", "https://arbitrum.blockpi.network/v1/rpc/public"], "swap": {"uiswap": {"name": "uniswap", "WETH": "ETH", "factoryAddress": "******************************************", "router": "******************************************", "tokens": {"ETH": "******************************************", "USDT": "******************************************"}}}}}