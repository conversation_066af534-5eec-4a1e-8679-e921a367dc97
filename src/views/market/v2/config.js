// bscTestnet
// PancakeV3Factory 0x0BFbCF9fa4f9C56B0F40a671Ad40E0805A091865
// PancakeV2Factory 0x6725F303b657a9451d8BA641348b6761A6CC7a17
// unuversalRouter 0x87FD5305E6a40F378da124864B2D479c2028BD86
// permit2 0x31c2F6fcFf4F8759b3Bd5Bf0e1084A055615c768
// wbnb 0xae13d989daC2f0dEbFf460aC112a837C89BAa7cd
// tbusd 0x8516Fc284AEEaa0374E66037BD2309349FF728eA
export const MARKET_V2_CONFIG = {
  bscTestnet: {
    name: "BSC 测试网",
    nema_en: "BSC Testnet",
    chainId: 97,
    rpcs: [
      "https://bsc-testnet-rpc.publicnode.com",
      "https://data-seed-prebsc-1-s2.binance.org:8545",
      "https://data-seed-prebsc-1-s2.bnbchain.org:8545",
      "https://endpoints.omniatech.io/v1/bsc/testnet/public",
      "https://data-seed-prebsc-2-s1.bnbchain.org:8545",
      "https://data-seed-prebsc-2-s2.bnbchain.org:8545",
      "https://bsc-testnet.blockpi.network/v1/rpc/public",
    ],
    swaps: {
      pancakeV2: {
        name: "PancakeV2",
        nema_en: "PancakeV2",
        poolType: "V2",
        factory: "0x6725F303b657a9451d8BA641348b6761A6CC7a17",
        universalRouter: "0x87FD5305E6a40F378da124864B2D479c2028BD86",
        permit2: "0x31c0x31c2F6fcFf4F8759b3Bd5Bf0e1084A055615c768",
        quote: {
          tBNB: {
            isNative: true,
            address: "0xae13d989daC2f0dEbFf460aC112a837C89BAa7cd",
            decimal: 18,
            name: "tBNB",
            symbol: "tBNB",
          },
          tBUSD: {
            isNative: false,
            address: "0x8516Fc284AEEaa0374E66037BD2309349FF728eA",
            decimal: 18,
            name: "tBUSD",
            symbol: "tBUSD",
          },
        },
      },
      pancakeV3: {
        name: "PancakeV3",
        nema_en: "PancakeV3",
        poolType: "V3",
        factory: "0x0BFbCF9fa4f9C56B0F40a671Ad40E0805A091865",
        universalRouter: "0x87FD5305E6a40F378da124864B2D479c2028BD86",
        permit2: "0x31c0x31c2F6fcFf4F8759b3Bd5Bf0e1084A055615c768",
        quote: {
          tBNB: {
            isNative: true,
            address: "0xae13d989daC2f0dEbFf460aC112a837C89BAa7cd",
            decimal: 18,
            name: "tBNB",
            symbol: "tBNB",
          },
          tBUSD: {
            isNative: false,
            address: "0x8516Fc284AEEaa0374E66037BD2309349FF728eA",
            decimal: 18,
            name: "tBUSD",
            symbol: "tBUSD",
          },
        },
      },
    },
  },
};
