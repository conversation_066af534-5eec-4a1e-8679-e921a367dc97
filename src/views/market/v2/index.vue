<template>
  <div class="app-container">
    <h1>Market V2</h1>
    <p>Welcome to the Market V2 page!</p>
    <p>You can add more content here.</p>
    <input type="text" placeholder="Enter text here" />
    <input type="number" placeholder="Enter number here" />
    <input type="date" />
    <button @click="testpermit">test</button>
  </div>
</template>

<script>
import { ethers, utils } from "ethers";
import {
  CommandType,
  RoutePlanner,
  encodePath,
} from "../../../utils/swap/planner.js";
import { MarketV2Func } from "./func.js";
import { MARKET_V2_CONFIG } from "./config.js";

export default {
  data() {
    return {
      message: "Hello from Market V2!",
    };
  },
  methods: {
    async testfunc() {
      const base = MARKET_V2_CONFIG.bscTestnet.swaps.pancakeV3.quote.tBNB
      const quote = MARKET_V2_CONFIG.bscTestnet.swaps.pancakeV3.quote.tBUSD;
      const dex = {
        chain: "bscTestnet",
        swap: "pancakeV3",
      };
      const t = new MarketV2Func("https://bsc-testnet-rpc.publicnode.com",dex,quote,base,500);
      console.log(t)
      const path = t.getSwapPath([base.address, quote.address]);
      console.log("Encoded Path:", path);
    },
    encodePath1(tokenIn, tokenOut, fee) {
      let c = 200;
      c.toString(16).padStart(6, "0");
      return (
        tokenIn.toLowerCase().replace(/^0x/, "") +
        fee.toString(16).padStart(6, "0") +
        tokenOut.toLowerCase().replace(/^0x/, "")
      );
    },
    async test2() {
      const provider = new ethers.providers.JsonRpcProvider(
        "https://bsc-testnet-rpc.publicnode.com"
      );
      const privateKey =
        "dc735f76123581ff73e2d10f40a25b317fb3026125ffe55ee813adb4f6890054";
      const wallet = new ethers.Wallet(privateKey, provider);

      const universalRouter = "******************************************";
      const WBNB = "******************************************";
      const CAKE = "******************************************";
      const FEE = 500; // 0.05%
      const amountInBNB = ethers.utils.parseEther("0.001"); // 0.01 BNB
      const usefee = ethers.utils.parseEther("0.001");
      const amountOutMin = 0; // 你可以加滑点控制
      const deadline = Math.floor(Date.now() / 1000) + 60 * 10; // 10分钟后过期
      const encodedPath = "0x" + this.encodePath1(CAKE, WBNB, FEE);
      console.log("Encoded Path:", encodedPath);
      const abiCoder = ethers.utils.defaultAbiCoder;
      const iface = new ethers.utils.Interface([
        "function execute(bytes commands, bytes[] inputs, uint256 deadline)",
      ]);
      ethers.constants.EtherSymbol;
      // commands: WRAP_ETH + V3_SWAP_EXACT_IN
      // const commands = "0x0505000c";
      ethers.utils;
      const commands = "0x05";
      //inputs 对应 WRAP_ETH 和 V3_SWAP_EXACT_IN 参数
      const sendEth = abiCoder.encode(
        ["address", "address", "uint256"],
        [
          ethers.constants.AddressZero, // 使用 AddressZero 作为代币地址，表示 BNB
          "******************************************",
          usefee,
        ]
      );
      const sendToken = abiCoder.encode(
        ["address", "address", "uint256"],
        [CAKE, universalRouter, ethers.utils.parseUnits("10000000000", 18)]
      );
      const transferEthInput = abiCoder.encode(
        ["address", "uint256"],
        ["******************************************", usefee]
      );
      const wrapEthInput = abiCoder.encode(
        ["address", "uint256"],
        ["******************************************", amountInBNB]
      );
      const unswapInput = abiCoder.encode(
        ["address", "uint256"],
        [wallet.address, 100]
      );
      const swapInput = abiCoder.encode(
        ["address", "uint256", "uint256", "bytes", "bool"],
        // [wallet.address, amountInBNB, amountOutMin, encodedPath, false] //bnb -> cake
        [
          universalRouter,
          ethers.utils.parseUnits("10000000000", 18),
          amountOutMin,
          encodedPath,
          false,
        ] //cake -> bnb
      );

      const calldata = iface.encodeFunctionData("execute", [
        commands,
        [
          // transferEthInput, // 转账ETH到目标地址
          // wrapEthInput, // 包装ETH为WBNB
          // sendEth, // 转账ETH到目标地址
          sendToken, // 转账CAKE到universalRouter
          // swapInput, // 执行V3交换
          // unswapInput, // 解锁
        ],
        deadline,
      ]);

      // 发起交易
      const tx = await wallet.sendTransaction({
        to: universalRouter,
        data: calldata,
        // value: amountInBNB.add(usefee), // 包括转账和包装的费用
        value: 0,
        gasLimit: 1_000_000,
      });

      console.log("✅ 交易已发送:", tx.hash);
    },
    async bnb2cake() {
      const provider = new ethers.providers.JsonRpcProvider(
        "https://bsc-testnet-rpc.publicnode.com"
      );
      const privateKey =
        "dc735f76123581ff73e2d10f40a25b317fb3026125ffe55ee813adb4f6890054";
      const wallet = new ethers.Wallet(privateKey, provider);
      const universalRouter = "******************************************";
      const WBNB = "******************************************";
      const CAKE = "******************************************";
      const FEE = 500; // 0.05%
      const amountInBNB = ethers.utils.parseEther("0.001"); // 0.01 BNB
      const usefee = ethers.utils.parseEther("0.001");
      const amountOutMin = 0; // 你可以加滑点控制
      const deadline = Math.floor(Date.now() / 1000) + 60 * 10; // 10分钟后过期
      const encodedPath = "0x" + this.encodePath1(WBNB, CAKE, FEE);
      console.log("Encoded Path:", encodedPath);
      const abiCoder = ethers.utils.defaultAbiCoder;
      const iface = new ethers.utils.Interface([
        "function execute(bytes commands, bytes[] inputs, uint256 deadline)",
      ]);
    },
    async test() {
      const universalRouter = "******************************************";
      const wbnb = "******************************************";
      const cake = "******************************************";
      const provider = new ethers.providers.JsonRpcProvider(
        "https://bsc-testnet-rpc.publicnode.com"
      );
      const signer = new ethers.Wallet(
        "dc735f76123581ff73e2d10f40a25b317fb3026125ffe55ee813adb4f6890054",
        provider
      );
      // 钱包余额
      const balance = await signer.getBalance();
      console.log(
        "test Wallet Balance:",
        ethers.utils.formatEther(balance),
        "BNB"
      );
      console.log("test Signer Address:", signer.address);
      const router = new ethers.Contract(
        universalRouter,
        [
          "function execute(bytes commands, bytes[] inputs, uint256 deadline) payable",
        ],
        signer
      );
      // 目标地址
      // const ctx = signer.sendTransaction({
      //   to: "******************************************",
      //   value: ethers.utils.parseEther("0.001"),
      // });

      // console.log("交易已确认，哈希值：", (await ctx).hash);
      const TARGET_ADDRESS = "******************************************";
      const planner = new RoutePlanner();
      const path = encodePath([wbnb, cake]);
      planner.addCommand(CommandType.WRAP_ETH, [
        signer.address,
        // "******************************************",
        ethers.utils.parseEther("0.7"),
      ]);
      planner.addCommand(CommandType.V3_SWAP_EXACT_IN, [
        signer.address,
        ethers.utils.parseEther("0.7"),
        0,
        path,
        true,
      ]);
      const { commands, inputs } = planner;
      const gasPrice = await provider.getGasPrice();
      const deadline = Math.floor(Date.now() / 1000) + 600;
      const tx = await router.execute(commands, inputs, deadline, {
        value: ethers.utils.parseEther("0.7"),
        gasLimit: 1000000,
        // gasPrice: gasPrice
      });

      console.log("交易已发送，哈希值：", tx.hash);
      const receipt = await tx.wait();
      console.log("交易已确认，哈希值：", receipt.transactionHash);
      // try {
      //   const estimatedGas = await router.estimateGas.execute(
      //     commands,
      //     inputs,
      //     deadline,
      //     {
      //       value: ethers.utils.parseEther("0.01"),
      //       gasLimit: 500000, // 设置一个合理的 gas limit
      //       gasPrice: gasPrice, // 设置 gas 价格
      //     }
      //   );
      //   console.log("估算的 Gas：", estimatedGas.toString());
      // } catch (err) {
      //   console.error("模拟提交失败：", err);
      // }
    },
    async testpermit() {
      const provider = new ethers.providers.JsonRpcProvider(
        "https://bsc-testnet-rpc.publicnode.com"
      );
      const owner = "******************************************";
      const token = "******************************************";
      const spender = "******************************************";

      const permit2 = new ethers.Contract(
        // "******************************************",
        "******************************************",
        [
          "function allowance(address owner, address token, address spender) view returns (uint160 amount, uint48 expiration, uint48 nonce)",
        ],
        provider
      );

      const [amount, expiration,nonce] = await permit2.allowance(
        owner,
        token,
        spender
      );
      const test = await permit2.allowance(
        owner,
        token,
        spender
      );
      console.log('test',test)
      console.log(" nonce:", nonce);
      console.log("🎯 授权数量 (10**18Wei 单位):", amount.toString());
      console.log("⏳ 有效期截止 Unix 时间戳:", expiration.toString());

      const now = Math.floor(Date.now() / 1000);
      if (amount.gt(0) && Number(expiration.toString()) > now) {
        console.log("✅ 已授权，并且未过期");
      } else if (amount.gt(0)) {
        console.log("⚠️ 已授权，但已过期");
      } else {
        console.log("❌ 未授权");
      }
    },
  },
};
</script>

<style scoped>
.app-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  font-family: Arial, sans-serif;
}

h1 {
  color: #333;
  margin-bottom: 20px;
}

p {
  color: #666;
  margin-bottom: 10px;
}

input[type="text"],
input[type="number"],
input[type="date"] {
  padding: 10px;
  margin: 5px;
  border: 1px solid #ccc;
  border-radius: 4px;
  width: 200px;
}

button {
  padding: 10px 20px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:hover {
  background-color: #0056b3;
}
</style>
