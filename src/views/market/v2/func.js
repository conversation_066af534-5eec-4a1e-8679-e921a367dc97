import { ethers } from "ethers";
import { MARKET_V2_CONFIG } from "@/views/market/v2/config.js";

export class MarketV2Func {
  provider;
  quoteToken = MARKET_V2_CONFIG.bscTestnet.swaps.pancakeV3.quote.tBUSD;
  baseToken = {
    isNative: false,
    address: "******************************************",
    decimal: 18,
    name: "cake",
    symbol: "cake",
  };
  dex = {
    chain: "bscTestnet",
    swap: "pancakeV3",
  };
  fee = 500; // Default fee for PancakeSwap V3
  constructor(rpc, de, quote, base, fee = 500) {
    this.provider = new ethers.providers.JsonRpcProvider(rpc);
    this.dex = de;
    this.quoteToken = quote;
    this.baseToken = base;
    this.fee = fee;
  }
  getSwapPath(path) {
    console.log(MARKET_V2_CONFIG[this.dex.chain]["swaps"][this.dex.swap]);
    if (
      MARKET_V2_CONFIG[this.dex.chain]["swaps"][this.dex.swap]["poolType"] ===
      "V2"
    ) {
      return path;
    } else if (
      MARKET_V2_CONFIG[this.dex.chain]["swaps"][this.dex.swap]["poolType"] ===
      "V3"
    ) {
      return this.encodePancakeV3Path(path, this.fee);
    } else {
      throw new Error(`Unsupported poolType: ${swap}`);
    }
  }

  encodePancakeV3Path(path, fee = 500) {
    // Implement Pancake V3 path encoding logic here
    console.log("Encoding Pancake V3 path:", path, fee);
    return (
      path[0].toLowerCase().replace(/^0x/, "") +
      fee.toString(16).padStart(6, "0") +
      path[1].toLowerCase().replace(/^0x/, "")
    );
  }
  // Swap functions
  async swapETH2Token(){}
  async swapToken2ETH(){}
  async swapToken2Token(){}
  async getPoolInfo(){}
  async checkAllowance(){}
  async approveToken(){}
  async approvePrimit2(){}
}

