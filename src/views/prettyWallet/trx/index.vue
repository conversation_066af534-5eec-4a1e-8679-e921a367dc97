<template>
  <div class="app-container">
    <!-- 标题区域 -->
    <div class="title-section">
      <el-card class="title-card" shadow="never">
        <div slot="header" class="title-header">
          <i class="el-icon-wallet"></i>
          <span>{{ $t("prettyWallet.trx.title") }}</span>
        </div>
        <div class="info-content">
          <el-alert
            :title="$t('prettyWallet.tips1Title')"
            :description="$t('prettyWallet.tips1Desc')"
            type="warning"
            show-icon
            :closable="false"
            class="info-alert"
          />
          <el-alert
            :title="$t('prettyWallet.tips2Title')"
            :description="$t('prettyWallet.tips2Desc')"
            type="info"
            show-icon
            :closable="false"
            class="info-alert"
          />
          <el-alert
            :title="$t('prettyWallet.tips3Title')"
            :description="$t('prettyWallet.tips3Desc')"
            type="success"
            show-icon
            :closable="false"
            class="info-alert"
          />
        </div>
      </el-card>
    </div>

    <!-- 主要内容区域 -->
    <div class="main1-container">
      <!-- 左侧表单区 -->
      <div class="form-section">
        <el-form :model="form" style="padding: 20px">
          <div class="row">
            <el-form-item
              style="width: 100%"
              :label="$t('prettyWallet.prefix')"
            >
              <el-input
                v-model="form.prefix"
                :placeholder="$t('prettyWallet.placeholder')"
                @input="onInputPrefix"
                :class="{ 'input-error': inputErrors.prefix }"
              />
              <div v-if="inputErrors.prefix" class="error-message">
                {{ $t("prettyWallet.trx.prefixMsg") }}
              </div>
            </el-form-item>
            <el-form-item
              style="width: 100%"
              :label="$t('prettyWallet.suffix')"
            >
              <el-input
                v-model="form.suffix"
                :placeholder="$t('prettyWallet.placeholder')"
                @input="onInputSuffix"
                :class="{ 'input-error': inputErrors.suffix }"
              />
              <div v-if="inputErrors.suffix" class="error-message">
                {{ $t("prettyWallet.trx.suffixMsg") }}
              </div>
            </el-form-item>
          </div>
          <div class="example" style="width: 100%">
            {{ $t("prettyWallet.eg") }}: T<b>{{ form.prefix }}</b
            >RkbWV9Nxns...16rCNKXRZc<b>{{ form.suffix }}</b>
          </div>
          <el-form-item :label="$t('prettyWallet.caseSensitive')">
            <el-radio-group v-model="form.caseSensitive">
              <el-radio :label="false">{{ $t("prettyWallet.no") }}</el-radio>
              <el-radio :label="true">{{ $t("prettyWallet.yes") }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item :label="$t('prettyWallet.workers')">
            <el-input-number v-model="form.workers" :min="1" :max="64" />
            <span class="thread-tip">{{ $t("prettyWallet.workerTips") }}</span>
          </el-form-item>
          <el-alert
            :title="$t('prettyWallet.trx.warning')"
            type="warning"
            show-icon
            :closable="false"
            class="tip"
          />
          <div class="btn-row">
            <el-button
              size="large"
              style="width: 50%"
              :disabled="!isGenerating"
              @click="stopGeneration"
              icon="el-icon-video-pause"
            >
              {{ $t("prettyWallet.stopGen") }}
            </el-button>
            <el-button
              size="large"
              style="width: 50%"
              type="primary"
              :disabled="isGenerating"
              @click="startGeneration"
              icon="el-icon-video-play"
            >
              {{ $t("prettyWallet.startGen") }}
            </el-button>
          </div>
        </el-form>
      </div>
      <!-- 右侧信息区 -->
      <div class="info-section">
        <div class="info-title">{{ $t("prettyWallet.generateInfo") }}</div>
        <div class="info-list">
          <div class="info-item">
            <span>{{ $t("prettyWallet.difficulty") }}</span
            ><span>{{ info.difficulty }}</span>
          </div>
          <div class="info-item">
            <span>{{ $t("prettyWallet.generated") }}</span
            ><span
              >{{ info.generated }} {{ $t("prettyWallet.addresses") }}</span
            >
          </div>
          <div class="info-item">
            <span>{{ $t("prettyWallet.estimateTime") }}</span
            ><span
              >{{ info.estimateTime }} {{ $t("prettyWallet.seconds") }}</span
            >
          </div>
          <div class="info-item">
            <span>{{ $t("prettyWallet.speed") }}</span
            ><span
              >{{ info.speed }} {{ $t("prettyWallet.addressesPerSec") }}</span
            >
          </div>
          <div class="info-item">
            <span>{{ $t("prettyWallet.status") }}</span
            ><span>{{ info.status }}</span>
          </div>
          <div class="info-item">
            <span>{{ $t("prettyWallet.totalTime") }}</span
            ><span>{{ info.totalTime }}</span>
          </div>
        </div>
        <el-progress
          :percentage="info.progress"
          :show-text="false"
          class="progress-bar"
        />
        <div class="progress-text">{{ info.progress }}%</div>
        <!-- 显示找到的靓号 -->
        <div class="found-wallet">
          <div class="wallet-title">{{ $t("prettyWallet.prettyWallet") }}:</div>
          <div class="wallet-address">
            {{ $t("prettyWallet.publicKey") }}:{{
              foundWallet ? foundWallet.checksumAddress : ""
            }}
            <i
              v-if="foundWallet"
              class="el-icon-document-copy copy-icon"
              @click="
                copyToClipboard(
                  foundWallet.checksumAddress,
                  $t('prettyWallet.publicKey')
                )
              "
              :title="$t('prettyWallet.copyPublicKey')"
            ></i>
          </div>
          <div class="wallet-private">
            {{ $t("prettyWallet.privateKey") }}:{{
              foundWallet ? foundWallet.privKey : ""
            }}
            <i
              v-if="foundWallet"
              class="el-icon-document-copy copy-icon"
              @click="
                copyToClipboard(
                  foundWallet.privKey,
                  $t('prettyWallet.privateKey')
                )
              "
              :title="$t('prettyWallet.copyPrivateKey')"
            ></i>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import TronWorker from "@/js/tron-wallet-worker";

export default {
  name: "PrettyWalletEvm",
  data() {
    return {
      form: {
        prefix: "P",
        suffix: "88",
        caseSensitive: false,
        workers: 8,
      },
      inputErrors: {
        prefix: false,
        suffix: false,
      },
      info: {
        difficulty: 1,
        generated: 0,
        estimateTime: 0,
        speed: 0,
        status: "未开始",
        progress: 0,
        totalTime: "0 秒",
      },
      isGenerating: false,
      workers: [],
      foundWallet: null,
      startTime: null,
      speedUpdateTimer: null,
      totalGenerated: 0,
      workerCounts: [],
      lastSpeed: 0,
      updateTotalTimer: null,
    };
  },
  watch: {
    "form.prefix": "calculateDifficulty",
    "form.suffix": "calculateDifficulty",
    "form.caseSensitive": "calculateDifficulty",
  },
  mounted() {
    this.calculateDifficulty();
  },
  methods: {
    // 创建 worker
    createWorker() {
      const worker = new TronWorker();

      // 添加错误处理
      worker.onerror = function (error) {
        console.error("Worker error:", error);
      };

      return worker;
    },

    // 计算难度
    calculateDifficulty() {
      const prefix = this.form.prefix;
      const suffix = this.form.suffix;
      const caseSensitive = this.form.caseSensitive;

      // 计算前缀难度
      let prefixDifficulty = 1;
      if (prefix.length > 0) {
        // 每个字符的难度是16（16进制字符：0-9, A-F, a-f）
        prefixDifficulty = Math.pow(58, prefix.length);
      }

      // 计算后缀难度
      let suffixDifficulty = 1;
      if (suffix.length > 0) {
        // 每个字符的难度是16（16进制字符：0-9, A-F, a-f）
        suffixDifficulty = Math.pow(58, suffix.length);
      }

      // 总难度 = 前缀难度 * 后缀难度
      this.info.difficulty = prefixDifficulty * suffixDifficulty;
    },

    // 开始生成
    async startGeneration() {
      if (this.isGenerating) return;

      this.isGenerating = true;
      this.foundWallet = null;
      this.totalGenerated = 0;
      this.workerCounts = new Array(this.form.workers).fill(0);
      this.startTime = Date.now();

      // 重置状态
      this.info.status = this.$t("prettyWallet.generating");
      this.info.progress = 0;
      this.info.generated = 0;
      this.info.speed = 0;
      this.info.estimateTime = 0;
      this.info.totalTime = `0 ${this.$t("prettyWallet.seconds")}`;
      this.foundWallet = null;

      // 启动速度更新定时器
      this.startSpeedUpdate();

      // 启动所有worker
      await this.startWorkers();
    },

    // 停止生成
    stopGeneration() {
      this.isGenerating = false;
      this.info.status = this.$t("prettyWallet.stopped");

      // 如果没有找到钱包，进度保持在99%以下
      if (!this.foundWallet && this.info.progress >= 99) {
        this.info.progress = 99;
      }

      // 计算总耗时
      if (this.startTime) {
        const totalTimeMs = Date.now() - this.startTime;
        const totalTimeSeconds = Math.floor(totalTimeMs / 1000);
        const totalTimeMinutes = Math.floor(totalTimeSeconds / 60);
        const remainingSeconds = totalTimeSeconds % 60;

        if (totalTimeMinutes > 0) {
          this.info.totalTime = `${totalTimeMinutes} ${this.$t(
            "prettyWallet.minutes"
          )} ${remainingSeconds} ${this.$t("prettyWallet.seconds")}`;
        } else {
          this.info.totalTime = `${totalTimeSeconds} ${this.$t(
            "prettyWallet.seconds"
          )}`;
        }
      }

      // 停止所有worker
      this.workers.forEach((worker) => {
        worker.terminate();
      });
      this.workers = [];

      // 停止速度更新
      if (this.speedUpdateTimer) {
        clearInterval(this.speedUpdateTimer);
        this.speedUpdateTimer = null;
      }
    },

    // 启动worker
    async startWorkers() {
      const { prefix, suffix, caseSensitive, workers } = this.form;
      console.log("Starting workers with:", {
        prefix,
        suffix,
        caseSensitive,
        workers,
      });

      for (let i = 0; i < workers; i++) {
        const worker = this.createWorker();
        // console.log(`Created worker ${i}:`, worker);
        this.workers.push(worker);

        // 监听worker消息
        worker.onmessage = (e) => {
          //   console.log(`Worker ${i} message:`, e.data);
          this.handleWorkerMessage(e, i);
        };

        // 等待一下确保worker初始化完成
        await new Promise((resolve) => setTimeout(resolve, 100));

        // 启动worker
        // console.log(`Starting worker ${i}...`);
        worker.postMessage({ prefix, suffix, caseSensitive });
      }
    },

    // 处理worker消息
    handleWorkerMessage(e, workerIndex) {
      const { type, success, wallet, count } = e.data;
      //   console.log(type, success, wallet, count);

      if (type === "result" && success) {
        // 找到靓号
        this.foundWallet = wallet;
        this.workerCounts[workerIndex] = count;

        // 计算总耗时
        if (this.startTime) {
          const totalTimeMs = Date.now() - this.startTime;
          const totalTimeSeconds = Math.floor(totalTimeMs / 1000);
          const totalTimeMinutes = Math.floor(totalTimeSeconds / 60);
          const remainingSeconds = totalTimeSeconds % 60;

          if (totalTimeMinutes > 0) {
            this.info.totalTime = `${totalTimeMinutes} ${this.$t(
              "prettyWallet.minutes"
            )} ${remainingSeconds} ${this.$t("prettyWallet.seconds")}`;
          } else {
            this.info.totalTime = `${totalTimeSeconds} ${this.$t(
              "prettyWallet.seconds"
            )}`;
          }
        }

        // 停止所有worker
        this.stopGeneration();
        this.info.status = this.$t("prettyWallet.completed");
        this.info.progress = 100;

        // 收集所有worker的count
        this.collectAllWorkerCounts();
      } else if (type === "count") {
        // 收到worker的count报告 - 批量更新，减少计算频率
        this.workerCounts[workerIndex] = count;

        // 使用防抖更新总数量，避免频繁计算
        if (this.updateTotalTimer) {
          clearTimeout(this.updateTotalTimer);
        }
        this.updateTotalTimer = setTimeout(() => {
          this.totalGenerated = this.workerCounts.reduce(
            (sum, count) => sum + count,
            0
          );
        }, 100);
      }
    },

    // 收集所有worker的count
    collectAllWorkerCounts() {
      // 向所有worker请求count
      this.workers.forEach((worker) => {
        worker.postMessage({ type: "getCount" });
      });

      // 等待所有count收集完成
      setTimeout(() => {
        this.totalGenerated = this.workerCounts.reduce(
          (sum, count) => sum + count,
          0
        );
        this.info.generated = this.totalGenerated;
      }, 100);
    },

    // 启动速度更新
    startSpeedUpdate() {
      this.speedUpdateTimer = setInterval(() => {
        // console.log(this.isGenerating);
        if (!this.isGenerating) return;

        const now = Date.now();
        const elapsed = (now - this.startTime) / 1000; // 秒

        if (elapsed > 0) {
          // 更新已生成数量
          this.info.generated = this.totalGenerated;
          this.info.speed = Math.floor(this.totalGenerated / elapsed);

          // 计算预计时间 - 只在速度变化时计算
          if (this.info.speed > 0 && this.info.speed !== this.lastSpeed) {
            const expectedAttempts = this.info.difficulty;
            this.info.estimateTime = Math.ceil(
              expectedAttempts / this.info.speed
            );
            this.lastSpeed = this.info.speed;
          }

          // 更新进度（基于已生成数量与预期难度的比例）
          // 在找到钱包前最大显示99%，避免显示100%但还没找到
          if (this.totalGenerated > 0) {
            const progress = Math.min(
              99,
              (this.totalGenerated / this.info.difficulty) * 100
            );
            this.info.progress = Math.floor(progress);
          } else {
            this.info.progress = 0;
          }

          // 更新当前运行时间
          const totalTimeSeconds = Math.floor(elapsed);
          const totalTimeMinutes = Math.floor(totalTimeSeconds / 60);
          const remainingSeconds = totalTimeSeconds % 60;

          if (totalTimeMinutes > 0) {
            this.info.totalTime = `${totalTimeMinutes} ${this.$t(
              "prettyWallet.minutes"
            )} ${remainingSeconds} ${this.$t("prettyWallet.seconds")}`;
          } else {
            this.info.totalTime = `${totalTimeSeconds} ${this.$t(
              "prettyWallet.seconds"
            )}`;
          }
        }
      }, 1000); // 每秒更新一次
    },

    // 复制到剪贴板
    async copyToClipboard(text, type) {
      try {
        await navigator.clipboard.writeText(text);
        this.$message.success(
          `${type}${this.$t("prettyWallet.copiedToClipboard")}`
        );
      } catch (err) {
        // 降级方案：使用传统方法
        const textArea = document.createElement("textarea");
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        try {
          document.execCommand("copy");
          this.$message.success(
            `${type}${this.$t("prettyWallet.copiedToClipboard")}`
          );
        } catch (fallbackErr) {
          this.$message.error(this.$t("prettyWallet.copyFailed"));
        }
        document.body.removeChild(textArea);
      }
    },

    // 输入过滤 - 前缀第一位只能是不包含O和I的大写字母，其余符合Base58字符集
    onInputPrefix(val) {
      const originalValue = val;
      let filteredValue = val;
      
      // Base58字符集：**********************************************************
      // 排除：0, O, I, l
      const base58Chars = /[**********************************************************]/g;
      
      if (val.length > 0) {
        // 第一位只能是不包含O和I的大写字母
        const firstChar = val[0];
        const validFirstChar = /[ABCDEFGHJKLMNPQRSTUVWXYZ]/;
        
        if (!validFirstChar.test(firstChar)) {
          // 如果第一位不符合要求，替换为有效字符或移除
          filteredValue = val.replace(/^[^ABCDEFGHJKLMNPQRSTUVWXYZ]/, '');
          this.inputErrors.prefix = true;
          setTimeout(() => {
            this.inputErrors.prefix = false;
          }, 5000);
        } else {
          // 第一位符合要求，过滤其余字符
          const firstCharValid = firstChar;
          const restChars = val.slice(1).replace(/[^**********************************************************]/g, '');
          filteredValue = firstCharValid + restChars;
          
          if (val !== filteredValue) {
            this.inputErrors.prefix = true;
            setTimeout(() => {
              this.inputErrors.prefix = false;
            }, 5000);
          }
        }
      } else {
        // 空值直接通过
        filteredValue = val;
      }

      this.form.prefix = filteredValue;
    },
    onInputSuffix(val) {
      const originalValue = val;
      // Base58字符集：**********************************************************
      // 排除：0, O, I, l
      const filteredValue = val.replace(/[^**********************************************************]/g, "");

      // 检查是否有被过滤掉的字符
      if (originalValue !== filteredValue) {
        this.inputErrors.suffix = true;
        //this.$message.error('后缀只能包含Base58字符集中的字符');
        // 2秒后清除错误状态
        setTimeout(() => {
          this.inputErrors.suffix = false;
        }, 5000);
      }

      this.form.suffix = filteredValue;
    },
  },

  beforeDestroy() {
    // 组件销毁时清理
    this.stopGeneration();
    if (this.updateTotalTimer) {
      clearTimeout(this.updateTotalTimer);
    }
  },
};
</script>

<style scoped>
.app-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: #fafafa;
}

.title-section {
  text-align: center;
  /* padding: 20px; */
  /* background: #fff; */
  box-sizing: border-box;
  /* box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1); */
  flex: 0 0 20%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.title-card {
  max-width: 1200px;
  width: 100%;
  margin: 0 auto;
  border: none;
  background: #f8f9fa;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.title-header {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  /* background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); */
  /* color: white; */
  /* border-radius: 4px; */
  /* padding: 16px; */
  /* margin: -20px -20px 20px -20px; */
}

.title-header i {
  margin-right: 8px;
  font-size: 22px;
}

.info-content {
  display: flex;
  flex-direction: row;
  gap: 16px;
  flex: 1;
  justify-content: center;
  align-items: center;
}

.info-alert {
  margin-bottom: 0;
  border: none;
  border-radius: 6px;
  flex: 1;
  max-width: 350px;
}

.info-alert .el-alert__title {
  font-weight: 600;
  font-size: 14px;
}

.info-alert .el-alert__description {
  font-size: 13px;
  line-height: 1.5;
  margin-top: 4px;
}

.recommendation {
  color: #ffd93d !important;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.encryption-info {
  color: #6bcf7f !important;
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.subtitle strong {
  font-weight: 600;
}

.main1-container {
  display: flex;
  flex: 0 0 80%;
  padding: 32px;
  justify-content: center;
  align-items: center;
}

.form-section {
  background: #fff;
  flex: 1 0 0%;
  margin-right: 24px;
  padding: 32px 24px;
  border-radius: 8px;
  box-sizing: border-box;
  min-width: 350px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
.info-section {
  background: #fff;
  flex: 1 0 0%;
  padding: 32px 24px;
  border-radius: 8px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
  min-height: 0;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}
.row {
  display: flex;
  width: 100%;
  gap: 24px;
}
.example {
  color: #888;
  font-size: 14px;
  margin-bottom: 16px;
}
.example b {
  color: #222;
  font-weight: bold;
}
.thread-tip {
  margin-left: 8px;
  color: #888;
  font-size: 13px;
}
.tip {
  margin-bottom: 24px;
}
.btn-row {
  justify-content: center;
  display: flex;
  gap: 16px;
  margin-top: 24px;
}

.btn-row .el-button {
  border-radius: 6px;
  font-weight: 500;
}
.info-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 24px;
  color: #222;
}
.info-list {
  margin-bottom: 24px;
}
.info-item {
  display: flex;
  justify-content: space-between;
  font-size: 15px;
  color: #222;
  margin-bottom: 12px;
}
.progress-bar {
  margin-bottom: 8px;
}
.progress-text {
  text-align: right;
  font-size: 14px;
  color: #888888;
  margin-bottom: 16px;
}
.found-wallet {
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 6px;
  padding: 16px;
  margin-top: 16px;
}
.wallet-title {
  font-size: 16px;
  font-weight: bold;
  color: #0369a1;
  margin-bottom: 8px;
}
.wallet-address {
  font-family: monospace;
  font-size: 14px;
  color: #0c4a6e;
  margin-bottom: 8px;
  word-break: break-all;
}
.wallet-private {
  font-family: monospace;
  font-size: 14px;
  color: #64748b;
  word-break: break-all;
}

.copy-icon {
  margin-left: 8px;
  color: #409eff;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.3s ease;
}

.copy-icon:hover {
  color: #66b1ff;
  transform: scale(1.1);
}

.wallet-address,
.wallet-private {
  display: flex;
  align-items: center;
  justify-content: space-between;
  word-break: break-all;
}

.wallet-address .copy-icon,
.wallet-private .copy-icon {
  flex-shrink: 0;
  margin-left: 12px;
}

/* 输入错误状态样式 */
.input-error {
  border-color: #f56c6c !important;
  box-shadow: 0 0 0 2px rgba(245, 108, 108, 0.2) !important;
}

.input-error .el-input__inner {
  border-color: #f56c6c !important;
}

.error-message {
  color: #f56c6c;
  font-size: 12px;
  margin-top: 4px;
  line-height: 1.2;
}
</style>
