<template>
    <div class="upload-container">
      <!-- 文件选择控件 -->
      <input
        type="file"
        ref="fileInput"
        @change="handleFileSelect"
        accept="image/*"
        class="file-input"
      />
      
      <!-- 加载状态 -->
      <div v-if="loading" class="upload-status">
        <progress :value="progress" max="100"></progress>
        <span>{{ progress }}%</span>
      </div>
  
      <!-- 错误提示 -->
      <div v-if="error" class="error-message">
        {{ error }}
      </div>
  
      <!-- 上传成功提示 -->
      <div v-if="uploadResult" class="success-message">
        上传成功！服务器返回：{{ uploadResult }}
      </div>
    </div>
  </template>
  
  <script>
  import axios from 'axios';
  
  export default {
    data() {
      return {
        loading: false,
        progress: 0,
        error: null,
        uploadResult: null
      };
    },
    methods: {
      // 处理文件选择
      handleFileSelect(event) {
        const file = event.target.files[0];
        if (file) {
          this.validateAndUpload(file);
        }
      },
  
      // 文件验证与上传
      async validateAndUpload(file) {
        // 重置状态
        this.loading = true;
        this.error = null;
        this.uploadResult = null;
  
        // 文件类型验证
        if (!file.type.startsWith('image/')) {
          this.error = '仅支持图片文件';
          this.loading = false;
          return;
        }
  
        // 准备表单数据
        const formData = new FormData();
        formData.append('file', file);
  
        try {
          const response = await axios.post(
            'http://localhost:3000/api/four/upload',
            formData,
            {
              headers: {
                'meme-web-access': 'MTc0NDQ0Mzg5Mjg4Nl8weGJlNDFkODFmMjZiZTE0ZmU2M2FmZjJjMTg5NzM1YTVhNDE1Y2FkYTRfc0JHQXlm.ynrn_SOfo7fDmpoeSWCeusFSLF6IyuNX4PwQzFw_Gwg'
              },
              onUploadProgress: progressEvent => {
                this.progress = Math.round(
                  (progressEvent.loaded * 100) / progressEvent.total
                );
              }
            }
          );
  
          this.uploadResult = response.data;
        } catch (error) {
          this.error = error.response?.data?.message || '上传失败，请重试';
        } finally {
          this.loading = false;
          this.progress = 0;
        }
      }
    }
  };
  </script>
  
  <style scoped>
  .upload-container {
    max-width: 500px;
    margin: 20px auto;
    padding: 20px;
    border: 1px solid #eee;
  }
  
  .file-input {
    display: block;
    margin-bottom: 15px;
  }
  
  .upload-status {
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 15px 0;
  }
  
  .error-message {
    color: #ff4444;
    padding: 10px;
    background: #ffecec;
    border-radius: 4px;
  }
  
  .success-message {
    color: #00c851;
    padding: 10px;
    background: #e2f9e5;
    border-radius: 4px;
  }
  
  progress {
    flex: 1;
    height: 20px;
  }
  </style>