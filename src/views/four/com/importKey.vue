<template>
  <div>
    <el-button type="primary" @click="dialogFormVisible = true"
      >点击导入</el-button
    >

    <el-dialog
      title="输入私钥(一行一个，最多15个钱包)"
      :visible.sync="dialogFormVisible"
      width="60%"
    >
      <el-form :model="form">
        <el-form-item>
          <el-input
            :autosize="{ minRows: 15, maxRows: 15 }"
            type="textarea"
            v-model="form.text"
            autocomplete="off"
          ></el-input>
        </el-form-item>
        <el-form-item>
          <el-row :gutter="24" style="margin-top: 10px;">
            <el-col :span="12">
              <el-button
                @click="dialogFormVisible = false"
                style="margin-left: 70%"
                >取消</el-button
              ></el-col
            >
            <el-col :span="12">
              <el-button type="primary" @click="importWallet"
                >导入</el-button
              ></el-col
            >
          </el-row>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>
<script>
import { ethers } from "ethers";
import store from "@/store";
export default {
  data() {
    return {
      dialogTableVisible: false,
      dialogFormVisible: false,
      form: {
        text: "0x9933d5792e3a13a26776ed574b1e399ea12ecd584cff6ceae571a6b8e0e291ff\n0x1550786a70b0f5ef4dcbdd43bc1840b919e817d2e333bc598d02dcbeb7c156b1",
      },
      pAddress: "",
      nPrice: "",
      sym: "",
      addressData: [],
    };
  },

  methods: {
    async importWallet() {
      const keysText = this.form.text.split("\n");
      const keys = [...new Set(keysText)];
      const addressData = [];
      try {
        let count = 0;
        for (const key of keys) {
          if (count >= 15) break;
          if (
            key.length ==
              "0x1550786a70b0f5ef4dcbdd43bc1840b919e817d2e333bc598d02dcbeb7c156b1"
                .length ||
            key.length ==
              "1550786a70b0f5ef4dcbdd43bc1840b919e817d2e333bc598d02dcbeb7c156b1"
                .length
          ) {
            const pubKey = new ethers.Wallet(key).publicKey;
            const address = ethers.utils.computeAddress(pubKey);
            const provider = store.state.user.provider;
            const balance = await provider.getBalance(address);
            // console.log(address);
            addressData.push({
              address: address,
              showAddress:
                address.substring(2, 4) + "..." + address.substring(37),
              prvKey: key,
              buyAmount: "0",
              balance: ethers.utils.formatEther(balance),
            });
            count++;
          }
        }
      } catch (error) {
        console.log(error);
        this.$notify.error({
          title: this.$t("market.错误"),
          message: this.$t("market.钱包格式错误"),
        });
        // console.log(error)
      }

      this.addressData = addressData;
      this.$emit("imported", addressData);
      this.dialogFormVisible = false;
    },
  },
};
</script>
