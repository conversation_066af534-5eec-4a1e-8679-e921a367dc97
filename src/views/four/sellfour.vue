<template>
  <div class="app-container">
    <div class="inner">
      <el-alert
        v-show="support"
        :title="$t('coinRelease.common.暂不支持此链')"
        type="error"
        :description="$t('coinRelease.common.如有需要请联系管理员定制')"
        center
        :closable="false"
        show-icon
      >
      </el-alert>
      <el-header class="header">
        <p>
          {{ $t("coinRelease.index.标准代币") }}
          <a
            style="
              margin-left: 10px;
              font-size: 12px;
              font-weight: 400;
              color: #409eff;
            "
            :href="helpURL"
            target="_blank"
            >{{ $t("coinRelease.common.教程") }}</a
          >
        </p>
        <p style="font-size: 14px; font-weight: 300">
          {{ $t("coinRelease.index.干净合约方便上手无税无功能Ave检测全绿") }}
        </p>
      </el-header>
      <el-main class="main">
        <el-form :model="form" label-width="auto" label-position="left">
          <el-form-item :label="$t('coinRelease.common.代币全称')">
            <el-input
              v-model="form._name"
              onkeyup="this.value=this.value.replace(/[\u4E00-\u9FA5]/g,'')"
              placeholder="Name"
            />
          </el-form-item>
          <el-form-item :label="$t('coinRelease.common.代币简称')">
            <el-input
              v-model="form._symbol"
              onkeyup="this.value=this.value.replace(/[\u4E00-\u9FA5]/g,'')"
              placeholder="symbol"
            />
          </el-form-item>
          <el-form-item :label="$t('coinRelease.common.发行量')">
            <el-input
              v-model="form._supply"
              oninput="value=value.replace(/[^0-9.]/g,'')"
              placeholder="202304"
            />
          </el-form-item>
          <el-form-item :label="$t('coinRelease.common.精度')">
            <el-input-number
              v-model="form._decimals"
              :min="1"
              :max="18"
              size="small"
            ></el-input-number>
          </el-form-item>
        </el-form>

        <el-button type="primary" @click="getReadytoCreate">{{
          $t("coinRelease.common.创建合约")
        }}</el-button>
        <span style="font-size: 12px; margin-left: 10px"
          >{{ $t("coinRelease.common.费用") }}:{{ getCostAndSymbol }}</span
        >
        <el-dialog
          :title="$t('coinRelease.common.创建合约')"
          :visible.sync="dialogVisible"
          show-close
        >
          <el-collapse v-model="activeNames">
            <el-collapse-item
              :title="$t('coinRelease.common.合约地址')"
              name="1"
            >
              <div style="font-size: 14px">
                {{ $t("coinRelease.common.预计生成地址") }}:
              </div>
              <div style="font-size: 16px; margin-top: 10px">
                {{ coinAddress }}
              </div>

              <div>
                {{ $t("coinRelease.common.预估手续费") }}:<span
                  style="color: red"
                  >{{ gasFee }}</span
                >,{{ $t("coinRelease.common.请确保钱包余额充足余额不足将")
                }}<span style="color: red">{{
                  $t("coinRelease.common.创建失败")
                }}</span
                >！
              </div>
            </el-collapse-item>
            <el-collapse-item
              :title="$t('coinRelease.common.开源参数')"
              name="2"
            >
              <div class="flex-container">
                <el-tag type="success">Optimization: YES</el-tag>
                <el-tag style="margin-left: 5px"> Runs: 200</el-tag>
              </div>
              <div class="flex-container">
                <el-tag type="success">Solidity Version: 0.8.18</el-tag>
                <el-tag style="margin-left: 5px"> License: MIT</el-tag>
              </div>
              <div class="flex-container">
                <el-link
                  icon="el-icon-discover"
                  :href="this.scanURL + coinAddress"
                  target="_blank"
                  >{{ $t("coinRelease.common.浏览器查看") }}</el-link
                >
                <el-link
                  style="margin-left: 10px"
                  icon="el-icon-video-play"
                  :href="tutorialLink"
                  target="_blank"
                  >{{ $t("coinRelease.common.开源教程") }}</el-link
                >
              </div>
              <div class="flex-container">
                <el-button
                  type="info"
                  size="small"
                  plain
                  @click="copy_str(standard)"
                  >{{ $t("coinRelease.common.复制源代码") }}</el-button
                >
                <el-button
                  type="info"
                  size="small"
                  plain
                  @click="copy_str(constructorArgs)"
                  >{{ $t("coinRelease.common.复制构造参数") }}</el-button
                >
              </div>
              <p>
                {{
                  $t(
                    "coinRelease.common.构造参数无法找回若不立即开源请复制后保存到本地文档"
                  )
                }}
              </p>
            </el-collapse-item>
          </el-collapse>
          <span slot="footer" class="dialog-footer">
            <el-button v-show="console" @click="enterConsole">{{
              $t("coinRelease.common.进入控制台")
            }}</el-button>
            <el-button v-if="loading" type="primary" :loading="true">{{
              $t("coinRelease.common.创建合约")
            }}</el-button>
            <el-button v-else-if="console" type="primary" disabled>{{
              $t("coinRelease.common.创建合约")
            }}</el-button>
            <el-button v-else type="primary" @click="onSubmit">{{
              $t("coinRelease.common.创建合约")
            }}</el-button>
          </span>
        </el-dialog>
      </el-main>
    </div>
  </div>
</template>

<script>
import standardData from "@/contracts/standardCoin.json";
import chainParams from "@/contracts/coinReleaseParams.json";
import { standard } from "@/contracts/sourceCode.js";
import Mode1 from "@/contracts/pandaMode1.json";
import store from "@/store";
const { ethers, BigNumber } = require("ethers");
const supportChain = [
  1, 56, 1116, 66, 42161, 97, 4002, 1115, 421613, 280, 5, 8453, 84532, 81457,
  137,
];
export default {
  name: "Dashboard",
  data() {
    return {
      standardData,
      standard,
      Mode1,
      store,
      supportChain,
      support: null,
      activeNames: ["1", "2"],
      ModeAddress: null,
      scanURL: null,
      dialogVisible: false,
      loading: false,
      console: false,
      Fee: null,
      value: null,
      chainSymbol: null,
      gasFee: null,
      salt: null,
      coinAddress: null,
      tutorialLink:
        this.$i18n.locale == "en-US"
          ? "https://help.pandatool.org/english/createtoken/verify-and-publish"
          : "https://help.pandatool.org/createtoken/verify-and-publish",
      helpURL:
        this.$i18n.locale == "en-US"
          ? "https://help.pandatool.org/english/createtoken/stardand"
          : "https://help.pandatool.org/createtoken/stardand",
      constructorArgs: null,
      form: {
        _name: null,
        _symbol: null,
        _supply: null,
        _decimals: 18,
        bigSupply: null,
      },
    };
  },
  created() {
    let Inval = setInterval(() => {
      if (store.state.user.chainId) {
        window.clearInterval(Inval);
        const chainId = store.state.user.chainId;

        if (supportChain.includes(chainId)) {
          this.support = false;
        } else {
          this.support = true;
        }
        this.scanURL = chainParams[chainId][0];
        const selectChainParams = chainParams[chainId][1];
        this.ModeAddress = ethers.utils.getAddress(
          selectChainParams.ModeAddress
        );
        this.Fee = selectChainParams.Fee;
        this.chainSymbol = selectChainParams.chainSymbol;
      }
    }, 1000);
  },
  computed: {
    getCostAndSymbol() {
      return this.Fee + " " + this.chainSymbol;
    },
  },

  methods: {
    checkParams() {
      var supplyArray = this.form._supply.toString().split(".");

      if (supplyArray.length == 1) {
        this.form.bigSupply = BigNumber.from(supplyArray[0]).mul(
          BigNumber.from(10).pow(this.form._decimals)
        );
      } else if (supplyArray.length == 2) {
        var intPart = BigNumber.from(supplyArray[0]).mul(
          BigNumber.from(10).pow(this.form._decimals)
        );
        var floatPart = BigNumber.from(supplyArray[1]).mul(
          BigNumber.from(10).pow(this.form._decimals - supplyArray[1].length)
        );
        this.form.bigSupply = intPart.add(floatPart);
      } else {
        this.$message({
          type: "danger",
          message: this.$t("coinRelease.index.发行量格式不正确"),
        });
      }
    },
    onSubmit() {
      this.loading = true;
      const Mode1Abi = this.Mode1;
      const provider = store.state.user.provider;
      const signer = provider.getSigner();
      const Mode1 = new ethers.Contract(this.ModeAddress, Mode1Abi, signer);
      provider.getNetwork().then((network) => {
        console.log(network.chainId);
        if (network.chainId == store.state.user.chainId) {
          this.DeployContract(Mode1, this.value)
            .then(() => {})
            .catch((error) => {
              console.log("error", error);
              this.loading = false;
              this.$message({
                type: "error",
                message: this.$t("coinRelease.common.创建失败请重试"),
              });
            });
        } else {
          this.$message({
            type: "error",
            message: this.$t(
              "coinRelease.common.公链错误请确保钱包与选择的公链一致"
            ),
          });
        }
      });
    },
    async DeployContract(Mode1, _value) {
      const stringParam = [this.form._name, this.form._symbol];
      const addressParam = [];
      const numberParam = [this.form._decimals, this.form.bigSupply];
      const boolParam = [];
      // All overrides are optional
      let overrides = {
        // The maximum units of gas for the transaction to use
        // gasLimit: 2300000,

        // The price (in wei) per unit of gas
        // gasPrice: utils.parseUnits('9.0', 'gwei'),

        // The nonce to use in the transaction
        // nonce: 123,

        // The amount to send with the transaction (i.e. msg.value)
        value: _value,

        // The chain ID (or network ID) to use
        // chainId: 1
      };
      // let overrides = {value:_value}
      // 常见合约工厂实例

      const contract = await Mode1.CreateContract(
        this.salt,
        stringParam,
        addressParam,
        numberParam,
        boolParam,
        overrides
      );
      console.log("result", contract.hash);
      console.log("contract", contract);

      await contract.wait();
      this.loading = false;
      this.console = true;
      this.$message({
        type: "success",
        message: this.$t("coinRelease.common.创建成功"),
      });
    },

    async getReadytoCreate() {
      this.checkParams();
      var FeeArray = this.Fee.toString().split(".");
      var intPart = BigNumber.from(FeeArray[0]).mul(BigNumber.from(10).pow(18));
      var floatPart = BigNumber.from(FeeArray[1]).mul(
        BigNumber.from(10).pow(18 - FeeArray[1].length)
      );
      this.value = intPart.add(floatPart);
      const encodedbytes = this.standardData.Coin1encodebytes;
      this.salt = ethers.utils.id("pandatoken");
      console.log("salt", this.salt);
      const stringParam = [this.form._name, this.form._symbol];
      const addressParam = [];
      const numberParam = [this.form._decimals, this.form.bigSupply];
      const boolParam = [];

      var abiCoder = new ethers.utils.AbiCoder();
      this.constructorArgs = abiCoder
        .encode(
          ["string[]", "address[]", "uint256[]", "bool[]"],
          [stringParam, addressParam, numberParam, boolParam]
        )
        .slice(2);
      // console.log("encodeABI", this.constructorArgs);

      const initCode = encodedbytes + this.constructorArgs;
      const initCodeHash = ethers.utils.keccak256(initCode);
      // console.log("initCodeHash", initCodeHash);
      this.coinAddress = ethers.utils.getCreate2Address(
        this.ModeAddress,
        this.salt,
        initCodeHash
      );

      const Mode1Abi = this.Mode1;
      const provider = store.state.user.provider;

      const Mode1 = new ethers.Contract(this.ModeAddress, Mode1Abi, provider);
      let overrides = { value: this.value };
      console.log("value", overrides);
      const GasPrice = await provider.getGasPrice();
      const estimateGas = await Mode1.estimateGas.CreateContract(
        this.salt,
        stringParam,
        addressParam,
        numberParam,
        boolParam
      );
      console.log("GasPrice", GasPrice);
      console.log("estimateGas", estimateGas);
      this.gasFee = parseFloat(
        new Number(GasPrice.mul(estimateGas)) / 10 ** 18 + Number(this.Fee)
      ).toFixed(6);
      console.log("gasfee", this.gasFee);

      console.log(this.coinAddress);
      this.dialogVisible = true;
    },
    enterConsole() {
      this.$router.push({
        path: "/coinrelease/detail/",
        query: {
          coinAddress: this.coinAddress,
          modeType: this.$t("coinRelease.modeType.标准模版"),
        },
      });
    },

    copy_str(text) {
      if (navigator.clipboard) {
        // clipboard api 复制
        navigator.clipboard.writeText(text);
      } else {
        var textarea = document.createElement("textarea");
        document.body.appendChild(textarea);
        // 隐藏此输入框
        textarea.style.position = "fixed";
        textarea.style.clip = "rect(0 0 0 0)";
        textarea.style.top = "10px";
        // 赋值
        textarea.value = text;
        // 选中
        textarea.select();
        // 复制
        document.execCommand("copy", true);
        // 移除输入框
        document.body.removeChild(textarea);
      }
      this.$message({
        type: "success",
        message: this.$t("coinRelease.common.已复制"),
      });
    },
  },
};
</script>

<style scoped>
/* @media screen and (max-width: 500px) {
  .inner{
  background-color:#ffff;
  padding: 10px;
  }

  .header{
  text-align: center;
  margin-top: 20px;
  font-size: larger;

  }
  .main{

    padding-left: 0%;
    padding-right: 0%;
    padding-bottom: 60px;


  }

}
@media screen and (min-width: 500px) {

.inner{
  background-color:#ffff;
  padding: 40px;
}
.header{
  text-align: center;
  margin-top: 20px;
  font-size: larger;

}
.main{

  padding-left: 30%;
  padding-right: 30%;
  padding-bottom: 100px;


}
.flex-container {
  display: flex;
  flex-direction: row;
  margin-top: 20px;
}

} */
</style>
