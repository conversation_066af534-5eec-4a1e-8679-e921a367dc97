<template>
  <div class="app-container">
    <div class="inner">
      <el-alert
        v-show="support"
        :title="$t('coinRelease.common.暂不支持此链')"
        type="error"
        :description="$t('coinRelease.common.如有需要请联系管理员定制')"
        center
        :closable="false"
        show-icon
      >
      </el-alert>
      <el-header class="header">
        <p>
          {{ $t("coinRelease.index.标准代币") }}
          <a
            style="
              margin-left: 10px;
              font-size: 12px;
              font-weight: 400;
              color: #409eff;
            "
            :href="helpURL"
            target="_blank"
            >{{ $t("coinRelease.common.教程") }}</a
          >
        </p>
        <p style="font-size: 14px; font-weight: 300">
          {{ $t("coinRelease.index.干净合约方便上手无税无功能Ave检测全绿") }}
        </p>
      </el-header>
      <el-main class="main">
        <el-form :model="form" label-width="auto" label-position="left">
          <el-form-item :label="$t('coinRelease.common.代币全称')">
            <el-input
              v-model="form._name"
              onkeyup="this.value=this.value.replace(/[\u4E00-\u9FA5]/g,'')"
              placeholder="Name"
            />
          </el-form-item>
          <el-form-item :label="$t('coinRelease.common.代币简称')">
            <el-input
              v-model="form._symbol"
              onkeyup="this.value=this.value.replace(/[\u4E00-\u9FA5]/g,'')"
              placeholder="symbol"
            />
          </el-form-item>
          <el-form-item label="Logo">
            <el-upload
              class="avatar-uploader"
              :show-file-list="false"
              action="http://************:3000/api/four/upload"
              :headers="{ 'meme-web-access': Authorization }"
              :on-change="handleAvatarChange"
              :on-success="handleAvatarSuccess"
              :before-upload="beforeAvatarUpload"
            >
              <img v-if="form.imageUrl" :src="form.imageUrl" class="avatar" />
              <div v-else>
                <i class="el-icon-plus avatar-uploader-icon"> </i>
                <p
                  style="
                    font-size: 12px;
                    font-weight: 300;
                    margin-top: -10px;
                    margin-bottom: -15px;
                  "
                >
                  点击上传,支持png,jpg,小于5M
                  <!-- {{
                    $t(
                      "coinRelease.common.添加池子后的首次交易需要在控制台手动开启如关闭则添加流动性后立即可以进行交易交易开启后无法关闭"
                    )
                  }} -->
                </p>
              </div>
              <!-- <i v-else class="el-icon-plus avatar-uploader-icon"> </i> -->
            </el-upload>
          </el-form-item>
          <el-form-item label="简介">
            <el-input
              v-model="form.description"
              type="textarea"
              placeholder="请输入内容"
              maxlength="1000"
              show-word-limit
              :rows="3"
              style="font-size: 14px"
            >
            </el-input>
          </el-form-item>
          <el-form-item label="交易代币">
            <el-radio v-model="form.raiseToken" label="1">
              <el-tag>BNB</el-tag>
            </el-radio>
          </el-form-item>
          <el-form-item label="标签">
            <el-radio-group style="" v-model="form.tag" size="small">
              <el-radio-button label="MEME">MEME</el-radio-button>
              <el-radio-button label="AI">AI</el-radio-button>
              <el-radio-button label="CRYPTO">CRYPTO</el-radio-button>
              <el-radio-button label="PANDA">PANDA</el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="可选参数">
            <el-switch v-model="form.option"> </el-switch>
          </el-form-item>
          <el-form-item v-show="form.option" label="Website">
            <el-input
              v-model="form.website"
              placeholder="https://example.com"
            />
          </el-form-item>
          <el-form-item v-show="form.option" label="Twitter/X">
            <el-input
              v-model="form.twitter"
              placeholder="https://twitter.com/username"
            />
          </el-form-item>
          <el-form-item v-show="form.option" label="Telegram">
            <el-input
              v-model="form.telegram"
              placeholder="https://t.me/username"
            />
          </el-form-item>

          <el-form-item v-show="form.option" label="开盘时间">
            <!-- <el-input
              v-model="form.lunchTime"
              onkeyup="this.value=this.value.replace(/[\u4E00-\u9FA5]/g,'')"
              placeholder="symbol"
            /> -->
            <el-date-picker
              v-model="form.lunchTime"
              type="datetime"
              placeholder="选择日期时间"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="购买数量">
            <el-input
              v-model="form.buyAmount"
              @keyup.native="form.buyAmount = checkDecimal(form.buyAmount)"
              placeholder="0.01"
            >
              <template slot="append">BNB</template>
            </el-input>
          </el-form-item>
        </el-form>

        <el-button type="primary" @click="getReadytoCreate">创建合约</el-button>
        <span style="font-size: 12px; margin-left: 10px"
          >费用:{{ getCostAndSymbol }}</span
        >
        <!-- <el-button type="primary" @click="test"> 测试 </el-button>
        <el-button type="primary" @click="test2"> 测试2 </el-button> -->
        <!-- <imageUploader /> -->
      </el-main>
      <el-button type="primary" @click="test2"> 测试 </el-button>
    </div>
  </div>
</template>

<script>
import standardData from "@/contracts/standardCoin.json";
import chainParams from "@/contracts/coinReleaseParams.json";
import { standard } from "@/contracts/sourceCode.js";
import Mode1 from "@/contracts/pandaMode1.json";
import store from "@/store";
import { reqNonce, reqLogin, reqGetUserInfo } from "@/utils/fourReq.js";
import axios from "axios";
const { ethers, BigNumber } = require("ethers");
const supportChain = [56];
//https://ipfs.io/ipfs/QmQrLuZ1mxLCNnyrw2WE3GZC6kQje5qcMZPkGx43jjCqu2
export default {
  name: "crateFour",
  data() {
    return {
      standardData,
      standard,
      Mode1,
      store,
      supportChain,
      support: null,
      activeNames: ["1", "2"],
      ModeAddress: null,
      scanURL: null,
      dialogVisible: false,
      loading: false,
      console: false,
      Fee: null,
      value: null,
      chainSymbol: null,
      gasFee: null,
      salt: null,
      Authorization: null,
      coinAddress: null,
      tutorialLink:
        this.$i18n.locale == "en-US"
          ? "https://help.pandatool.org/english/createtoken/verify-and-publish"
          : "https://help.pandatool.org/createtoken/verify-and-publish",
      helpURL:
        this.$i18n.locale == "en-US"
          ? "https://help.pandatool.org/english/createtoken/stardand"
          : "https://help.pandatool.org/createtoken/stardand",
      constructorArgs: null,
      form: {
        _name: null,
        _symbol: null,
        imageUrl: "",
        description: null,
        tag: "MEME",
        raiseToken: "1",
        option: false,
        website: "",
        twitter: "",
        telegram: "",
        lunchTime: new Date(),
        buyAmount: "0",
      },
    };
  },
  created() {
    let Inval = setInterval(() => {
      if (store.state.user.chainId) {
        window.clearInterval(Inval);
        const chainId = store.state.user.chainId;

        if (supportChain.includes(chainId)) {
          this.support = false;
        } else {
          this.support = true;
        }
        this.scanURL = chainParams[chainId][0];
        const selectChainParams = chainParams[chainId][1];
        this.ModeAddress = ethers.utils.getAddress(
          selectChainParams.ModeAddress
        );
        this.Fee = selectChainParams.Fee;
        this.chainSymbol = selectChainParams.chainSymbol;
      }
    }, 1000);
  },
  computed: {
    getCostAndSymbol() {
      return this.Fee + " " + this.chainSymbol;
    },
  },

  methods: {
    checkDecimal(num) {
      var str = num;
      var len1 = str.substr(0, 1);
      var len2 = str.substr(1, 1);
      //如果第一位是0，第二位不是点，就用数字把点替换掉
      if (str.length > 1 && len1 == 0 && len2 != ".") {
        str = str.substr(1, 1);
      }
      //第一位不能是.
      if (len1 == ".") {
        str = "";
      }
      //限制只能输入一个小数点
      if (str.indexOf(".") != -1) {
        var str_ = str.substr(str.indexOf(".") + 1);
        if (str_.indexOf(".") != -1) {
          str = str.substr(0, str.indexOf(".") + str_.indexOf(".") + 1);
        }
      }
      //正则替换
      str = str.replace(/[^\d^\.]+/g, ""); // 保留数字和小数点
      str = str.replace(/(\.\d{5})\d+$/, "$1"); // 小数点后最多保留5位
      return str;
    },
    async test() {
      const res = await reqNonce(
        ethers.utils.getAddress(store.state.user.address)
      );
      console.log(res);
      console.log(store.state.user);
      console.log(ethers.utils.getAddress(store.state.user.address));
      const provider = store.state.user.provider;
      const signer = provider.getSigner();
      //You are sign in Meme 936855
      const messageSignature = await signer.signMessage(
        `You are sign in Meme ${res.data}`
      );
      const loginRes = await reqLogin(
        ethers.utils.getAddress(store.state.user.address),
        messageSignature
      );
      this.Authorization = loginRes.data;
      console.log(loginRes);
    },
    async test2() {
      console.log(this.form.lunchTime.getTime());
      console.log(this.Authorization);
    },

    handleAvatarChange() {
      console.log("change");
    },
    handleAvatarSuccess(res, file) {
      console.log("after success", res);
      if (res.code != 0) {
        this.$message.error("上传失败");
        return;
      }
      this.form.imageUrl = res.data;
    },
    async beforeAvatarUpload(file) {
      const isJPG = file.type === "image/jpeg" || file.type === "image/png";
      const isLt2M = file.size / 1024 / 1024 < 2;

      if (!isJPG) {
        this.$message.error("仅支持jpg/jpeg/png格式图片!");
      }
      if (!isLt2M) {
        this.$message.error("图片大小不能超过2M!");
      }
      if (this.Authorization == null) {
        if (store.state.user.chainId == null) {
          this.$message.error("请连钱包");
          return false;
        }
        const res = await reqNonce(
          ethers.utils.getAddress(store.state.user.address)
        );
        const provider = store.state.user.provider;
        const signer = provider.getSigner();
        //You are sign in Meme 936855
        const messageSignature = await signer.signMessage(
          `You are sign in Meme ${res.data}`
        );
        const loginRes = await reqLogin(
          ethers.utils.getAddress(store.state.user.address),
          messageSignature
        );
        this.Authorization = loginRes.data;

        console.log("loginRes", loginRes);
      }
      return isJPG && isLt2M;
    },
    checkParams() {
      if (
        this.form._name == null ||
        this.form._name == null ||
        this.form.description == null
      ) {
        this.$message.error("代币全称或简称或描述不能为空");
        return false;
      }
      if (this.form.imageUrl == null || this.form.imageUrl == "") {
        this.$message.error("代币Logo不能为空");
        return false;
      }
      return true;
    },
    onSubmit() {},

    async getReadytoCreate() {
      if (!this.checkParams()) {
        return;
      }
      const provider = store.state.user.provider;
      const signer = provider.getSigner(store.state.user.address);
      // 请求 http://************:3000/api/four/create
      let optionData = {
        ...(this.form.website != "" && { webUrl: this.form.website }),
        ...(this.form.twitter != "" && { twitterUrl: this.form.twitter }),
        ...(this.form.telegram != "" && { telegramUrl: this.form.telegram }),
      };
      const config = {
        method: "post", // 请求方法为 POST
        url: "http://************:3000/api/four/create",
        headers: {
          "content-type": "application/json",
          "meme-web-access": this.Authorization,
        },
        data: {
          name: this.form._name,
          shortName: this.form._symbol,
          desc: this.form.description,
          imgUrl: this.form.imageUrl,
          tag: this.form.tag,
          raiseToken: this.form.raiseToken,
          launchTime: this.form.lunchTime.getTime(),
          preSale: this.form.buyAmount,
          ...optionData,
        },
      };
      const res = await axios(config);
      const contractABI = [
        {
          inputs: [
            { internalType: "bytes", name: "args", type: "bytes" },
            { internalType: "bytes", name: "signature", type: "bytes" },
          ],
          name: "createToken",
          outputs: [],
          stateMutability: "payable",
          type: "function",
        },
      ];

      const contractAddress = "******************************************";
      const contract = new ethers.Contract(
        contractAddress,
        contractABI,
        signer
      );
      const txOptions = {
        value: ethers.utils.parseEther(this.form.buyAmount),
      };
      console.log(res.createArg, res.signature, res);
      if (res.data.code != 0) {
        this.$message.error(res.data.msg);
        return;
      }
      const tx = await contract.createToken(
        res.data.data.createArg,
        res.data.data.signature,
        txOptions
      );

      console.log("Transaction hash:", tx.hash);
      await provider.waitForTransaction(tx.hash, 1);
      // 等待交易确认
      this.$message({
        type: "success",
        message: "创建成功" + tx.hash,
      });
    },
  },
};
</script>

<style scoped>
.avatar-uploader .el-upload {
  border: 2px dashed #741717;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #030f1b;
}
.avatar-uploader-icon {
  font-size: 32px;
  color: #235ba9;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
}
.avatar {
  width: 100px;
  height: 100px;
  display: block;
}
/* @media screen and (max-width: 500px) {
  .inner{
  background-color:#ffff;
  padding: 10px;
  }

  .header{
  text-align: center;
  margin-top: 20px;
  font-size: larger;

  }
  .main{

    padding-left: 0%;
    padding-right: 0%;
    padding-bottom: 60px;


  }

}
@media screen and (min-width: 500px) {

.inner{
  background-color:#ffff;
  padding: 40px;
}
.header{
  text-align: center;
  margin-top: 20px;
  font-size: larger;

}
.main{

  padding-left: 30%;
  padding-right: 30%;
  padding-bottom: 100px;


}
.flex-container {
  display: flex;
  flex-direction: row;
  margin-top: 20px;
}

} */
</style>
