<template>
  <div class="app-container">
    
    <iframe :src="url" width="100%" height="800px" frameborder="0"></iframe>
  </div>
</template>
<script>
// import { ethers, BigNumber } from "ethers";
// import vipAbi from "@/contracts/vip.abi.json";
// import store from "@/store";
// const vipContractAddress = "******************************************";
// const vipChainId = 56;
export default {
  data() {
    return {
      vipShow: false,
      vipType: 1,
      feeDay: "0.1",
      feeWeek: "0.4",
      feeMonth: "1.0",
      forever: "3.0",
      buyDisable: false,
      vipDate: this.$t("gather.未开通VIP"),
      chainErr: false,
      url: ``,
      lang: this.$i18n.locale == "zh-CN" ? "zh" : "en",
    };
  },
  /**
   *
   */
  methods: {
    // async buyVip() {
    //   this.buyDisable = true;
    //   const chainId = store.state.user.chainId;
    //   if (chainId != vipChainId) {
    //     this.notify(this.$t("gather.请切换至币安链") + "", 2);
    //     return;
    //   }
    //   const provider = store.state.user.provider;
    //   const gasPrice = await provider.getGasPrice();
    //   const signer = provider.getSigner(store.state.user.address);
    //   const vipContract = new ethers.Contract(
    //     vipContractAddress,
    //     vipAbi,
    //     signer
    //   );
    //   // console.log(ethers.utils.parseUnits(this.feeDay))
    //   try {
    //     let res = null;
    //     if (this.vipType == 1) {
    //       res = await vipContract.payForValidTime(store.state.user.address, 1, {
    //         value: ethers.utils.parseUnits(this.feeDay),
    //         gasPrice: gasPrice,
    //       });
    //     }
    //     if (this.vipType == 2) {
    //       res = await vipContract.payForValidTime(store.state.user.address, 2, {
    //         value: ethers.utils.parseUnits(this.feeWeek),
    //         gasPrice: gasPrice,
    //       });
    //     }
    //     if (this.vipType == 3) {
    //       res = await vipContract.payForValidTime(store.state.user.address, 3, {
    //         value: ethers.utils.parseUnits(this.feeMonth),
    //         gasPrice: gasPrice,
    //       });
    //     }
    //     if (this.vipType == 4) {
    //       res = await vipContract.payForValidTime(store.state.user.address, 4, {
    //         value: ethers.utils.parseUnits(this.forever),
    //         gasPrice: gasPrice,
    //       });
    //     }
    //     if (res) {
    //       await provider.waitForTransaction(res.hash, 1);
    //       this.buyDisable = false;
    //       this.notify(res.hash, 1);
    //       const usersVaildTime = await vipContract.UsersVaildTime(
    //         store.state.user.address
    //       );
    //       const currentTimestampMillis = new Date().getTime();
    //       const currentTimestampSeconds = Math.floor(
    //         currentTimestampMillis / 1000
    //       );
    //       if (
    //         usersVaildTime.gt(0) &&
    //         usersVaildTime.sub(currentTimestampSeconds) > 0
    //       ) {
    //         this.vipDate =
    //           this.$t("gather.到期时间") +
    //           "：" +
    //           this.formatUnixTimestamp(usersVaildTime.toString());
    //         // console.log(usersVaildTime.toString());
    //         this.vipShow = true;
    //       }
    //       return;
    //     } else {
    //       this.notify(this.$t("gather.错误404") + "", 2);
    //     }
    //   } catch (e) {
    //     this.buyDisable = false;
    //     console.log(e);
    //     this.notify(this.$t("gather.出现错误请检查您的网络或者余额") + "", 2);
    //   }

    //   this.buyDisable = false;
    //   // console.log(res);
    // },
    // formatUnixTimestamp(unixTimestamp) {
    //   // 创建 Date 对象，将 UNIX 时间戳转换为毫秒
    //   const date = new Date(unixTimestamp * 1000);

    //   // 获取年、月、日、时、分、秒
    //   const year = date.getFullYear();
    //   const month = (date.getMonth() + 1).toString().padStart(2, "0");
    //   const day = date.getDate().toString().padStart(2, "0");
    //   const hours = date.getHours().toString().padStart(2, "0");
    //   const minutes = date.getMinutes().toString().padStart(2, "0");
    //   const seconds = date.getSeconds().toString().padStart(2, "0");

    //   // 拼接成格式化的字符串
    //   const formattedDate = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;

    //   return formattedDate;
    // },
  },
  created() {
    let Inval = setInterval(async () => {
      // if (store.state.user.address) {
      //   window.clearInterval(Inval);
      // }
      // const chainId = store.state.user.chainId;
      // console.log(chainId);
      // if (chainId != vipChainId) {
      //   this.chainErr = true;
      //   return;
      // }
      // const address = store.state.user.address;
      this.chainErr = false;
      // const provider = new ethers.providers.Web3Provider(window.ethereum);
      // const signer = provider.getSigner(store.state.user.address);
      // const vipContract = new ethers.Contract(
      //   vipContractAddress,
      //   vipAbi,
      //   signer
      // );
      // const fee1 = await vipContract.validFee1();
      // const fee2 = await vipContract.validFee2();
      // const fee3 = await vipContract.validFee3();
      // const ulimitedFee = await vipContract.ulimitedFee();
      // this.feeDay = ethers.utils.formatEther(fee1);
      // this.feeWeek = ethers.utils.formatEther(fee2);
      // this.feeMonth = ethers.utils.formatEther(fee3);
      // this.forever = ethers.utils.formatEther(ulimitedFee);
      // const usersVaildTime = await vipContract.UsersVaildTime(
      //   store.state.user.address
      // );
      // // console.log(usersVaildTime)
      // const currentTimestampMillis = new Date().getTime();
      // const currentTimestampSeconds = Math.floor(currentTimestampMillis / 1000);
      this.url = `https://bot.pandatool.org/evmgather/${this.lang}?vip=true`;
      // if (
      //   usersVaildTime.gt(0) &&
      //   usersVaildTime.sub(currentTimestampSeconds) > 0
      // ) {
      //   this.vipDate =
      //     this.$t("gather.到期时间") +
      //     "：" +
      //     this.formatUnixTimestamp(usersVaildTime.toString());
      //   this.vipShow = true;
      //   this.url = `https://bot.pandatool.org/evmgather/${this.lang}?vip=true`;
      // }
    }, 500);
  },
};
</script>
