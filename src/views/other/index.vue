<template>
  <div class="app-container">
    <div class="inner">
      <el-header class="header">
        <p>{{ $t("other.p1") }}</p>
      </el-header>
      <el-main class="main">
        <p>
          {{ $t("other.p2") }}
          <el-link
            style="font-size: 22px; color: #30608f"
            icon="el-icon-telegram"
            href="https://t.me/btc6560"
            target="_blank"
            >{{ $t("other.p3") }}</el-link
          >
          {{ $t("other.p4") }}
          <el-link
            style="font-size: 22px; color: #30608f"
            icon="el-icon-telegram"
            href="https://t.me/pandatool"
            target="_blank"
            >{{ $t("other.p5") }}</el-link
          >
          {{ $t("other.p6") }}
        </p>
      </el-main>
    </div>
  </div>
</template>

<script>
export default {
  name: "Dashboard",
  data() {
    return {};
  },
  methods: {},
};
</script>

<style scoped>
@media screen and (max-width: 500px) {
  .main {
    text-align: center;
    padding-left: 0;
    padding-right: 0;
  }
}
@media screen and (min-width: 500px) {
  .header {
    text-align: center;
    margin-top: 20px;
    font-size: 30px;
  }
  .main {
    font-size: 24px;
    text-align: center;
    padding-left: 5%;
    padding-right: 5%;
  }
}
</style>
