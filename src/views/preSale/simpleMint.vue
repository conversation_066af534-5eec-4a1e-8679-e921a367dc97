<template>
  <div class="app-container">
    <div class="inner">
      <el-alert
        v-show="support"
        :title="$t('preSale.暂不支持此链')"
        type="error"
        :description="$t('preSale.如有需要请联系管理员定制')"
        center
        :closable="false"
        show-icon
      >
      </el-alert>
      <el-header class="header">
        <p>
          {{ $t("preSale.simpleMint.标准Mint") }}
          <a
            style="
              margin-left: 10px;
              font-size: 12px;
              font-weight: 400;
              color: #409eff;
            "
            :href="helpURL"
            target="_blank"
          >
            {{ $t("preSale.simpleMint.教程") }}</a
          >
        </p>
        <p style="font-size: 14px; font-weight: 300">
          {{ $t("preSale.simpleMint.转账即预售链上可查去中心化") }}
        </p>
      </el-header>
      <el-main class="main">
        <el-form :model="form" label-width="auto" label-position="left">
          <el-form-item :label="$t('preSale.simpleMint.预售名称')">
            <el-input
              v-model="form.name"
              onkeyup="this.value=this.value.replace(/[\u4E00-\u9FA5]/g,'')"
              placeholder="Name"
            />
          </el-form-item>
          <el-form-item :label="$t('preSale.simpleMint.预售代币地址')">
            <el-input v-model="form.tokenAddr" placeholder="0x"> </el-input>
          </el-form-item>
          <el-form-item :label="$t('preSale.simpleMint.每份价格')">
            <el-input
              v-model="form.price"
              @keyup.native="form.price = checkDecimal(form.price)"
              placeholder="0.001"
            >
              <template slot="append">{{ this.chainSymbol }}</template>
            </el-input>
          </el-form-item>
          <el-form-item :label="$t('preSale.simpleMint.每份数量')">
            <el-input
              v-model="form.amountPerUnits"
              @keyup.native="
                form.amountPerUnits = checkDecimal(form.amountPerUnits)
              "
              placeholder="1000"
            >
            </el-input>
          </el-form-item>
          <el-form-item :label="$t('preSale.simpleMint.总份数')">
            <el-input
              v-model="form.mintLimit"
              type="number"
              oninput="value=value.replace(/[^0-9.]/g,'')"
              placeholder="1"
            >
            </el-input>
          </el-form-item>
          <el-form-item :label="$t('preSale.simpleMint.单次预售最大份数')">
            <el-input
              v-model="form.accEachLimit"
              type="number"
              oninput="value=value.replace(/[^0-9.]/g,'')"
              placeholder="1"
            >
            </el-input>
          </el-form-item>
          <el-form-item :label="$t('preSale.simpleMint.单钱包预售最大份数')">
            <el-input
              v-model="form.accMintLimit"
              type="number"
              oninput="value=value.replace(/[^0-9.]/g,'')"
              placeholder="1"
            >
            </el-input>
          </el-form-item>
        </el-form>

        <el-button type="primary" @click="getReadytoCreate">{{
          $t("preSale.simpleMint.创建合约")
        }}</el-button>
        <span style="font-size: 12px; margin-left: 10px"
          >{{ $t("preSale.simpleMint.费用") }}:{{ getCostAndSymbol }}</span
        >
        <el-dialog
          :title="$t('preSale.simpleMint.创建合约')"
          :visible.sync="dialogVisible"
          show-close
        >
          <el-collapse v-model="activeNames">
            <el-collapse-item
              :title="$t('preSale.simpleMint.合约地址')"
              name="1"
            >
              <div style="font-size: 14px">
                {{ $t("preSale.simpleMint.预计生成地址") }}:
              </div>
              <div style="font-size: 16px; margin-top: 10px">
                {{ coinAddress }}
              </div>

              <div>
                {{ $t("preSale.simpleMint.预估手续费") }}:<span
                  style="color: red"
                  >{{ gasFee }}</span
                >,{{ $t("preSale.simpleMint.请确保钱包余额充足余额不足将")
                }}<span style="color: red">{{
                  $t("preSale.simpleMint.创建失败")
                }}</span
                >！
              </div>
            </el-collapse-item>
            <el-collapse-item
              :title="$t('preSale.simpleMint.开源参数')"
              name="2"
            >
              <div class="flex-container">
                <el-tag type="success">Optimization: YES</el-tag>
                <el-tag style="margin-left: 5px"> Runs: 200</el-tag>
              </div>
              <div class="flex-container">
                <el-tag type="success">Solidity Version: 0.8.18</el-tag>
                <el-tag style="margin-left: 5px"> License: MIT</el-tag>
              </div>
              <div class="flex-container">
                <el-link
                  icon="el-icon-discover"
                  :href="this.scanURL + coinAddress"
                  target="_blank"
                  >{{ $t("preSale.simpleMint.浏览器查看") }}</el-link
                >
                <el-link
                  style="margin-left: 10px"
                  icon="el-icon-video-play"
                  :href="tutorialLink"
                  target="_blank"
                  >{{ $t("preSale.simpleMint.开源教程") }}</el-link
                >
              </div>
              <div class="flex-container">
                <el-button
                  type="info"
                  size="small"
                  plain
                  @click="copy_str(preSaleMint)"
                  >{{ $t("preSale.simpleMint.复制源代码") }}</el-button
                >
                <el-button
                  type="info"
                  size="small"
                  plain
                  @click="copy_str(constructorArgs)"
                  >{{ $t("preSale.simpleMint.复制构造参数") }}</el-button
                >
              </div>
              <p>
                {{
                  $t(
                    "preSale.simpleMint.构造参数无法找回若不立即开源请复制后保存到本地文档"
                  )
                }}
              </p>
            </el-collapse-item>
          </el-collapse>
          <span slot="footer" class="dialog-footer">
            <el-button v-show="console" @click="enterConsole">{{
              $t("preSale.simpleMint.进入控制台")
            }}</el-button>
            <el-button v-if="loading" type="primary" :loading="true">{{
              $t("preSale.simpleMint.创建合约")
            }}</el-button>
            <el-button v-else-if="console" type="primary" disabled>{{
              $t("preSale.simpleMint.创建合约")
            }}</el-button>
            <el-button v-else type="primary" @click="onSubmit">{{
              $t("preSale.simpleMint.创建合约")
            }}</el-button>
          </span>
        </el-dialog>
      </el-main>
    </div>
  </div>
</template>

<script>
import standardData from "@/contracts/standardCoin.json";
import presaleData from "@/contracts/presaleBytesABI.json";
import chainParams from "@/contracts/presaleParams.json";
import { preSaleMint } from "@/contracts/sourceCode.js";
import Mode1 from "@/contracts/pandaMode1.json";
import store from "@/store";
const { ethers, BigNumber } = require("ethers");
const supportChain = [56, 97, 8453, 84532];
export default {
  name: "Dashboard",
  data() {
    return {
      standardData,
      presaleData,
      preSaleMint,
      Mode1,
      store,
      supportChain,
      support: null,
      activeNames: ["1", "2"],
      ModeAddress: null,
      scanURL: null,
      dialogVisible: false,
      loading: false,
      console: false,
      Fee: null,
      value: null,
      chainSymbol: null,
      gasFee: null,
      salt: null,
      coinAddress: null,
      tokenABI: null,
      tutorialLink:
        this.$i18n.locale == "en-US"
          ? "https://help.pandatool.org/english/createtoken/verify-and-publish"
          : "https://help.pandatool.org/createtoken/verify-and-publish",
      helpURL:
        this.$i18n.locale == "en-US"
          ? "https://help.pandatool.org/english/presale/simplemint"
          : "https://help.pandatool.org/presale/simplemint",
      constructorArgs: null,
      swapOptions: [],
      form: {
        name: null,
        tokenAddr: null,
        price: null,
        bigPrice: null,
        amountPerUnits: null,
        bigAmountPerUnits: null,
        mintLimit: null,
        accEachLimit: null,
        accMintLimit: null,
        enableAddLP: false,

        selectSwap: null,
        otherSwap: null,
        addPart: 100,
        _swapRouter: null,
        enableDonate: false,
        donateETHAddr: "******************************************",
        donateTokenAddr: "******************************************",
        donateETHPart: 0,
        donateTokenPart: 0,
      },
    };
  },
  created() {
    let Inval = setInterval(() => {
      if (store.state.user.chainId) {
        window.clearInterval(Inval);
        const chainId = store.state.user.chainId;
        this.tokenABI = standardData.Coin1abi;

        if (supportChain.includes(chainId)) {
          this.support = false;
        } else {
          this.support = true;
        }
        this.scanURL = chainParams[chainId][0];
        const selectChainParams = chainParams[chainId][1];
        this.ModeAddress = ethers.utils.getAddress(
          selectChainParams.ModeAddress
        );
        console.log("this.ModeAddress", this.ModeAddress);
        this.Fee = selectChainParams.Fee1;
        // this.Fee = "0.07";
        this.chainSymbol = selectChainParams.chainSymbol;
        for (let i = 0; i < selectChainParams.swapOptions.length; i++) {
          var temAddress = ethers.utils.getAddress(
            selectChainParams.swapOptions[i].value
          );
          var temParam = {
            label: selectChainParams.swapOptions[i].label,
            value: temAddress,
          };
          this.form._swapRouter = temAddress;
          this.swapOptions.push(temParam);
        }
      }
    }, 1000);
  },
  computed: {
    getCostAndSymbol() {
      return this.Fee + " " + this.chainSymbol;
    },
  },

  methods: {
    getSwap(selectSwap) {
      console.log("selectSwap", selectSwap);
      if (selectSwap == 1) {
        this.form.otherSwap = true;
        this.form._swapRouter = null;
      } else {
        this.form.otherSwap = false;
        this.form._swapRouter = selectSwap;
      }
    },
    async checkParams() {
      let isCurrency = ethers.utils.isAddress(this.form.tokenAddr);
      if (!isCurrency) {
        this.$message({
          type: "error",
          message: this.$t("preSale.simpleMint.预售代币地址不正确") + "!",
        });
        return false;
      }
      const provider = store.state.user.provider;
      const signer = provider.getSigner();
      const tokenContract = new ethers.Contract(
        this.form.tokenAddr,
        this.tokenABI,
        signer
      );

      const bigDecimals = await tokenContract.decimals();
      const decimals = Number(bigDecimals.toString());

      this.form.bigPrice = this.getBigInt(this.form.price, 18);
      if (!this.form.bigPrice) {
        this.$message({
          type: "danger",
          message: this.$t("preSale.simpleMint.价格格式不正确") + "!",
        });
        return false;
      }

      this.form.bigAmountPerUnits = this.getBigInt(
        this.form.amountPerUnits,
        decimals
      );
      if (!this.form.bigAmountPerUnits) {
        this.$message({
          type: "danger",
          message: this.$t("preSale.simpleMint.每份数量格式不正确") + "!",
        });
        return false;
      }

      if (this.form.enableAddLP) {
        let isSwap = ethers.utils.isAddress(this.form._swapRouter);
        if (!isSwap) {
          this.$message({
            type: "error",
            message: this.$t("preSale.simpleMint.交易所路由地址不正确") + "!",
          });
          return false;
        }
      }
      return true;
    },
    onSubmit() {
      this.loading = true;
      const Mode1Abi = this.Mode1;
      const provider = store.state.user.provider;
      const signer = provider.getSigner();
      const Mode1 = new ethers.Contract(this.ModeAddress, Mode1Abi, signer);
      provider.getNetwork().then((network) => {
        console.log(network.chainId);
        if (network.chainId == store.state.user.chainId) {
          this.DeployContract(Mode1, this.value)
            .then(() => {})
            .catch((error) => {
              console.log("error", error);
              this.loading = false;
              this.$message({
                type: "error",
                message: this.$t("preSale.simpleMint.创建失败请重试"),
              });
            });
        } else {
          this.$message({
            type: "error",
            message: this.$t(
              "preSale.simpleMint.公链错误请确保钱包与选择的公链一致"
            ),
          });
        }
      });
    },
    async DeployContract(Mode1, _value) {
      const stringParam = [this.form.name];
      const addressParam = [
        this.form.tokenAddr,
        this.form.donateETHAddr,
        this.form.donateTokenAddr,
        this.form._swapRouter,
      ];
      const numberParam = [
        this.form.bigPrice,
        this.form.bigAmountPerUnits,
        Number(this.form.mintLimit),
        Number(this.form.accEachLimit),
        Number(this.form.accMintLimit),

        Number(this.form.addPart),
        Number(this.form.donateETHPart),
        Number(this.form.donateTokenPart),
      ];
      const boolParam = [this.form.enableAddLP, this.form.enableDonate];
      // All overrides are optional
      let overrides = {
        // The maximum units of gas for the transaction to use
        // gasLimit: 2300000,

        // The price (in wei) per unit of gas
        // gasPrice: utils.parseUnits('9.0', 'gwei'),

        // The nonce to use in the transaction
        // nonce: 123,

        // The amount to send with the transaction (i.e. msg.value)
        value: _value,

        // The chain ID (or network ID) to use
        // chainId: 1
      };
      // let overrides = {value:_value}
      // 常见合约工厂实例

      const contract = await Mode1.CreateContract(
        this.salt,
        stringParam,
        addressParam,
        numberParam,
        boolParam,
        overrides
      );
      console.log("hash", contract.hash);

      await contract.wait();
      this.loading = false;
      this.console = true;
      this.$message({
        type: "success",
        message: this.$t("preSale.simpleMint.创建成功") + "!",
      });
    },

    async getReadytoCreate() {
      await this.checkParams();
      this.value = this.getBigInt(this.Fee, 18);
      const encodedbytes = this.presaleData.Sale1encodebytes;
      this.salt = ethers.utils.id("pandatoken");
      // console.log("salt", this.salt);

      // console.log("encodedbytes", this.presaleData.Sale1encodebytes);

      const stringParam = [this.form.name];
      const addressParam = [
        this.form.tokenAddr,
        this.form.donateETHAddr,
        this.form.donateTokenAddr,
        this.form._swapRouter,
      ];
      const numberParam = [
        this.form.bigPrice,
        this.form.bigAmountPerUnits,
        Number(this.form.mintLimit),
        Number(this.form.accEachLimit),
        Number(this.form.accMintLimit),

        Number(this.form.addPart),
        Number(this.form.donateETHPart),
        Number(this.form.donateTokenPart),
      ];
      const boolParam = [this.form.enableAddLP, this.form.enableDonate];
      // console.log("stringParam", stringParam);
      // console.log("addressParam", addressParam);
      // console.log("numberParam", numberParam);
      // console.log("boolParam", boolParam);
      var abiCoder = new ethers.utils.AbiCoder();
      this.constructorArgs = abiCoder
        .encode(
          ["string[]", "address[]", "uint256[]", "bool[]"],
          [stringParam, addressParam, numberParam, boolParam]
        )
        .slice(2);
      //   console.log("encodeABI", this.constructorArgs);

      const initCode = encodedbytes + this.constructorArgs;

      //   console.log("initCode", initCode);
      const initCodeHash = ethers.utils.keccak256(initCode);
      //   console.log("initCodeHash", initCodeHash);
      this.coinAddress = ethers.utils.getCreate2Address(
        this.ModeAddress,
        this.salt,
        initCodeHash
      );

      console.log(this.coinAddress);

      const Mode1Abi = this.Mode1;
      const provider = store.state.user.provider;

      const Mode1 = new ethers.Contract(this.ModeAddress, Mode1Abi, provider);
      let overrides = { value: this.value };
      console.log("value", overrides);
      const GasPrice = await provider.getGasPrice();
      const estimateGas = await Mode1.estimateGas.CreateContract(
        this.salt,
        stringParam,
        addressParam,
        numberParam,
        boolParam
      );
      console.log(this.Fee);
      this.gasFee = parseFloat(
        new Number(GasPrice.mul(estimateGas)) / 10 ** 18 + Number(this.Fee)
      ).toFixed(6);
      console.log("gasfee", this.gasFee);

      this.dialogVisible = true;
    },
    enterConsole() {
      this.$router.push({
        path: "/presale/mintAddSaleDetail/",
        query: { coinAddress: this.coinAddress },
      });
    },

    copy_str(text) {
      if (navigator.clipboard) {
        // clipboard api 复制
        navigator.clipboard.writeText(text);
      } else {
        var textarea = document.createElement("textarea");
        document.body.appendChild(textarea);
        // 隐藏此输入框
        textarea.style.position = "fixed";
        textarea.style.clip = "rect(0 0 0 0)";
        textarea.style.top = "10px";
        // 赋值
        textarea.value = text;
        // 选中
        textarea.select();
        // 复制
        document.execCommand("copy", true);
        // 移除输入框
        document.body.removeChild(textarea);
      }
      this.$message({
        type: "success",
        message: this.$t("preSale.simpleMint.已复制") + "!",
      });
    },
    getBigInt(floatNumber, tragetDecimals) {
      var floatArray = floatNumber.toString().split(".");
      let rsValue;
      if (floatArray.length == 1) {
        rsValue = BigNumber.from(floatArray[0]).mul(
          BigNumber.from(10).pow(tragetDecimals)
        );
        console.log(rsValue);
        return rsValue;
      } else if (floatArray.length == 2) {
        var intPart = BigNumber.from(floatArray[0]).mul(
          BigNumber.from(10).pow(tragetDecimals)
        );
        var floatPart = BigNumber.from(floatArray[1]).mul(
          BigNumber.from(10).pow(tragetDecimals - floatArray[1].length)
        );
        rsValue = intPart.add(floatPart);
        console.log(rsValue);
        return rsValue;
      } else {
        return null;
      }
    },
    checkDecimal(num) {
      var str = num;
      var len1 = str.substr(0, 1);
      var len2 = str.substr(1, 1);
      //如果第一位是0，第二位不是点，就用数字把点替换掉
      if (str.length > 1 && len1 == 0 && len2 != ".") {
        str = str.substr(1, 1);
      }
      //第一位不能是.
      if (len1 == ".") {
        str = "";
      }
      //限制只能输入一个小数点
      if (str.indexOf(".") != -1) {
        var str_ = str.substr(str.indexOf(".") + 1);
        if (str_.indexOf(".") != -1) {
          str = str.substr(0, str.indexOf(".") + str_.indexOf(".") + 1);
        }
      }
      //正则替换
      str = str.replace(/[^\d^\.]+/g, ""); // 保留数字和小数点

      str = str.replace(/(\.\d{3})\d+$/, ""); // 小数点后只能输三位

      return str;
    },
  },
};
</script>

<style scoped></style>
