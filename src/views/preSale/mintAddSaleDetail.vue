<template>
  <div class="app-container">
    <div class="inner">
      <el-header class="header">
        <p>
          {{ $t("preSale.mintAddSaleDetail.预售控制台") }}
          <i
            class="el-icon-copy-document"
            style="
              margin-left: 10px;
              font-size: 12px;
              font-weight: 400;
              color: #409eff;
            "
            @click="copy_str(url)"
            >{{ $t("preSale.mintAddSaleDetail.复制链接") }}</i
          >
        </p>
      </el-header>
      <el-main class="main">
        <el-row :gutter="24">
          <el-col :xs="24" :sm="18">
            <el-card shadow="always" class="box-card">
              <div slot="header" class="clearfix">
                <span>{{ $t("preSale.mintAddSaleDetail.基本信息") }}</span>
              </div>
              <el-descriptions size="medium" :column="isMobile ? 1 : 2" border>
                <el-descriptions-item
                  :label="$t('preSale.mintAddSaleDetail.预售名称')"
                  >{{ basicParams.name }}</el-descriptions-item
                >
                <el-descriptions-item
                  :label="$t('preSale.mintAddSaleDetail.预售地址')"
                >
                  {{ getContract }}
                  <i
                    class="el-icon-copy-document"
                    @click="copy_str(ContractAddr)"
                  ></i>
                </el-descriptions-item>

                <el-descriptions-item
                  :label="$t('preSale.mintAddSaleDetail.预售代币简称')"
                  >{{ tokenParams.symbol }}</el-descriptions-item
                >

                <el-descriptions-item
                  :label="$t('preSale.mintAddSaleDetail.预售代币地址')"
                >
                  {{ getTokenContract }}
                  <i
                    class="el-icon-copy-document"
                    @click="copy_str(basicParams.tokenAddr)"
                  ></i>
                </el-descriptions-item>

                <el-descriptions-item
                  :label="$t('preSale.mintAddSaleDetail.预售模版')"
                >
                  {{ getMode }}
                </el-descriptions-item>
                <el-descriptions-item
                  :label="$t('preSale.mintAddSaleDetail.所有者')"
                >
                  {{ getOwner }}
                  <i
                    class="el-icon-copy-document"
                    @click="copy_str(basicParams._owner)"
                  ></i>
                </el-descriptions-item>
              </el-descriptions>
            </el-card>
            <el-card shadow="always" class="box-card">
              <div slot="header" class="clearfix">
                <span>{{ $t("preSale.mintAddSaleDetail.预售情况") }}</span>
              </div>
              <el-descriptions
                size="medium"
                :column="1"
                border
                :contentStyle="CS"
                :label-style="LS"
              >
                <el-descriptions-item
                  :label="$t('preSale.mintAddSaleDetail.已售份数')"
                  >{{ ecoParams.minted }}</el-descriptions-item
                >
                <el-descriptions-item
                  :label="$t('preSale.mintAddSaleDetail.合约内代币数量')"
                >
                  {{ getTokenBalance }}</el-descriptions-item
                >
                <el-descriptions-item :label="this.ETHBalanceLabel">
                  {{ getETHBalance }}</el-descriptions-item
                >
              </el-descriptions>
            </el-card>
            <el-card shadow="always" class="box-card">
              <div slot="header" class="clearfix">
                <span>{{ $t("preSale.mintAddSaleDetail.预售参数") }}</span>
              </div>
              <el-descriptions
                size="medium"
                :column="1"
                border
                :contentStyle="CS"
                :label-style="LS"
              >
                <el-descriptions-item
                  :label="$t('preSale.mintAddSaleDetail.价格')"
                >
                  {{ getPrice }} {{ this.chainSymbol }}
                </el-descriptions-item>
                <el-descriptions-item
                  :label="$t('preSale.mintAddSaleDetail.每份数量')"
                >
                  {{ getAmountUint }}
                </el-descriptions-item>
                <el-descriptions-item
                  :label="$t('preSale.mintAddSaleDetail.总份数')"
                >
                  {{ ecoParams.mintLimit }}
                </el-descriptions-item>
                <el-descriptions-item
                  :label="$t('preSale.mintAddSaleDetail.单次Mint最大份数')"
                >
                  {{ ecoParams.accEachLimit }}
                </el-descriptions-item>
                <el-descriptions-item
                  :label="$t('preSale.mintAddSaleDetail.单钱包最大份数')"
                >
                  {{ ecoParams.accMintLimit }}
                </el-descriptions-item>
              </el-descriptions>
              <el-descriptions
                style="margin-top: 2px"
                v-show="ecoParams.enableAddLP"
                size="medium"
                :column="1"
                border
                :contentStyle="CS"
                :label-style="LS"
              >
                <el-descriptions-item
                  :label="$t('preSale.mintAddSaleDetail.加池比例')"
                >
                  {{ ecoParams.addPart }}%
                </el-descriptions-item>
                <el-descriptions-item
                  :label="$t('preSale.mintAddSaleDetail.交易所')"
                >
                  {{ getSwap }}
                  <i
                    class="el-icon-copy-document"
                    @click="copy_str(ecoParams._swapRouter)"
                  ></i>
                </el-descriptions-item>
                <el-descriptions-item
                  :label="$t('preSale.mintAddSaleDetail.池子地址')"
                >
                  {{ getPool }}
                  <i
                    class="el-icon-copy-document"
                    @click="copy_str(ecoParams._mainPair)"
                  ></i>
                </el-descriptions-item>

                <el-descriptions-item
                  :label="$t('preSale.mintAddSaleDetail.营销钱包')"
                >
                  {{ getFund }}
                  <i
                    class="el-icon-copy-document"
                    @click="copy_str(ecoParams.fundAddress)"
                  ></i>
                </el-descriptions-item>
              </el-descriptions>
              <el-descriptions
                style="margin-top: 2px"
                v-show="ecoParams.enableDonate"
                size="medium"
                :column="1"
                border
                :contentStyle="CS"
                :label-style="LS"
              >
                <el-descriptions-item :label="this.donatePartLabel">
                  {{ ecoParams.donateETHPart }}%
                </el-descriptions-item>

                <el-descriptions-item :label="this.donateAddrLabel">
                  {{ getDonateETH }}
                  <i
                    class="el-icon-copy-document"
                    @click="copy_str(ecoParams.donateETHAddr)"
                  ></i>
                </el-descriptions-item>
                <el-descriptions-item
                  :label="$t('preSale.mintAddSaleDetail.捐赠代币比例')"
                >
                  {{ ecoParams.donateTokenPart }}%
                </el-descriptions-item>

                <el-descriptions-item
                  :label="$t('preSale.mintAddSaleDetail.接收代币地址')"
                >
                  {{ getDonateToken }}
                  <i
                    class="el-icon-copy-document"
                    @click="copy_str(ecoParams.donateTokenAddr)"
                  ></i>
                </el-descriptions-item>
              </el-descriptions>
            </el-card>
          </el-col>
          <el-col :xs="24" :sm="6">
            <el-card v-show="partShow" shadow="always" class="box-card">
              <div slot="header" class="clearfix">
                <span>{{ $t("preSale.mintAddSaleDetail.预售控制") }}</span>
              </div>
              <div class="controlbutton">
                <el-button
                  size="mini"
                  plain
                  @click="dialogVisible.changeOwnerVisible = true"
                  >{{ $t("preSale.mintAddSaleDetail.转让所有权") }}</el-button
                >
                <el-dialog
                  :title="$t('preSale.mintAddSaleDetail.转让所有权')"
                  :visible.sync="dialogVisible.changeOwnerVisible"
                  width="30%"
                  center
                >
                  <div>
                    <p>{{ $t("preSale.mintAddSaleDetail.转让地址") }}</p>
                    <el-input v-model="temValue" placeholder="0x..." />
                  </div>

                  <span slot="footer" class="dialog-footer">
                    <el-button
                      size="mini"
                      plain
                      @click="dialogVisible.changeOwnerVisible = false"
                      >{{ $t("preSale.mintAddSaleDetail.取消") }}</el-button
                    >
                    <el-button
                      size="mini"
                      plain
                      v-if="loading.changeOwner"
                      type="primary"
                      :loading="true"
                      >{{ $t("preSale.mintAddSaleDetail.确定") }}</el-button
                    >
                    <el-button
                      size="mini"
                      plain
                      v-else
                      type="primary"
                      @click="transferOwnership(temValue)"
                      >{{ $t("preSale.mintAddSaleDetail.确定") }}</el-button
                    >
                  </span>
                </el-dialog>
              </div>
              <div class="controlbutton">
                <el-button
                  size="mini"
                  v-if="loading.launch"
                  :loading="true"
                  plain
                  >{{ $t("preSale.mintAddSaleDetail.开启预售") }}</el-button
                >
                <el-button
                  size="mini"
                  v-else-if="ecoParams.start"
                  plain
                  disabled
                  >{{ $t("preSale.mintAddSaleDetail.开启预售") }}</el-button
                >
                <el-button size="mini" v-else plain @click="launch()">{{
                  $t("preSale.mintAddSaleDetail.开启预售")
                }}</el-button>
              </div>

              <div class="controlbutton">
                <el-button
                  size="mini"
                  plain
                  @click="dialogVisible.setClaims = true"
                  >{{
                    $t("preSale.mintAddSaleDetail.提取合约内代币")
                  }}</el-button
                >
                <el-dialog
                  :title="$t('preSale.mintAddSaleDetail.提取合约内代币')"
                  :visible.sync="dialogVisible.setClaims"
                  width="35%"
                  center
                >
                  <el-radio-group v-model="temBool">
                    <el-radio :label="true">{{ this.chainSymbol }}</el-radio>
                    <el-radio :label="false">{{
                      $t("preSale.mintAddSaleDetail.预售代币")
                    }}</el-radio>
                  </el-radio-group>

                  <div style="margin-top: 30px">
                    <p>{{ $t("preSale.mintAddSaleDetail.数量") }}</p>
                    <el-input
                      v-model="temValue"
                      type="number"
                      placeholder="10"
                    ></el-input>
                  </div>

                  <span slot="footer" class="dialog-footer">
                    <el-button
                      size="mini"
                      plain
                      @click="dialogVisible.setClaims = false"
                      >{{ $t("preSale.mintAddSaleDetail.取消") }}</el-button
                    >
                    <el-button
                      size="mini"
                      v-if="loading.setClaims"
                      :loading="true"
                      type="primary"
                      plain
                      >{{ $t("preSale.mintAddSaleDetail.确定") }}</el-button
                    >
                    <el-button
                      size="mini"
                      v-else
                      type="primary"
                      plain
                      @click="setClaims(temValue, temBool)"
                      >{{ $t("preSale.mintAddSaleDetail.确定") }}</el-button
                    >
                  </span>
                </el-dialog>
              </div>
            </el-card>
            <el-card v-show="partShow" shadow="always" class="box-card">
              <div slot="header" class="clearfix">
                <span>{{ $t("preSale.mintAddSaleDetail.参数控制") }}</span>
              </div>
              <div class="controlbutton">
                <el-button
                  size="mini"
                  plain
                  @click="dialogVisible.setPrice = true"
                  >{{ $t("preSale.mintAddSaleDetail.修改每份价格") }}</el-button
                >
                <el-dialog
                  :title="$t('preSale.mintAddSaleDetail.修改Mint每份价格')"
                  :visible.sync="dialogVisible.setPrice"
                  width="30%"
                  center
                >
                  <div>
                    <p>{{ $t("preSale.mintAddSaleDetail.每份价格") }}</p>
                    <el-input
                      v-model="temValue"
                      @keyup.native="temValue = checkDecimal(temValue)"
                      placeholder="0.01"
                    >
                    </el-input>
                  </div>

                  <span slot="footer" class="dialog-footer">
                    <el-button
                      size="mini"
                      plain
                      @click="dialogVisible.setPrice = false"
                      >{{ $t("preSale.mintAddSaleDetail.取消") }}</el-button
                    >
                    <el-button
                      size="mini"
                      plain
                      v-if="loading.setPrice"
                      type="primary"
                      :loading="true"
                      >{{ $t("preSale.mintAddSaleDetail.确定") }}</el-button
                    >
                    <el-button
                      size="mini"
                      plain
                      v-else
                      type="primary"
                      @click="setPrice(temValue)"
                      >{{ $t("preSale.mintAddSaleDetail.确定") }}</el-button
                    >
                  </span>
                </el-dialog>
              </div>
              <div class="controlbutton">
                <el-button
                  size="mini"
                  plain
                  @click="dialogVisible.setAmountPerUnits = true"
                  >{{ $t("preSale.mintAddSaleDetail.修改每份数量") }}</el-button
                >
                <el-dialog
                  :title="$t('preSale.mintAddSaleDetail.修改每份数量')"
                  :visible.sync="dialogVisible.setAmountPerUnits"
                  width="30%"
                  center
                >
                  <div>
                    <p>{{ $t("preSale.mintAddSaleDetail.修改每份数量") }}</p>
                    <el-input
                      v-model="temValue"
                      @keyup.native="temValue = checkDecimal(temValue)"
                      placeholder="1000"
                    >
                    </el-input>
                  </div>

                  <span slot="footer" class="dialog-footer">
                    <el-button
                      size="mini"
                      plain
                      @click="dialogVisible.setAmountPerUnits = false"
                      >{{ $t("preSale.mintAddSaleDetail.取消") }}</el-button
                    >
                    <el-button
                      size="mini"
                      plain
                      v-if="loading.setAmountPerUnits"
                      type="primary"
                      :loading="true"
                      >{{ $t("preSale.mintAddSaleDetail.确定") }}</el-button
                    >
                    <el-button
                      size="mini"
                      plain
                      v-else
                      type="primary"
                      @click="setAmountPerUnits(temValue)"
                      >{{ $t("preSale.mintAddSaleDetail.确定") }}</el-button
                    >
                  </span>
                </el-dialog>
              </div>

              <div class="controlbutton">
                <el-button
                  size="mini"
                  plain
                  @click="dialogVisible.setMintLimit = true"
                  >{{ $t("preSale.mintAddSaleDetail.修改总份数") }}</el-button
                >
                <el-dialog
                  :title="$t('preSale.mintAddSaleDetail.修改总份数')"
                  :visible.sync="dialogVisible.setMintLimit"
                  width="30%"
                  center
                >
                  <div>
                    <p>{{ $t("preSale.mintAddSaleDetail.修改总份数") }}</p>
                    <el-input v-model="temValue" type="number" placeholder="10">
                    </el-input>
                  </div>

                  <span slot="footer" class="dialog-footer">
                    <el-button
                      size="mini"
                      plain
                      @click="dialogVisible.setMintLimit = false"
                      >{{ $t("preSale.mintAddSaleDetail.取消") }}</el-button
                    >
                    <el-button
                      size="mini"
                      plain
                      v-if="loading.setMintLimit"
                      type="primary"
                      :loading="true"
                      >{{ $t("preSale.mintAddSaleDetail.确定") }}</el-button
                    >
                    <el-button
                      size="mini"
                      plain
                      v-else
                      type="primary"
                      @click="setMintLimit(temValue)"
                      >{{ $t("preSale.mintAddSaleDetail.确定") }}</el-button
                    >
                  </span>
                </el-dialog>
              </div>

              <div class="controlbutton">
                <el-button
                  size="mini"
                  plain
                  @click="dialogVisible.setAccEachLimit = true"
                  >{{
                    $t("preSale.mintAddSaleDetail.修改单次Mint最大份数")
                  }}</el-button
                >
                <el-dialog
                  :title="$t('preSale.mintAddSaleDetail.修改单次Mint最大份数')"
                  :visible.sync="dialogVisible.setAccEachLimit"
                  width="30%"
                  center
                >
                  <div>
                    <p>
                      {{ $t("preSale.mintAddSaleDetail.修改单次Mint最大份数") }}
                    </p>
                    <el-input v-model="temValue" type="number" placeholder="10">
                    </el-input>
                  </div>

                  <span slot="footer" class="dialog-footer">
                    <el-button
                      size="mini"
                      plain
                      @click="dialogVisible.setAccEachLimit = false"
                      >{{ $t("preSale.mintAddSaleDetail.取消") }}</el-button
                    >
                    <el-button
                      size="mini"
                      plain
                      v-if="loading.setAccEachLimit"
                      type="primary"
                      :loading="true"
                      >{{ $t("preSale.mintAddSaleDetail.确定") }}</el-button
                    >
                    <el-button
                      size="mini"
                      plain
                      v-else
                      type="primary"
                      @click="setAccEachLimit(temValue)"
                      >{{ $t("preSale.mintAddSaleDetail.确定") }}</el-button
                    >
                  </span>
                </el-dialog>
              </div>

              <div class="controlbutton">
                <el-button
                  size="mini"
                  plain
                  @click="dialogVisible.setAccMintLimit = true"
                  >{{
                    $t("preSale.mintAddSaleDetail.修改单钱包最大份数")
                  }}</el-button
                >
                <el-dialog
                  :title="$t('preSale.mintAddSaleDetail.修改单钱包最大份数')"
                  :visible.sync="dialogVisible.setAccMintLimit"
                  width="30%"
                  center
                >
                  <div>
                    <p>
                      {{ $t("preSale.mintAddSaleDetail.修改单钱包最大份数") }}
                    </p>
                    <el-input v-model="temValue" type="number" placeholder="10">
                    </el-input>
                  </div>

                  <span slot="footer" class="dialog-footer">
                    <el-button
                      size="mini"
                      plain
                      @click="dialogVisible.setAccMintLimit = false"
                      >{{ $t("preSale.mintAddSaleDetail.取消") }}</el-button
                    >
                    <el-button
                      size="mini"
                      plain
                      v-if="loading.setAccMintLimit"
                      type="primary"
                      :loading="true"
                      >{{ $t("preSale.mintAddSaleDetail.确定") }}</el-button
                    >
                    <el-button
                      size="mini"
                      plain
                      v-else
                      type="primary"
                      @click="setAccMintLimit(temValue)"
                      >{{ $t("preSale.mintAddSaleDetail.确定") }}</el-button
                    >
                  </span>
                </el-dialog>
              </div>
            </el-card>
            <el-card
              v-show="ecoParams.enableAddLP"
              shadow="always"
              class="box-card"
            >
              <div slot="header" class="clearfix">
                <span>{{ $t("preSale.mintAddSaleDetail.加池控制") }}</span>
              </div>
              <div class="controlbutton">
                <el-button
                  size="mini"
                  plain
                  @click="dialogVisible.changeFundVisible = true"
                  >{{ $t("preSale.mintAddSaleDetail.修改营销钱包") }}</el-button
                >
                <el-dialog
                  :title="$t('preSale.mintAddSaleDetail.修改营销钱包')"
                  :visible.sync="dialogVisible.changeFundVisible"
                  width="30%"
                  center
                >
                  <div>
                    <p>{{ $t("preSale.mintAddSaleDetail.营销钱包地址") }}</p>
                    <el-input v-model="temValue" placeholder="0x..." />
                  </div>

                  <span slot="footer" class="dialog-footer">
                    <el-button
                      size="mini"
                      plain
                      @click="dialogVisible.changeFundVisible = false"
                      >{{ $t("preSale.mintAddSaleDetail.取消") }}</el-button
                    >
                    <el-button
                      size="mini"
                      v-if="loading.changeFund"
                      :loading="true"
                      type="primary"
                      plain
                      >{{ $t("preSale.mintAddSaleDetail.确定") }}</el-button
                    >
                    <el-button
                      size="mini"
                      v-else
                      type="primary"
                      plain
                      @click="setFundAddress(temValue)"
                      >{{ $t("preSale.mintAddSaleDetail.确定") }}</el-button
                    >
                  </span>
                </el-dialog>
              </div>
              <div class="controlbutton">
                <el-button
                  size="mini"
                  plain
                  @click="dialogVisible.setAddPart = true"
                  >{{ $t("preSale.mintAddSaleDetail.修改加池比例") }}</el-button
                >
                <el-dialog
                  :title="$t('preSale.mintAddSaleDetail.修改加池比例')"
                  :visible.sync="dialogVisible.setAddPart"
                  width="30%"
                  center
                >
                  <div>
                    <p>{{ $t("preSale.mintAddSaleDetail.加池比例") }}</p>
                    <el-input-number
                      v-model="temPart"
                      :min="10"
                      :max="100"
                      :step="10"
                      size="small"
                    ></el-input-number>
                  </div>

                  <span slot="footer" class="dialog-footer">
                    <el-button
                      size="mini"
                      plain
                      @click="dialogVisible.setAddPart = false"
                      >{{ $t("preSale.mintAddSaleDetail.取消") }}</el-button
                    >
                    <el-button
                      size="mini"
                      v-if="loading.setAddPart"
                      :loading="true"
                      type="primary"
                      plain
                      >{{ $t("preSale.mintAddSaleDetail.确定") }}</el-button
                    >
                    <el-button
                      size="mini"
                      v-else
                      type="primary"
                      plain
                      @click="setAddPart(temPart)"
                      >{{ $t("preSale.mintAddSaleDetail.确定") }}</el-button
                    >
                  </span>
                </el-dialog>
              </div>
            </el-card>
            <el-card
              v-show="ecoParams.enableDonate"
              shadow="always"
              class="box-card"
            >
              <div slot="header" class="clearfix">
                <span>{{ $t("preSale.mintAddSaleDetail.捐赠控制") }}</span>
              </div>
              <div class="controlbutton">
                <el-button
                  size="mini"
                  plain
                  @click="dialogVisible.setDonateETHAddress = true"
                  >{{
                    $t("preSale.mintAddSaleDetail.修改接收钱包", {
                      chainSymbol: chainSymbol,
                    })
                  }}
                </el-button>
                <el-dialog
                  :title="$t('preSale.mintAddSaleDetail.修改接收钱包1')"
                  :visible.sync="dialogVisible.setDonateETHAddress"
                  width="30%"
                  center
                >
                  <div>
                    <p>
                      {{
                        $t("preSale.mintAddSaleDetail.修改接收钱包", {
                          chainSymbol: chainSymbol,
                        })
                      }}
                    </p>
                    <el-input v-model="temValue" placeholder="0x..." />
                  </div>

                  <span slot="footer" class="dialog-footer">
                    <el-button
                      size="mini"
                      plain
                      @click="dialogVisible.setDonateETHAddress = false"
                      >{{ $t("preSale.mintAddSaleDetail.取消") }}
                    </el-button>
                    <el-button
                      size="mini"
                      v-if="loading.setDonateETHAddress"
                      :loading="true"
                      type="primary"
                      plain
                      >{{ $t("preSale.mintAddSaleDetail.确定") }}</el-button
                    >
                    <el-button
                      size="mini"
                      v-else
                      type="primary"
                      plain
                      @click="setDonateETHAddress(temValue)"
                      >{{ $t("preSale.mintAddSaleDetail.确定") }}</el-button
                    >
                  </span>
                </el-dialog>
              </div>
              <div class="controlbutton">
                <el-button
                  size="mini"
                  plain
                  @click="dialogVisible.setDonateETHPart = true"
                  >{{
                    $t("preSale.mintAddSaleDetail.修改捐赠比例", {
                      chainSymbol: chainSymbol,
                    })
                  }}</el-button
                >
                <el-dialog
                  :title="$t('preSale.mintAddSaleDetail.修改捐赠比例1')"
                  :visible.sync="dialogVisible.setDonateETHPart"
                  width="30%"
                  center
                >
                  <div>
                    <p>
                      {{
                        $t("preSale.mintAddSaleDetail.修改捐赠比例", {
                          chainSymbol: chainSymbol,
                        })
                      }}
                    </p>
                    <el-input-number
                      v-model="temPart"
                      :min="0"
                      :max="100"
                      :step="10"
                      size="small"
                    ></el-input-number>
                  </div>

                  <span slot="footer" class="dialog-footer">
                    <el-button
                      size="mini"
                      plain
                      @click="dialogVisible.setDonateETHPart = false"
                      >{{ $t("preSale.mintAddSaleDetail.取消") }}</el-button
                    >
                    <el-button
                      size="mini"
                      v-if="loading.setDonateETHPart"
                      :loading="true"
                      type="primary"
                      plain
                      >{{ $t("preSale.mintAddSaleDetail.确定") }}</el-button
                    >
                    <el-button
                      size="mini"
                      v-else
                      type="primary"
                      plain
                      @click="setDonateETHPart(temPart)"
                      >{{ $t("preSale.mintAddSaleDetail.确定") }}</el-button
                    >
                  </span>
                </el-dialog>
              </div>
              <div class="controlbutton">
                <el-button
                  size="mini"
                  plain
                  @click="dialogVisible.setDonateTokenAddress = true"
                  >{{
                    $t("preSale.mintAddSaleDetail.修改接收代币钱包", {
                      symbol: tokenParams.symbol,
                    })
                  }}</el-button
                >
                <el-dialog
                  :title="$t('preSale.mintAddSaleDetail.修改接收代币钱包1')"
                  :visible.sync="dialogVisible.setDonateTokenAddress"
                  width="30%"
                  center
                >
                  <div>
                    <p>
                      {{
                        $t("preSale.mintAddSaleDetail.修改接收代币钱包", {
                          symbol: tokenParams.symbol,
                        })
                      }}
                    </p>
                    <el-input v-model="temValue" placeholder="0x..." />
                  </div>

                  <span slot="footer" class="dialog-footer">
                    <el-button
                      size="mini"
                      plain
                      @click="dialogVisible.setDonateTokenAddress = false"
                      >{{ $t("preSale.mintAddSaleDetail.取消") }}</el-button
                    >
                    <el-button
                      size="mini"
                      v-if="loading.setDonateTokenAddress"
                      :loading="true"
                      type="primary"
                      plain
                      >{{ $t("preSale.mintAddSaleDetail.确定") }}</el-button
                    >
                    <el-button
                      size="mini"
                      v-else
                      type="primary"
                      plain
                      @click="setDonateTokenAddress(temValue)"
                      >{{ $t("preSale.mintAddSaleDetail.确定") }}</el-button
                    >
                  </span>
                </el-dialog>
              </div>
              <div class="controlbutton">
                <el-button
                  size="mini"
                  plain
                  @click="dialogVisible.setDonateTokenPart = true"
                  >{{
                    $t("preSale.mintAddSaleDetail.修改捐赠代币比例", {
                      symbol: tokenParams.symbol,
                    })
                  }}</el-button
                >
                <el-dialog
                  :title="$t('preSale.mintAddSaleDetail.修改捐赠代币比例1')"
                  :visible.sync="dialogVisible.setDonateTokenPart"
                  width="30%"
                  center
                >
                  <div>
                    <p>
                      {{ $t("preSale.mintAddSaleDetail.捐赠比例") }}
                    </p>
                    <el-input-number
                      v-model="temPart"
                      :min="0"
                      :max="100"
                      :step="10"
                      size="small"
                    ></el-input-number>
                  </div>

                  <span slot="footer" class="dialog-footer">
                    <el-button
                      size="mini"
                      plain
                      @click="dialogVisible.setDonateTokenPart = false"
                      >{{ $t("preSale.mintAddSaleDetail.取消") }}</el-button
                    >
                    <el-button
                      size="mini"
                      v-if="loading.setDonateTokenPart"
                      :loading="true"
                      type="primary"
                      plain
                      >{{ $t("preSale.mintAddSaleDetail.确定") }}</el-button
                    >
                    <el-button
                      size="mini"
                      v-else
                      type="primary"
                      plain
                      @click="setDonateTokenPart(temPart)"
                      >{{ $t("preSale.mintAddSaleDetail.确定") }}</el-button
                    >
                  </span>
                </el-dialog>
              </div>
            </el-card>
            <el-card shadow="always" class="box-card">
              <div slot="header" class="clearfix">
                <span>{{ $t("preSale.mintAddSaleDetail.预售官网示例") }}</span>
              </div>
              <a
                href="https://web-seven-gray-88.vercel.app/sbc1-web/index.html#mint"
                target="_blank"
              >
                <img src="../../assets/example.jpg" style="height: 100px" />
              </a>
              <h5>
                <el-link
                  style="color: #40b3e0; font-size: small"
                  icon="el-icon-telegram"
                  href="https://t.me/btc6560"
                  target="_blank"
                  >{{
                    $t("preSale.mintAddSaleDetail.需要定制官网请联系商务")
                  }}</el-link
                >
              </h5>
            </el-card>
          </el-col>
        </el-row>
      </el-main>
    </div>
  </div>
</template>

<script>
import store from "@/store";
import chainParams from "@/contracts/presaleParams.json";
import Mode1 from "@/contracts/pandaMode1.json";
import presaleData from "@/contracts/presaleBytesABI.json";
import standardData from "@/contracts/standardCoin.json";
const { ethers, BigNumber } = require("ethers");
export default {
  name: "Dashboard",
  data() {
    return {
      standardData,
      presaleData,
      chainParams,
      Mode1,
      ModeType: null,
      url: null,
      ContractAddr: null,
      tokenABI: null,
      saleABI: null,
      temValue: null,
      temPart: 0,
      temBool: true,
      chainSymbol: "ETH",
      ETHBalanceLabel: null,
      donatePartLabel: null,
      donateAddrLabel: null,
      basicParams: {
        name: null,
        tokenAddr: null,

        _owner: null,
      },
      ecoParams: {
        _swapRouter: null,
        _mainPair: null,
        start: false,
        price: null,
        amountPerUnits: null,
        mintLimit: null,
        minted: null,
        accEachLimit: null,
        accMintLimit: null,

        enableAddLP: false,
        addPart: 1,
        fundAddress: null,

        enableDonate: false,
        donateETHPart: 1,
        donateTokenPart: 1,
        donateETHAddr: null,
        donateTokenAddr: null,
      },
      tokenParams: {
        symbol: null,
        decimals: null,
        tokenBalacne: null,
        ETHBalance: null,
      },
      modeType: null,
      partShow: true,
      dialogVisible: {
        changeOwnerVisible: false,

        setSwapPairListVisible: false,
        changeExTaxVisible: false,
        changeWhiterVisible: false,
        changeFundVisible: false,

        setPrice: false,
        setAmountPerUnits: false,
        setMintLimit: false,
        setAccEachLimit: false,
        setAccMintLimit: false,
        setClaims: false,

        setAddPart: false,
        setDonateETHPart: false,
        setDonateTokenPart: false,
        setDonateETHAddress: false,
        setDonateTokenAddress: false,
      },
      loading: {
        changeOwner: false,
        renounceOwner: false,
        launch: false,

        changeFund: false,
        setClaims: false,

        setNumTokensSellRate: false,

        setPrice: false,
        setAmountPerUnits: false,
        setMintLimit: false,
        setAccEachLimit: false,
        setAccMintLimit: false,

        setAddPart: false,
        setDonateETHPart: false,
        setDonateTokenPart: false,
        setDonateETHAddress: false,
        setDonateTokenAddress: false,
      },

      swapOptions: [],

      CS: {
        "text-align": "left",
        "min-width": "120px",
        "word-break": "break-all",
      },
      LS: {
        "text-align": "center",
        height: "40px",
        "max-width": "70px",
        "word-break": "break-all",
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now();
        },
      },
    };
  },

  created() {
    this.ContractAddr = this.$route.query.coinAddress;
    this.url = window.location.href;
    this.saleABI = presaleData.Sale1abi;
    this.tokenABI = standardData.Coin1abi;
    const provider = store.state.user.provider;
    const signer = provider.getSigner();

    const preSaleContract = new ethers.Contract(
      this.ContractAddr,
      this.saleABI,
      signer
    );
    this.getBasicParams(preSaleContract);
    let Inval = setInterval(() => {
      if (this.basicParams._owner) {
        if (
          ethers.utils.getAddress(this.basicParams._owner) !=
          ethers.utils.getAddress(store.state.user.address)
        ) {
          this.partShow = false;
        }
      }
      if (store.state.user.chainId) {
        window.clearInterval(Inval);
        const chainId = store.state.user.chainId;

        const selectChainParams = chainParams[chainId][1];
        this.chainSymbol = selectChainParams.chainSymbol;
        this.donatePartLabel = this.$t(
          "preSale.mintAddSaleDetail.donatePartLabel",
          { eth: selectChainParams.chainSymbol }
        );
        // this.$t("preSale.mintAddSaleDetail.捐赠") +
        // selectChainParams.chainSymbol +
        // this.$t("preSale.mintAddSaleDetail.比例");
        this.donateAddrLabel = this.$t(
          "preSale.mintAddSaleDetail.donateAddrLabel",
          { eth: selectChainParams.chainSymbol }
        );
        // this.$t("preSale.mintAddSaleDetail.接收") +
        // selectChainParams.chainSymbol +
        // this.$t("preSale.mintAddSaleDetail.地址");
        this.ETHBalanceLabel = this.$t(
          "preSale.mintAddSaleDetail.ETHBalanceLabel",
          { eth: selectChainParams.chainSymbol }
        );
        // this.$t("preSale.mintAddSaleDetail.合约内") +
        // selectChainParams.chainSymbol +
        // this.$t("preSale.mintAddSaleDetail.数量");
        for (let i = 0; i < selectChainParams.swapOptions.length; i++) {
          var temAddress = ethers.utils.getAddress(
            selectChainParams.swapOptions[i].value
          );
          var temParam = {
            label: selectChainParams.swapOptions[i].label,
            value: temAddress,
          };
          this.swapOptions.push(temParam);
        }
      }
    }, 1000);
    this.getEcoParams(preSaleContract);
  },
  computed: {
    isMobile() {
      if (window.screen.width < 500) {
        return true;
      } else {
        return false;
      }
    },
    getTokenBalance() {
      if (this.tokenParams.tokenBalacne) {
        return parseFloat(
          new Number(this.tokenParams.tokenBalacne.toString()) /
            10 ** Number(this.tokenParams.decimals)
        ).toFixed(6);
      } else {
        return null;
      }
    },
    getETHBalance() {
      if (this.tokenParams.ETHBalance) {
        return parseFloat(
          new Number(this.tokenParams.ETHBalance.toString()) / 10 ** 18
        ).toFixed(6);
      } else {
        return null;
      }
    },
    getPrice() {
      if (this.ecoParams.price) {
        return parseFloat(
          new Number(this.ecoParams.price.toString()) / 10 ** 18
        ).toFixed(2);
      } else {
        return null;
      }
    },
    getAmountUint() {
      if (this.ecoParams.amountPerUnits) {
        return parseFloat(
          new Number(this.ecoParams.amountPerUnits.toString()) /
            10 ** Number(this.tokenParams.decimals)
        ).toFixed(6);
      } else {
        return null;
      }
    },

    getOwner() {
      if (this.basicParams._owner) {
        return (
          this.basicParams._owner.substring(0, 5) +
          "..." +
          this.basicParams._owner.substring(37)
        );
      } else {
        return null;
      }
    },
    getContract() {
      if (this.ContractAddr) {
        return (
          this.ContractAddr.substring(0, 5) +
          "..." +
          this.ContractAddr.substring(37)
        );
      } else {
        return null;
      }
    },
    getTokenContract() {
      if (this.basicParams.tokenAddr) {
        return (
          this.basicParams.tokenAddr.substring(0, 5) +
          "..." +
          this.basicParams.tokenAddr.substring(37)
        );
      } else {
        return null;
      }
    },
    getSwap() {
      if (this.ecoParams._swapRouter) {
        for (let i = 0; i < this.swapOptions.length; i++) {
          if (
            ethers.utils.getAddress(this.ecoParams._swapRouter) ==
            ethers.utils.getAddress(this.swapOptions[i].value)
          ) {
            return this.swapOptions[i].label;
          }
        }
        return (
          this.ecoParams._swapRouter.substring(0, 5) +
          "..." +
          this.ecoParams._swapRouter.substring(37)
        );
      } else {
        return null;
      }
    },
    getPool() {
      if (this.ecoParams._mainPair) {
        return (
          this.ecoParams._mainPair.substring(0, 5) +
          "..." +
          this.ecoParams._mainPair.substring(37)
        );
      } else {
        return null;
      }
    },
    getMode() {
      if (this.ecoParams.enableAddLP) {
        return this.$t("preSale.mintAddSaleDetail.mint加池");
      } else if (this.ecoParams.enableDonate) {
        return this.$t("preSale.mintAddSaleDetail.mint捐赠");
      } else {
        return this.$t("preSale.mintAddSaleDetail.标准mint");
      }
    },
    getFund() {
      if (this.ecoParams.fundAddress) {
        return (
          this.ecoParams.fundAddress.substring(0, 5) +
          "..." +
          this.ecoParams.fundAddress.substring(37)
        );
      } else {
        return null;
      }
    },
    getDonateETH() {
      if (this.ecoParams.donateETHAddr) {
        return (
          this.ecoParams.donateETHAddr.substring(0, 5) +
          "..." +
          this.ecoParams.donateETHAddr.substring(37)
        );
      } else {
        return null;
      }
    },
    getDonateToken() {
      if (this.ecoParams.donateTokenAddr) {
        return (
          this.ecoParams.donateTokenAddr.substring(0, 5) +
          "..." +
          this.ecoParams.donateTokenAddr.substring(37)
        );
      } else {
        return null;
      }
    },
    getEnableAdd() {
      if (this.ecoParams.enableAddLP) {
        return this.$t("preSale.mintAddSaleDetail.开启");
      } else {
        return this.$t("preSale.mintAddSaleDetail.关闭");
      }
    },
  },

  methods: {
    checkDecimal(num) {
      var str = num;
      var len1 = str.substr(0, 1);
      var len2 = str.substr(1, 1);
      //如果第一位是0，第二位不是点，就用数字把点替换掉
      if (str.length > 1 && len1 == 0 && len2 != ".") {
        str = str.substr(1, 1);
      }
      //第一位不能是.
      if (len1 == ".") {
        str = "";
      }
      //限制只能输入一个小数点
      if (str.indexOf(".") != -1) {
        var str_ = str.substr(str.indexOf(".") + 1);
        if (str_.indexOf(".") != -1) {
          str = str.substr(0, str.indexOf(".") + str_.indexOf(".") + 1);
        }
      }
      //正则替换
      str = str.replace(/[^\d^\.]+/g, ""); // 保留数字和小数点
      str = str.replace(/(\.\d{3})\d+$/, ""); // 小数点后只能输两位
      return str;
    },
    getBasicParams(contract) {
      contract
        .owner()
        .then((owner) => {
          this.basicParams._owner = owner;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t(
              "preSale.mintAddSaleDetail.获取合约拥有者错误请检查网络"
            ),
          });
        });

      contract
        .name()
        .then((name) => {
          this.basicParams.name = name;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t(
              "preSale.mintAddSaleDetail.获取名称错误请检查网络"
            ),
          });
        });
      contract
        .tokenAddr()
        .then((tokenAddr) => {
          this.basicParams.tokenAddr = tokenAddr;
          this.getTokenParams(tokenAddr);
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t(
              "preSale.mintAddSaleDetail.获取代币地址错误请检查网络"
            ),
          });
        });
    },
    getTokenParams(tokenAddr) {
      const provider = store.state.user.provider;
      const signer = provider.getSigner();

      const tokenContract = new ethers.Contract(
        tokenAddr,
        this.tokenABI,
        signer
      );
      provider
        .getBalance(this.ContractAddr)
        .then((res) => {
          this.tokenParams.ETHBalance = res;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message:
              this.$t(
                "preSale.mintAddSaleDetail.获取合约ETH数量错误请检查网络"
              ) + "",
          });
        });
      tokenContract
        .decimals()
        .then((_decimals) => {
          this.tokenParams.decimals = _decimals;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t(
              "preSale.mintAddSaleDetail.获取代币精度错误请检查网络"
            ),
          });
        });
      tokenContract
        .symbol()
        .then((symbol) => {
          this.tokenParams.symbol = symbol;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t(
              "preSale.mintAddSaleDetail.获取代币简称错误请检查网络"
            ),
          });
        });
      tokenContract
        .balanceOf(this.ContractAddr)
        .then((balance) => {
          this.tokenParams.tokenBalacne = balance;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t(
              "preSale.mintAddSaleDetail.获取代币数量错误请检查网络"
            ),
          });
        });
    },
    getEcoParams(contract) {
      contract
        .start()
        .then((start) => {
          this.ecoParams.start = start;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t(
              "preSale.mintAddSaleDetail.获取开启预售失败请检查网络"
            ),
          });
        });
      contract
        .enableDonate()
        .then((enableDonate) => {
          this.ecoParams.enableDonate = enableDonate;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t(
              "preSale.mintAddSaleDetail.获取捐赠模式失败请检查网络"
            ),
          });
        });
      contract
        .donateETHPart()
        .then((donateETHPart) => {
          this.ecoParams.donateETHPart = donateETHPart;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t(
              "preSale.mintAddSaleDetail.获取捐赠ETH比例失败请检查网络"
            ),
          });
        });
      contract
        .donateTokenPart()
        .then((donateTokenPart) => {
          this.ecoParams.donateTokenPart = donateTokenPart;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t(
              "preSale.mintAddSaleDetail.获取捐赠代币比例失败请检查网络"
            ),
          });
        });
      contract
        .donateETHAddr()
        .then((donateETHAddr) => {
          this.ecoParams.donateETHAddr = donateETHAddr;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t(
              "preSale.mintAddSaleDetail.获取接收ETH地址失败请检查网络"
            ),
          });
        });
      contract
        .donateTokenAddr()
        .then((donateTokenAddr) => {
          this.ecoParams.donateTokenAddr = donateTokenAddr;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t(
              "preSale.mintAddSaleDetail.获取接收代币地址失败请检查网络"
            ),
          });
        });
      contract
        .enableAddLP()
        .then((enableAddLP) => {
          this.ecoParams.enableAddLP = enableAddLP;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t(
              "preSale.mintAddSaleDetail.获取加池模式失败请检查网络"
            ),
          });
        });
      contract
        .addPart()
        .then((addPart) => {
          this.ecoParams.addPart = addPart;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t(
              "preSale.mintAddSaleDetail.获取加池模式失败请检查网络"
            ),
          });
        });
      contract
        ._swapRouter()
        .then((_swapRouter) => {
          this.ecoParams._swapRouter = _swapRouter;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t(
              "preSale.mintAddSaleDetail.获取交易所错误请检查网络"
            ),
          });
        });
      contract
        ._mainPair()
        .then((_mainPair) => {
          this.ecoParams._mainPair = _mainPair;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t(
              "preSale.mintAddSaleDetail.获取池子地址错误请检查网络"
            ),
          });
        });

      contract
        .price()
        .then((price) => {
          this.ecoParams.price = price;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t(
              "preSale.mintAddSaleDetail.获取每份价格失败请检查网络"
            ),
          });
        });
      contract
        .amountPerUnits()
        .then((amountPerUnits) => {
          this.ecoParams.amountPerUnits = amountPerUnits;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t(
              "preSale.mintAddSaleDetail.获取每份数量失败请检查网络"
            ),
          });
        });
      contract
        .mintLimit()
        .then((mintLimit) => {
          this.ecoParams.mintLimit = mintLimit;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t(
              "preSale.mintAddSaleDetail.获取最大MINT数量失败请检查网络"
            ),
          });
        });
      contract
        .accEachLimit()
        .then((accEachLimit) => {
          this.ecoParams.accEachLimit = accEachLimit;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t(
              "preSale.mintAddSaleDetail.获取单次最大MINT数量失败请检查网络"
            ),
          });
        });
      contract
        .accMintLimit()
        .then((accMintLimit) => {
          this.ecoParams.accMintLimit = accMintLimit;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t(
              "preSale.mintAddSaleDetail.获取单钱包最大MINT数量失败检查网络"
            ),
          });
        });
      contract
        .minted()
        .then((minted) => {
          this.ecoParams.minted = minted;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t(
              "preSale.mintAddSaleDetail.获取最大MINT数量失败请检查网络"
            ),
          });
        });
      contract
        .fundAddress()
        .then((fundAddress) => {
          this.ecoParams.fundAddress = fundAddress;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t(
              "preSale.mintAddSaleDetail.获取营销钱包错误请检查网络"
            ),
          });
        });
    },

    transferOwnership(temValue) {
      let isAddress = ethers.utils.isAddress(temValue);
      if (isAddress) {
        this.loading.changeOwner = true;
        const provider = store.state.user.provider;
        const signer = provider.getSigner();
        const preSaleContract = new ethers.Contract(
          this.ContractAddr,
          this.saleABI,
          signer
        );
        preSaleContract
          .transferOwnership(temValue)
          .then((rs) => {
            this.dialogVisible.changeOwnerVisible = false;
            this.loading.changeOwner = false;
            this.temValue = null;
            this.$message({
              type: "success",
              message: this.$t("preSale.mintAddSaleDetail.已提交等待区块确认"),
            });

            rs.wait()
              .then(() => {
                this.$message({
                  type: "success",
                  message: this.$t("preSale.mintAddSaleDetail.修改成功") + "!",
                });

                this.getBasicParams(preSaleContract);
                this.partShow = false;
              })
              .catch((error) => {
                console.log("错误!", error);
                this.$message({
                  type: "danger",
                  message: this.$t(
                    "preSale.mintAddSaleDetail.确认失败请前往浏览器查看"
                  ),
                });
              });
          })
          .catch((error) => {
            console.log("错误!", error);
            this.$message({
              type: "danger",
              message: this.$t("preSale.mintAddSaleDetail.出错了") + "!",
            });
            this.dialogVisible.changeOwnerVisible = false;
            this.loading.changeOwner = false;
            this.temValue = null;
          });
      } else {
        this.$message({
          type: "danger",
          message: this.$t("preSale.mintAddSaleDetail.地址格式不正确") + "!",
        });
        this.loading.changeOwner = false;
      }
    },
    renounceOwnership() {
      this.loading.renounceOwner = true;
      const provider = store.state.user.provider;
      const signer = provider.getSigner();
      const preSaleContract = new ethers.Contract(
        this.ContractAddr,
        this.saleABI,
        signer
      );
      preSaleContract
        .renounceOwnership()
        .then((rs) => {
          this.$message({
            type: "success",
            message: this.$t("preSale.mintAddSaleDetail.已提交等待区块确认"),
          });

          rs.wait()
            .then(() => {
              this.$message({
                type: "success",
                message: this.$t("preSale.mintAddSaleDetail.修改成功") + "!",
              });
              this.loading.renounceOwner = false;
              this.getBasicParams(preSaleContract);
              this.partShow = false;
            })
            .catch((error) => {
              console.log("错误!", error);
              this.$message({
                type: "danger",
                message: this.$t(
                  "preSale.mintAddSaleDetail.确认失败请前往浏览器查看"
                ),
              });
            });
        })
        .catch((error) => {
          console.log("错误!", error);
          this.$message({
            type: "danger",
            message: this.$t("preSale.mintAddSaleDetail.出错了") + "!",
          });

          this.loading.renounceOwner = false;
          this.temValue = null;
        });
    },
    setAddLiquidityFee(temValue) {
      this.loading.changeAddLiquidity = true;
      const provider = store.state.user.provider;
      const signer = provider.getSigner();
      const preSaleContract = new ethers.Contract(
        this.ContractAddr,
        this.saleABI,
        signer
      );
      preSaleContract
        .setAddLiquidityFee(temValue * 100)
        .then((rs) => {
          this.dialogVisible.changeAddLiquidityVisible = false;
          this.loading.changeAddLiquidity = false;
          this.temValue = null;
          this.$message({
            type: "success",
            message: this.$t("preSale.mintAddSaleDetail.已提交等待区块确认"),
          });

          rs.wait()
            .then(() => {
              this.$message({
                type: "success",
                message: this.$t("preSale.mintAddSaleDetail.修改成功"),
              });

              this.getEcoParams(preSaleContract);
            })
            .catch((error) => {
              console.log("错误!", error);
              this.$message({
                type: "danger",
                message: this.$t(
                  "preSale.mintAddSaleDetail.确认失败请前往浏览器查看"
                ),
              });
            });
        })
        .catch((error) => {
          console.log("错误!", error);
          this.$message({
            type: "danger",
            message: this.$t("preSale.mintAddSaleDetail.出错了") + "!",
          });
          this.dialogVisible.changeAddLiquidityVisible = false;
          this.loading.changeAddLiquidity = false;
          this.temValue = null;
        });
    },

    launch() {
      this.loading.launch = true;
      const provider = store.state.user.provider;
      const signer = provider.getSigner();
      const preSaleContract = new ethers.Contract(
        this.ContractAddr,
        this.saleABI,
        signer
      );
      const tokenContract = new ethers.Contract(
        this.basicParams.tokenAddr,
        this.tokenABI,
        signer
      );
      const mintAmount = this.ecoParams.amountPerUnits.mul(
        this.ecoParams.mintLimit
      );
      tokenContract
        .approve(this.ContractAddr, mintAmount)
        .then((rs) => {
          this.$message({
            type: "success",
            message: this.$t("preSale.mintAddSaleDetail.正在授权请稍等"),
          });
          rs.wait()
            .then(() => {
              this.$message({
                type: "success",
                message: this.$t(
                  "preSale.mintAddSaleDetail.授权完成请确认开启预售"
                ),
              });

              preSaleContract
                .launch()
                .then((rs) => {
                  this.$message({
                    type: "success",
                    message: this.$t(
                      "preSale.mintAddSaleDetail.已提交等待区块确认"
                    ),
                  });

                  rs.wait()
                    .then(() => {
                      this.$message({
                        type: "success",
                        message:
                          this.$t("preSale.mintAddSaleDetail.开启预售成功") +
                          "!",
                      });
                      this.getEcoParams(preSaleContract);
                      this.getTokenParams(this.basicParams.tokenAddr);
                      this.loading.launch = false;
                    })
                    .catch((error) => {
                      console.log("错误!", error);
                      this.$message({
                        type: "danger",
                        message: this.$t(
                          "preSale.mintAddSaleDetail.确认失败请前往浏览器查看"
                        ),
                      });
                      this.loading.launch = false;
                    });
                })
                .catch((error) => {
                  console.log("错误!", error);
                  this.$message({
                    type: "danger",
                    message:
                      this.$t("preSale.mintAddSaleDetail.开启预售失败") + "!",
                  });
                  this.loading.launch = false;
                });
            })
            .catch((error) => {
              console.log("错误!", error);
              this.$message({
                type: "danger",
                message: this.$t(
                  "preSale.mintAddSaleDetail.确认失败请前往浏览器查看"
                ),
              });
              this.loading.launch = false;
            });
        })
        .catch((error) => {
          console.log("错误!", error);
          this.$message({
            type: "danger",
            message: this.$t("preSale.mintAddSaleDetail.授权失败") + "!!",
          });
          this.loading.launch = false;
          this.temValue = null;
        });
    },

    setFundAddress(temValue) {
      let isAddress = ethers.utils.isAddress(temValue);
      if (isAddress) {
        this.loading.changeFund = true;
        const provider = store.state.user.provider;
        const signer = provider.getSigner();
        const preSaleContract = new ethers.Contract(
          this.ContractAddr,
          this.saleABI,
          signer
        );
        preSaleContract
          .setFundAddress(temValue)
          .then((rs) => {
            this.dialogVisible.changeFundVisible = false;
            this.loading.changeFund = false;
            this.temValue = null;
            this.$message({
              type: "success",
              message: this.$t("preSale.mintAddSaleDetail.已提交等待区块确认"),
            });

            rs.wait()
              .then(() => {
                this.$message({
                  type: "success",
                  message: this.$t("preSale.mintAddSaleDetail.修改成功") + "!",
                });

                this.getEcoParams(preSaleContract);
              })
              .catch((error) => {
                console.log("错误!", error);
                this.$message({
                  type: "danger",
                  message: this.$t(
                    "preSale.mintAddSaleDetail.确认失败请前往浏览器查看"
                  ),
                });
              });
          })
          .catch((error) => {
            console.log("错误!", error);
            this.$message({
              type: "danger",
              message: this.$t("preSale.mintAddSaleDetail.出错了") + "!",
            });
            this.dialogVisible.changeFundVisible = false;
            this.loading.changeFund = false;
            this.temValue = null;
          });
      } else {
        this.$message({
          type: "danger",
          message: this.$t("preSale.mintAddSaleDetail.地址格式不正确") + "!",
        });
        this.loading.changeFund = false;
      }
    },
    getBigInt(floatNumber, tragetDecimals) {
      var floatArray = floatNumber.toString().split(".");
      let rsValue;
      if (floatArray.length == 1) {
        rsValue = BigNumber.from(floatArray[0]).mul(
          BigNumber.from(10).pow(tragetDecimals)
        );
      } else if (floatArray.length == 2) {
        var intPart = BigNumber.from(floatArray[0]).mul(
          BigNumber.from(10).pow(tragetDecimals)
        );
        var floatPart = BigNumber.from(floatArray[1]).mul(
          BigNumber.from(10).pow(tragetDecimals - floatArray[1].length)
        );
        rsValue = intPart.add(floatPart);
      }
      console.log(rsValue);
      return rsValue;
    },
    setClaims() {
      this.loading.setClaims = true;
      const provider = store.state.user.provider;
      const signer = provider.getSigner();
      const preSaleContract = new ethers.Contract(
        this.ContractAddr,
        this.saleABI,
        signer
      );
      const tokenContract = new ethers.Contract(
        this.basicParams.tokenAddr,
        this.tokenABI,
        signer
      );
      if (this.temBool) {
        provider.getBalance(this.ContractAddr).then((res) => {
          var bigValue = this.getBigInt(this.temValue, 18);
          if (res.gte(bigValue)) {
            preSaleContract
              .setClaims("******************************************", bigValue)
              .then((rs) => {
                this.dialogVisible.setClaims = false;

                this.temValue = null;
                this.$message({
                  type: "success",
                  message: this.$t(
                    "preSale.mintAddSaleDetail.已提交等待区块确认"
                  ),
                });

                rs.wait()
                  .then(() => {
                    this.$message({
                      type: "success",
                      message:
                        this.$t("preSale.mintAddSaleDetail.修改成功") + "!",
                    });
                    this.getTokenParams(this.basicParams.tokenAddr);
                    this.loading.setClaims = false;
                  })
                  .catch((error) => {
                    console.log("错误!", error);
                    this.$message({
                      type: "danger",
                      message: this.$t(
                        "preSale.mintAddSaleDetail.确认失败请前往浏览器查看"
                      ),
                    });
                  });
              })
              .catch((error) => {
                console.log("错误!", error);
                this.$message({
                  type: "danger",
                  message: this.$t("preSale.mintAddSaleDetail.修改失败") + "!",
                });
                this.loading.setClaims = false;
              });
          } else {
            this.$message({
              type: "danger",
              message:
                this.$t("preSale.mintAddSaleDetail.合约内余额不足") + "!",
            });
          }
        });
      } else {
        tokenContract.balanceOf(this.ContractAddr).then((res) => {
          var bigValue = this.getBigInt(
            this.temValue,
            this.tokenParams.decimals
          );
          if (res.gte(bigValue)) {
            preSaleContract
              .setClaims(this.basicParams.tokenAddr, bigValue)
              .then((rs) => {
                this.dialogVisible.setClaims = false;

                this.temValue = null;
                this.$message({
                  type: "success",
                  message: this.$t(
                    "preSale.mintAddSaleDetail.已提交等待区块确认"
                  ),
                });

                rs.wait()
                  .then(() => {
                    this.$message({
                      type: "success",
                      message:
                        this.$t("preSale.mintAddSaleDetail.修改成功") + "!",
                    });
                    this.getTokenParams(this.basicParams.tokenAddr);
                    this.loading.setClaims = false;
                  })
                  .catch((error) => {
                    console.log("错误!", error);
                    this.$message({
                      type: "danger",
                      message: this.$t(
                        "preSale.mintAddSaleDetail.确认失败请前往浏览器查看"
                      ),
                    });
                  });
              })
              .catch((error) => {
                console.log("错误!", error);
                this.$message({
                  type: "danger",
                  message: this.$t("preSale.mintAddSaleDetail.修改失败") + "!",
                });
                this.loading.setClaims = false;
              });
          } else {
            this.$message({
              type: "danger",
              message:
                this.$t("preSale.mintAddSaleDetail.合约内余额不足") + "!",
            });
            this.dialogVisible.setClaims = false;
            this.loading.setClaims = false;
            this.temValue = null;
          }
        });
      }
    },

    setPrice(temValue) {
      this.loading.setPrice = true;
      const provider = store.state.user.provider;
      const signer = provider.getSigner();
      const preSaleContract = new ethers.Contract(
        this.ContractAddr,
        this.saleABI,
        signer
      );
      var amountArray = temValue.toString().split(".");
      console.log("amountArray", amountArray);
      if (amountArray.length == 1) {
        let mintPrice = BigNumber.from(amountArray[0]).mul(
          BigNumber.from(10).pow(18)
        );
        console.log(mintPrice);
        preSaleContract
          .setPrice(mintPrice)
          .then((rs) => {
            this.dialogVisible.setPrice = false;
            this.loading.setPrice = false;
            this.temValue = null;
            this.$message({
              type: "success",
              message: this.$t("preSale.mintAddSaleDetail.已提交等待区块确认"),
            });

            rs.wait()
              .then(() => {
                this.$message({
                  type: "success",
                  message: this.$t("preSale.mintAddSaleDetail.修改成功") + "!",
                });

                this.getEcoParams(preSaleContract);
              })
              .catch((error) => {
                console.log("错误!", error);
                this.$message({
                  type: "danger",
                  message: this.$t(
                    "preSale.mintAddSaleDetail.确认失败请前往浏览器查看"
                  ),
                });
              });
          })
          .catch((error) => {
            console.log("错误!", error);
            this.$message({
              type: "danger",
              message: this.$t("preSale.mintAddSaleDetail.出错了") + "!",
            });
            this.dialogVisible.setPrice = false;
            this.loading.setPrice = false;
            this.temValue = null;
          });
      } else if (amountArray.length == 2) {
        var intPart = BigNumber.from(amountArray[0]).mul(
          BigNumber.from(10).pow(18)
        );
        var floatPart = BigNumber.from(amountArray[1]).mul(
          BigNumber.from(10).pow(18 - amountArray[1].length)
        );
        let mintPrice = intPart.add(floatPart);
        console.log(mintPrice);
        preSaleContract
          .setPrice(mintPrice)
          .then((rs) => {
            this.dialogVisible.setPrice = false;
            this.loading.setPrice = false;
            this.temValue = null;
            this.$message({
              type: "success",
              message: this.$t("preSale.mintAddSaleDetail.已提交等待区块确认"),
            });

            rs.wait()
              .then(() => {
                this.$message({
                  type: "success",
                  message: this.$t("preSale.mintAddSaleDetail.修改成功") + "!",
                });

                this.getEcoParams(preSaleContract);
              })
              .catch((error) => {
                console.log("错误!", error);
                this.$message({
                  type: "danger",
                  message: this.$t(
                    "preSale.mintAddSaleDetail.确认失败请前往浏览器查看"
                  ),
                });
              });
          })
          .catch((error) => {
            console.log("错误!", error);
            this.$message({
              type: "danger",
              message: this.$t("preSale.mintAddSaleDetail.出错了") + "!",
            });
            this.dialogVisible.setPrice = false;
            this.loading.setPrice = false;
            this.temValue = null;
          });
      } else {
        this.$message({
          type: "danger",
          message:
            this.$t("preSale.mintAddSaleDetail.请输入正确的数字格式") + "!",
        });
        this.loading.setPrice = false;
        this.temValue = null;
      }
    },
    setAmountPerUnits(temValue) {
      this.loading.setAmountPerUnits = true;
      const provider = store.state.user.provider;
      const signer = provider.getSigner();
      const preSaleContract = new ethers.Contract(
        this.ContractAddr,
        this.saleABI,
        signer
      );
      const bigUnitAmount = this.getBigInt(temValue, this.tokenParams.decimals);

      preSaleContract
        .setAmountPerUnits(bigUnitAmount)
        .then((rs) => {
          this.dialogVisible.setAmountPerUnits = false;
          this.loading.setAmountPerUnits = false;
          this.temValue = null;
          this.$message({
            type: "success",
            message: this.$t("preSale.mintAddSaleDetail.已提交等待区块确认"),
          });

          rs.wait()
            .then(() => {
              this.$message({
                type: "success",
                message: this.$t("preSale.mintAddSaleDetail.修改成功") + "!",
              });

              this.getEcoParams(preSaleContract);
            })
            .catch((error) => {
              console.log("错误!", error);
              this.$message({
                type: "danger",
                message: this.$t(
                  "preSale.mintAddSaleDetail.确认失败请前往浏览器查看"
                ),
              });
            });
        })
        .catch((error) => {
          console.log("错误!", error);
          this.$message({
            type: "danger",
            message: this.$t("preSale.mintAddSaleDetail.出错了") + "!",
          });
          this.dialogVisible.setAmountPerUnits = false;
          this.loading.setAmountPerUnits = false;
          this.temValue = null;
        });
    },
    setMintLimit(temValue) {
      if (temValue == 0) {
        this.$message({
          type: "danger",
          message: this.$t("preSale.mintAddSaleDetail.总份数不可设置为0") + "!",
        });
      } else {
        this.loading.setMintLimit = true;
        const provider = store.state.user.provider;
        const signer = provider.getSigner();
        const preSaleContract = new ethers.Contract(
          this.ContractAddr,
          this.saleABI,
          signer
        );

        preSaleContract
          .setMintLimit(temValue)
          .then((rs) => {
            this.dialogVisible.setMintLimit = false;
            this.loading.setMintLimit = false;
            this.temValue = null;
            this.$message({
              type: "success",
              message: this.$t("preSale.mintAddSaleDetail.已提交等待区块确认"),
            });

            rs.wait()
              .then(() => {
                this.$message({
                  type: "success",
                  message: this.$t("preSale.mintAddSaleDetail.修改成功") + "!",
                });

                this.getEcoParams(preSaleContract);
              })
              .catch((error) => {
                console.log("错误!", error);
                this.$message({
                  type: "danger",
                  message: this.$t(
                    "preSale.mintAddSaleDetail.确认失败请前往浏览器查看"
                  ),
                });
              });
          })
          .catch((error) => {
            console.log("错误!", error);
            this.$message({
              type: "danger",
              message: this.$t("preSale.mintAddSaleDetail.出错了") + "!",
            });
            this.dialogVisible.setMintLimit = false;
            this.loading.setMintLimit = false;
            this.temValue = null;
          });
      }
    },
    setAccMintLimit(temValue) {
      if (temValue == 0) {
        this.$message({
          type: "danger",
          message:
            this.$t("preSale.mintAddSaleDetail.单钱包Mint最大份数不可设置为0") +
            "!",
        });
      } else {
        this.loading.setAccMintLimit = true;
        const provider = store.state.user.provider;
        const signer = provider.getSigner();
        const preSaleContract = new ethers.Contract(
          this.ContractAddr,
          this.saleABI,
          signer
        );

        preSaleContract
          .setAccMintLimit(temValue)
          .then((rs) => {
            this.dialogVisible.setAccMintLimit = false;
            this.loading.setAccMintLimit = false;
            this.temValue = null;
            this.$message({
              type: "success",
              message: this.$t("preSale.mintAddSaleDetail.已提交等待区块确认"),
            });

            rs.wait()
              .then(() => {
                this.$message({
                  type: "success",
                  message: this.$t("preSale.mintAddSaleDetail.修改成功") + "!",
                });

                this.getEcoParams(preSaleContract);
              })
              .catch((error) => {
                console.log("错误!", error);
                this.$message({
                  type: "danger",
                  message: this.$t(
                    "preSale.mintAddSaleDetail.确认失败请前往浏览器查看"
                  ),
                });
              });
          })
          .catch((error) => {
            console.log("错误!", error);
            this.$message({
              type: "danger",
              message: this.$t("preSale.mintAddSaleDetail.出错了") + "!",
            });
            this.dialogVisible.setAccMintLimit = false;
            this.loading.setAccMintLimit = false;
            this.temValue = null;
          });
      }
    },
    setAccEachLimit(temValue) {
      if (temValue == 0) {
        this.$message({
          type: "danger",
          message:
            this.$t("preSale.mintAddSaleDetail.单次Mint最大份数不可设置为0") +
            "!",
        });
      } else {
        this.loading.setAccEachLimit = true;
        const provider = store.state.user.provider;
        const signer = provider.getSigner();
        const preSaleContract = new ethers.Contract(
          this.ContractAddr,
          this.saleABI,
          signer
        );

        preSaleContract
          .setAccEachLimit(temValue)
          .then((rs) => {
            this.dialogVisible.setAccEachLimit = false;
            this.loading.setAccEachLimit = false;
            this.temValue = null;
            this.$message({
              type: "success",
              message: this.$t("preSale.mintAddSaleDetail.已提交等待区块确认"),
            });

            rs.wait()
              .then(() => {
                this.$message({
                  type: "success",
                  message: this.$t("preSale.mintAddSaleDetail.修改成功") + "!",
                });

                this.getEcoParams(preSaleContract);
              })
              .catch((error) => {
                console.log("错误!", error);
                this.$message({
                  type: "danger",
                  message: this.$t(
                    "preSale.mintAddSaleDetail.确认失败请前往浏览器查看"
                  ),
                });
              });
          })
          .catch((error) => {
            console.log("错误!", error);
            this.$message({
              type: "danger",
              message: this.$t("preSale.mintAddSaleDetail.出错了") + "!",
            });
            this.dialogVisible.setAccEachLimit = false;
            this.loading.setAccEachLimit = false;
            this.temValue = null;
          });
      }
    },
    setAddPart(temPart) {
      if (temPart == 0 || temPart > 100) {
        this.$message({
          type: "danger",
          message: this.$t("preSale.mintAddSaleDetail.添加比例需在之间"),
        });
      } else {
        this.loading.setAddPart = true;
        const provider = store.state.user.provider;
        const signer = provider.getSigner();
        const preSaleContract = new ethers.Contract(
          this.ContractAddr,
          this.saleABI,
          signer
        );

        preSaleContract
          .setAddPart(temPart)
          .then((rs) => {
            this.dialogVisible.setAddPart = false;
            this.loading.setAddPart = false;
            this.temPart = null;
            this.$message({
              type: "success",
              message: this.$t("preSale.mintAddSaleDetail.已提交等待区块确认"),
            });

            rs.wait()
              .then(() => {
                this.$message({
                  type: "success",
                  message: this.$t("preSale.mintAddSaleDetail.修改成功") + "!",
                });

                this.getEcoParams(preSaleContract);
              })
              .catch((error) => {
                console.log("错误!", error);
                this.$message({
                  type: "danger",
                  message: this.$t(
                    "preSale.mintAddSaleDetail.确认失败请前往浏览器查看"
                  ),
                });
              });
          })
          .catch((error) => {
            console.log("错误!", error);
            this.$message({
              type: "danger",
              message: this.$t("preSale.mintAddSaleDetail.出错了") + "!",
            });
            this.dialogVisible.setAddPart = false;
            this.loading.setAddPart = false;
            this.temPart = null;
          });
      }
    },
    setDonateETHPart(temPart) {
      if (temPart < 0 || temPart > 100) {
        this.$message({
          type: "danger",
          message: this.$t("preSale.mintAddSaleDetail.捐赠比例需在之间"),
        });
      } else {
        this.loading.setDonateETHPart = true;
        const provider = store.state.user.provider;
        const signer = provider.getSigner();
        const preSaleContract = new ethers.Contract(
          this.ContractAddr,
          this.saleABI,
          signer
        );

        preSaleContract
          .setDonateETHPart(temPart)
          .then((rs) => {
            this.dialogVisible.setDonateETHPart = false;
            this.loading.setDonateETHPart = false;
            this.temPart = null;
            this.$message({
              type: "success",
              message: this.$t("preSale.mintAddSaleDetail.已提交等待区块确认"),
            });

            rs.wait()
              .then(() => {
                this.$message({
                  type: "success",
                  message: this.$t("preSale.mintAddSaleDetail.修改成功") + "!",
                });

                this.getEcoParams(preSaleContract);
              })
              .catch((error) => {
                console.log("错误!", error);
                this.$message({
                  type: "danger",
                  message: this.$t(
                    "preSale.mintAddSaleDetail.确认失败请前往浏览器查看"
                  ),
                });
              });
          })
          .catch((error) => {
            console.log("错误!", error);
            this.$message({
              type: "danger",
              message: this.$t("preSale.mintAddSaleDetail.出错了") + "!",
            });
            this.dialogVisible.setDonateETHPart = false;
            this.loading.setDonateETHPart = false;
            this.temPart = null;
          });
      }
    },
    setDonateTokenPart(temPart) {
      if (temPart == 0 || temPart > 100) {
        this.$message({
          type: "danger",
          message: this.$t("preSale.mintAddSaleDetail.捐赠比例需在之间"),
        });
      } else {
        this.loading.setDonateTokenPart = true;
        const provider = store.state.user.provider;
        const signer = provider.getSigner();
        const preSaleContract = new ethers.Contract(
          this.ContractAddr,
          this.saleABI,
          signer
        );

        preSaleContract
          .setDonateTokenPart(temPart)
          .then((rs) => {
            this.dialogVisible.setDonateTokenPart = false;
            this.loading.setDonateTokenPart = false;
            this.temPart = null;
            this.$message({
              type: "success",
              message: this.$t("preSale.mintAddSaleDetail.已提交等待区块确认"),
            });

            rs.wait()
              .then(() => {
                this.$message({
                  type: "success",
                  message: this.$t("preSale.mintAddSaleDetail.修改成功") + "!",
                });

                this.getEcoParams(preSaleContract);
              })
              .catch((error) => {
                console.log("错误!", error);
                this.$message({
                  type: "danger",
                  message: this.$t(
                    "preSale.mintAddSaleDetail.确认失败请前往浏览器查看"
                  ),
                });
              });
          })
          .catch((error) => {
            console.log("错误!", error);
            this.$message({
              type: "danger",
              message: this.$t("preSale.mintAddSaleDetail.出错了") + "!",
            });
            this.dialogVisible.setDonateTokenPart = false;
            this.loading.setDonateTokenPart = false;
            this.temPart = null;
          });
      }
    },
    setDonateETHAddress(temValue) {
      let isAddress = ethers.utils.isAddress(temValue);
      if (isAddress) {
        this.loading.setDonateETHAddress = true;
        const provider = store.state.user.provider;
        const signer = provider.getSigner();
        const preSaleContract = new ethers.Contract(
          this.ContractAddr,
          this.saleABI,
          signer
        );
        preSaleContract
          .setDonateETHAddress(temValue)
          .then((rs) => {
            this.dialogVisible.setDonateETHAddress = false;
            this.loading.setDonateETHAddress = false;
            this.temValue = null;
            this.$message({
              type: "success",
              message: this.$t("preSale.mintAddSaleDetail.已提交等待区块确认"),
            });

            rs.wait()
              .then(() => {
                this.$message({
                  type: "success",
                  message: this.$t("preSale.mintAddSaleDetail.修改成功") + "!",
                });

                this.getEcoParams(preSaleContract);
              })
              .catch((error) => {
                console.log("错误!", error);
                this.$message({
                  type: "danger",
                  message: this.$t(
                    "preSale.mintAddSaleDetail.确认失败请前往浏览器查看"
                  ),
                });
              });
          })
          .catch((error) => {
            console.log("错误!", error);
            this.$message({
              type: "danger",
              message: this.$t("preSale.mintAddSaleDetail.出错了") + "!",
            });
            this.dialogVisible.setDonateETHAddress = false;
            this.loading.setDonateETHAddress = false;
            this.temValue = null;
          });
      } else {
        this.$message({
          type: "danger",
          message: this.$t("preSale.mintAddSaleDetail.地址格式不正确") + "!",
        });
        this.loading.setDonateETHAddress = false;
      }
    },
    setDonateTokenAddress(temValue) {
      let isAddress = ethers.utils.isAddress(temValue);
      if (isAddress) {
        this.loading.setDonateTokenAddress = true;
        const provider = store.state.user.provider;
        const signer = provider.getSigner();
        const preSaleContract = new ethers.Contract(
          this.ContractAddr,
          this.saleABI,
          signer
        );
        preSaleContract
          .setDonateTokenAddress(temValue)
          .then((rs) => {
            this.dialogVisible.setDonateTokenAddress = false;
            this.loading.setDonateTokenAddress = false;
            this.temValue = null;
            this.$message({
              type: "success",
              message: this.$t("preSale.mintAddSaleDetail.已提交等待区块确认"),
            });

            rs.wait()
              .then(() => {
                this.$message({
                  type: "success",
                  message: this.$t("preSale.mintAddSaleDetail.修改成功"),
                });

                this.getEcoParams(preSaleContract);
              })
              .catch((error) => {
                console.log("错误!", error);
                this.$message({
                  type: "danger",
                  message: this.$t(
                    "preSale.mintAddSaleDetail.确认失败请前往浏览器查看"
                  ),
                });
              });
          })
          .catch((error) => {
            console.log("错误!", error);
            this.$message({
              type: "danger",
              message: this.$t("preSale.mintAddSaleDetail.出错了") + "!",
            });
            this.dialogVisible.setDonateTokenAddress = false;
            this.loading.setDonateTokenAddress = false;
            this.temValue = null;
          });
      } else {
        this.$message({
          type: "danger",
          message: this.$t("preSale.mintAddSaleDetail.地址格式不正确") + "!",
        });
        this.loading.setDonateTokenAddress = false;
      }
    },
    copy_str(text) {
      if (navigator.clipboard) {
        // clipboard api 复制
        navigator.clipboard.writeText(text);
      } else {
        var textarea = document.createElement("textarea");
        document.body.appendChild(textarea);
        // 隐藏此输入框
        textarea.style.position = "fixed";
        textarea.style.clip = "rect(0 0 0 0)";
        textarea.style.top = "10px";
        // 赋值
        textarea.value = text;
        // 选中
        textarea.select();
        // 复制
        document.execCommand("copy", true);
        // 移除输入框
        document.body.removeChild(textarea);
      }
      this.$message({
        type: "success",
        message: this.$t("preSale.mintAddSaleDetail.已复制") + "!",
      });
    },
  },
};
</script>

<style scoped>
@media screen and (max-width: 500px) {
  .main {
    text-align: center;
    margin-top: 5%;
    padding-left: 0;
    padding-right: 0;
  }
  .row {
    width: 100%;
    margin-bottom: 10px;
  }
  .box-card {
    margin-bottom: 20px;
    text-align: center;
    width: 100%;
  }
  .controlbutton {
    margin-bottom: 10px;
  }
}
@media screen and (min-width: 500px) {
  .header {
    text-align: center;
    margin-top: 0px;
    font-size: larger;
  }
  .main {
    /* display: flexbox;
    justify-content: center; */
    margin-top: 0%;
    padding-left: 5%;
    padding-right: 5%;
  }
  .row {
    width: 100%;
    margin-bottom: 10px;
  }

  .col {
    /* margin-left: 20px; */
    border-radius: 4px;
  }
  .box-card {
    margin-bottom: 20px;
    text-align: center;
    width: 100%;
  }
  .controlbutton {
    margin-bottom: 10px;
  }
}
</style>
