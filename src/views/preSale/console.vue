<template>
  <div class="app-container">
    <div class="inner">
      <el-header class="header">
        <p>{{$t('preSale.console.预售列表')}}</p>
      </el-header>
      <el-main class="main">
        <el-table
          :data="CoinList"
          :default-sort="{ prop: 'createdTime', order: 'descending' }"
          size="medium"
          class="table"
        >
          <el-table-column prop="coinAddress" :label="$t('preSale.console.合约地址')" width="210">
            <template slot-scope="scope"
              >{{ scope.row.coinAddress }}
              <i
                class="el-icon-copy-document"
                @click="copy_str(scope.row.coinAddress)"
              ></i
            ></template>
          </el-table-column>
          <el-table-column prop="name" :label="$t('preSale.console.预售名称')" width="150">
          </el-table-column>
          <el-table-column prop="owner" :label="$t('preSale.console.所有者')" width="210">
            <template slot-scope="scope"
              >{{ scope.row.owner }}
              <i
                class="el-icon-copy-document"
                @click="copy_str(scope.row.owner)"
              ></i
            ></template>
          </el-table-column>
          <el-table-column prop="createdTime" :label="$t('preSale.console.创建时间')" width="150">
            <template slot-scope="scope"
              >{{ getDate(scope.row.createdTime) }}
            </template>
          </el-table-column>
          <el-table-column width="120" :label="$t('preSale.console.操作')">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="primary"
                @click="enterConsole(scope.row)"
                >{{$t('preSale.console.进入控制台')}}</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </el-main>
    </div>
  </div>
</template>

<script>
import store from "@/store";
import Mode1 from "@/contracts/pandaMode1.json";
import chainParams from "@/contracts/presaleParams.json";
import standardData from "@/contracts/standardCoin.json";
const ethers = require("ethers");
// const AbiCoder = require('ethers.utils.AbiCoder')
export default {
  name: "Dashboard",
  data() {
    return {
      standardData,
      chainParams,
      Mode1,
      mintAddModeAddress: null,

      tokenAddress: null,
      store,
      CoinList: [],
      modeADDList: [],
    };
  },
  created() {
    let Inval = setInterval(() => {
      if (store.state.user.chainId && store.state.user.address) {
        window.clearInterval(Inval);
        const chainId = store.state.user.chainId;
        for (let i = 0; i < chainParams[chainId].length; i++) {
          if (i != 0) {
            this.mintAddModeAddress = chainParams[chainId][i].ModeAddress;
            console.log(
              "chainParams[chainId][1].ModeAddress",
              chainParams[chainId][1].ModeAddress
            );
            this.modeADDList.push(this.mintAddModeAddress);
          } else {
          }
        }

        this.getCoinList();
      }
    }, 1000);
  },
  computed: {
    isMobile() {
      if (window.screen.width < 500) {
        return true;
      } else {
        return false;
      }
    },
    getShortAddress(address) {
      return address.substring(0, 5) + "..." + address.substring(37);
    },
  },
  methods: {
    getCoinList() {
      const modeABI = this.Mode1;
      const modeADDList = this.modeADDList;
      const provider = store.state.user.provider;
      console.log("modeADDList", modeADDList);
      for (let i = 0; i < modeADDList.length; i++) {
        const ModeContract = new ethers.Contract(
          modeADDList[i],
          modeABI,
          provider
        );
        ModeContract.checkCreatedCoin(store.state.user.address)
          .then((result) => {
            const tokenABI = this.standardData.Coin1abi;
            this.getTableData(result, tokenABI);
          })
          .catch(() => {
            this.$message({
              type: "danger",
              message: this.$t("preSale.console.获取mint预售错误请检查网络"),
            });
          });
        // if (i == 0) {
        //   const ModeContract = new ethers.Contract(
        //     modeADDList[i],
        //     modeABI,
        //     provider
        //   );
        //   ModeContract.checkCreatedCoin(store.state.user.address)
        //     .then((result) => {
        //       const tokenABI = this.standardData.Coin1abi;
        //       this.getTableData(result, tokenABI);
        //     })
        //     .catch(() => {
        //       this.$message({
        //         type: "danger",
        //         message: "获取mint预售错误,请检查网络!",
        //       });
        //     });
        // } else {
        //   this.$message({
        //     type: "danger",
        //     message: "未知错误!",
        //   });
        // }
      }
    },

    async getTableData(result, tokenABI) {
      for (let i = 0; i < result.length; i++) {
        const tokenContract = new ethers.Contract(
          result[i]._tokenAddress,
          tokenABI,
          store.state.user.provider
        );
        let owner = await tokenContract.owner();
        let createdtime = result[i]._createdTime.toNumber();
        let temToken = {
          coinAddress: result[i]._tokenAddress,
          name: result[i]._name,
          symbol: result[i]._symbol,
          owner: owner,
          createdTime: createdtime,
        };
        this.CoinList.push(temToken);
      }
      console.log("final Table Data", this.CoinList);
    },

    enterConsole(row) {
      console.log("row", row);
      this.$router.push({
        path: "/presale/mintAddSaleDetail/",
        query: { coinAddress: row.coinAddress },
      });
    },
    getDate(timestamp) {
      var date = new Date(timestamp * 1000);
      var Y = date.getFullYear() + "-";
      var M =
        (date.getMonth() + 1 < 10
          ? "0" + (date.getMonth() + 1)
          : date.getMonth() + 1) + "-";
      var D = date.getDate() + " ";
      var h = date.getHours() + ":";
      var m = date.getMinutes();
      return Y + M + D + h + m;
    },

    copy_str(text) {
      if (navigator.clipboard) {
        // clipboard api 复制
        navigator.clipboard.writeText(text);
      } else {
        var textarea = document.createElement("textarea");
        document.body.appendChild(textarea);
        // 隐藏此输入框
        textarea.style.position = "fixed";
        textarea.style.clip = "rect(0 0 0 0)";
        textarea.style.top = "10px";
        // 赋值
        textarea.value = text;
        // 选中
        textarea.select();
        // 复制
        document.execCommand("copy", true);
        // 移除输入框
        document.body.removeChild(textarea);
      }
      this.$message({
        type: "success",
        message: this.$t('preSale.console.已复制')+"!",
      });
    },
  },
};
</script>

<style scoped>
@media screen and (max-width: 500px) {
  .main {
    text-align: center;
    margin-top: 5%;
    padding-left: 0;
    padding-right: 0;
  }
}
@media screen and (min-width: 500px) {
  .inner {
    background-color: #ffff;
    padding: 40px;
  }
  .header {
    text-align: center;
    margin-top: 20px;
    font-size: larger;
  }
  .main {
    /* display: flex;
  align-items:center; */
    text-align: center;
    margin-top: 2%;
    padding-left: 5%;
    padding-right: 5%;
  }
  .table {
    width: 100%;
    size: medium;
  }
}
</style>
