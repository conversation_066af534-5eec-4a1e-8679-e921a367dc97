<template>
  <div class="app-container">
    <div class="inner">
      <el-header class="header">
        <p>下周上线,敬请期待！</p>
      </el-header>
      <el-main class="main">
        <p>功能介绍:</p>
      </el-main>
    </div>
  </div>
</template>

<script>
import V1Json from "@/contracts/standardCoin.json";
import store from "@/store";
// import verifyERC20 from '@/contracts/verifyERC20.json'
// import { ftmVerify } from '@/api/verify'
const ethers = require("ethers");
export default {
  name: "Dashboard",
  data() {
    return {
      V1Json,
      // store,
      form: {
        _name: "",
        _symbol: "",
        _supply: "",
        _decimals: "",
        _Options: false,
        type: [],
        _tokenOwner: "",
        _FeeOptions: false,
        _txFee: "",
        _burnFee: "",
        _FeeAddress: "",
      },
    };
  },
  methods: {
    onSubmit() {
      const { abi, data } = this.V1Json;
      // console.log(abi)
      const bytecode = data.bytecode;
      // console.log(bytecode)
      // let provider = new ethers.providers.JsonRpcProvider('https://rpc.ankr.com/fantom_testnet/');
      const provider = new ethers.providers.Web3Provider(window.ethereum);
      const signer = provider.getSigner();
      console.log("signer", signer);
      // var privKey = '6f3b1224ad05ffbb921793acb08769bc8ec909f2ae406ae108abfaabbc724803';
      // let wallet = new ethers.Wallet(privKey,provider);
      this.DeployContract(abi, bytecode, signer);
    },
    async DeployContract(abi, bytecode, signer) {
      const _name = "pandatoolv1";
      const _symbol = "pdv1";
      const _decimals = 18;
      const _supply = 202302;
      const _txFee = 1;
      const _burnFee = 1;
      const _FeeAddress = "******************************************";
      const _tokenOwner = "******************************************";
      const service = "******************************************";
      const _value = ethers.BigNumber.from("1000000000000000000");
      const overrides = { value: _value };
      // 常见合约工厂实例
      const factory = new ethers.ContractFactory(abi, bytecode, signer);

      // 请注意，我们将 "Hello World" 作为参数传递给合约构造函数constructor
      const contract = await factory.deploy(
        _name,
        _symbol,
        _decimals,
        _supply,
        _txFee,
        _burnFee,
        _FeeAddress,
        _tokenOwner,
        service,
        overrides
      );

      console.log(contract.address);
      console.log(contract.deployTransaction.hash);

      // 合约还没有部署;我们必须等到它被挖出
      // await contract.deployed()
    },

    onCancel() {
      var abiCoder = new ethers.utils.AbiCoder();
      let bytesdata = abiCoder.encode(
        ["string[]", "address[]", "uint256[]", "bool[]"],
        [["asd", "dsa"], [], [18, 1221212], []]
      );
      console.log("encodeABI", bytesdata);
      console.log(
        "000000000000000000000000000000000000000000000000000000000000008000000000000000000000000000000000000000000000000000000000000001600000000000000000000000000000000000000000000000000000000000000180000000000000000000000000000000000000000000000000000000000000034000000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000040000000000000000000000000000000000000000000000000000000000000008000000000000000000000000000000000000000000000000000000000000000036173640000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000364736100000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000d00000000000000000000000000000000000000000000000000000000000000120000000000000000000000000000000000000000000000000de0b6b3a7640000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000"
      );
      //   console.log('chainId',store.state.user.chainID)
      //   console.log('address',store.state.user.chainName)
    },
  },
};
</script>

<style scoped>
@media screen and (max-width: 500px) {
  .main {
    text-align: center;
    padding-left: 0;
    padding-right: 0;
  }
}
@media screen and (min-width: 500px) {
  .header {
    text-align: center;
    margin-top: 20px;
    font-size: 30px;
  }
  .main {
    font-size: 24px;
    text-align: center;
    padding-left: 30%;
    padding-right: 30%;
  }
}
</style>
