<template>
  <div class="app-container">
    <div class="inner">
      <el-header class="header">
        <p>
          {{$t("coinRelease.common.代币详情")}}
          <i
            class="el-icon-copy-document"
            style="
              margin-left: 10px;
              font-size: 12px;
              font-weight: 400;
              color: #409eff;
            "
            @click="copy_str(url)"
            >{{$t("coinRelease.common.复制链接")}}</i
          >
        </p>
      </el-header>
      <el-main class="main">
        <el-row :gutter="24">
          <el-col :xs="24" :sm="18">
            <el-card shadow="always" class="box-card">
              <div slot="header" class="clearfix">
                <span>{{$t('coinRelease.common.基本信息')}}</span>
              </div>
              <el-descriptions size="medium" :column="isMobile ? 1 : 2" border>
                <el-descriptions-item :label="$t('coinRelease.common.全称')">{{
                  basicParams._name
                }}</el-descriptions-item>
                <el-descriptions-item :label="$t('coinRelease.common.简称')">{{
                  basicParams._symbol
                }}</el-descriptions-item>
                <el-descriptions-item :label="$t('coinRelease.common.精度')">{{
                  basicParams._decimals
                }}</el-descriptions-item>
                <el-descriptions-item :label="$t('coinRelease.common.总量')">{{
                  getSupply
                }}</el-descriptions-item>
                <el-descriptions-item
                  :label="$t('coinRelease.common.代币模版')"
                  >{{ modeType }}</el-descriptions-item
                >
                <el-descriptions-item :label="$t('coinRelease.common.所有者')">
                  {{ getOwner }}
                  <i
                    class="el-icon-copy-document"
                    @click="copy_str(basicParams._owner)"
                  ></i>
                </el-descriptions-item>
                <el-descriptions-item :label="$t('coinRelease.common.合约地址')">
                  {{ getContract }}
                  <i
                    class="el-icon-copy-document"
                    @click="copy_str(tokenAddress)"
                  ></i>
                </el-descriptions-item>
              </el-descriptions>
            </el-card>

            <el-card shadow="always" class="box-card">
              <div slot="header" class="clearfix">
                <span>{{ $t("coinRelease.common.经济模型") }}</span>
              </div>
              <el-descriptions
                size="medium"
                :column="1"
                border
                :contentStyle="CS"
                :label-style="LS"
              >
                <el-descriptions-item :label="$t('coinRelease.common.交易所')">
                  {{ getSwap }}
                  <i
                    class="el-icon-copy-document"
                    @click="copy_str(ecoParams._swapRouter)"
                  ></i>
                </el-descriptions-item>
                <el-descriptions-item :label="$t('coinRelease.common.池子地址')">
                  {{ getPool }}
                  <i
                    class="el-icon-copy-document"
                    @click="copy_str(ecoParams._mainPair)"
                  ></i>
                </el-descriptions-item>
                <el-descriptions-item
                  :label="$t('coinRelease.common.底池代币')"
                >
                  {{ getCurrency }}
                  <i
                    class="el-icon-copy-document"
                    @click="copy_str(ecoParams._currency)"
                  ></i>
                </el-descriptions-item>
                <el-descriptions-item :label="$t('coinRelease.common.分红代币')">
                  {{ getRewardToken }}
                  <i
                    class="el-icon-copy-document"
                    @click="copy_str(ecoParams._rewardToken)"
                  ></i>
                </el-descriptions-item>
                <el-descriptions-item style="max-width: 300;" :label="$t('coinRelease.common.买入税率')"
                  >{{ getBuyFee }}% ({{ $t("coinRelease.common.营销")
                  }}{{ ecoParams._buyFundFee }}% +
                  {{ $t("coinRelease.common.销毁")
                  }}{{ ecoParams.buy_burnFee }}% + {{$t('coinRelease.common.分红')}}{{
                    ecoParams._buyRewardFee
                  }}% + {{ $t("coinRelease.common.回流")
                  }}{{ ecoParams._buyLPFee }}% + {{$t('coinRelease.common.推荐')}}{{
                    ecoParams._inviterFee
                  }}%)
                </el-descriptions-item>
                <el-descriptions-item :label="$t('coinRelease.common.卖出税率')"
                  >{{ getSellFee }}% ({{ $t("coinRelease.common.营销")
                  }}{{ ecoParams._sellFundFee }}% +
                  {{ $t("coinRelease.common.销毁")
                  }}{{ ecoParams.sell_burnFee }}% + {{$t('coinRelease.common.分红')}}{{
                    ecoParams._sellRewardFee
                  }}% + {{ $t("coinRelease.common.回流")
                  }}{{ ecoParams._sellLPFee }}% + {{$t('coinRelease.common.推荐')}}{{
                    ecoParams._inviterFee
                  }}%)
                </el-descriptions-item>
                <el-descriptions-item :label="$t('coinRelease.common.推荐税率')"
                  >{{ ecoParams._inviterFee }} %</el-descriptions-item
                >
                <el-descriptions-item :label="$t('coinRelease.common.推荐奖励')"
                  >{{ ecoParams.generations }}{{ $t("coinRelease.common.代奖励") }}
                  <p>{{ $t("coinRelease.common.一代比例") }}{{ ecoParams.fristRate }}%</p>
                  <p>{{ $t("coinRelease.common.二代比例") }}{{ ecoParams.secondRate }}%</p>
                  <p>{{ $t("coinRelease.common.三代比例") }}{{ ecoParams.thirdRate }}%</p>
                  <p>{{ $t("coinRelease.common.其他各代比例") }}{{ ecoParams.leftRate }}%</p>
                </el-descriptions-item>
                <el-descriptions-item :label="$t('coinRelease.common.最小转账数量')"
                  >{{ getMinTransAmount }}
                  <p
                    style="
                      font-size: 12px;
                      font-weight: 300;
                      margin-top: -5px;
                      margin-bottom: -10px;
                      max-width: 400px
                    "
                  >
                  {{ $t('coinRelease.common.注绑定推荐关系的最小数量回转确认后绑定成功默认为0') }}
                  </p>
                </el-descriptions-item>
                <el-descriptions-item :label="$t('coinRelease.common.撤池税率')"
                  >{{ ecoParams.removeLiquidityFee }}%</el-descriptions-item
                >
                <el-descriptions-item
                  :label="$t('coinRelease.common.营销钱包')"
                >
                  {{ getFund }}
                  <i
                    class="el-icon-copy-document"
                    @click="copy_str(ecoParams.fundAddress)"
                  ></i>
                </el-descriptions-item>
                <el-descriptions-item :label="$t('coinRelease.common.杀抢跑机器人区块')">{{
                  getkb
                }}</el-descriptions-item>

                <el-descriptions-item :label="$t('coinRelease.common.交易空投地址数')">{{
                  getAirdropNumbs
                }}</el-descriptions-item>
              </el-descriptions>
            </el-card>
          </el-col>
          <el-col :xs="24" :sm="6">
            <el-card v-show="partShow" shadow="always" class="box-card">
              <div slot="header" class="clearfix">
                <span>{{ $t("coinRelease.common.权限控制") }}</span>
              </div>
              <el-button
                size="mini"
                plain
                @click="dialogVisible.changeOwnerVisible = true"
                >{{$t('coinRelease.common.转让所有权')}}</el-button
              >
              <el-dialog
                :title="$t('coinRelease.common.转让所有权')"
                :visible.sync="dialogVisible.changeOwnerVisible"
                width="30%"
                center
              >
                <div>
                  <p>{{$t('coinRelease.common.转让地址')}}</p>
                  <el-input v-model="temValue" placeholder="0x..." />
                </div>

                <span slot="footer" class="dialog-footer">
                  <el-button
                    size="mini"
                    plain
                    @click="dialogVisible.changeOwnerVisible = false"
                    >{{$t('coinRelease.common.取消')}}</el-button
                  >
                  <el-button
                    size="mini"
                    plain
                    v-if="loading.changeOwner"
                    type="primary"
                    :loading="true"
                    >{{$t('coinRelease.common.确定')}}</el-button
                  >
                  <el-button
                    size="mini"
                    plain
                    v-else
                    type="primary"
                    @click="transferOwnership(temValue)"
                    >{{$t('coinRelease.common.确定')}}</el-button
                  >
                </span>
              </el-dialog>
              <el-button
                size="mini"
                v-if="loading.renounceOwner"
                :loading="true"
                plain
                >{{ $t("coinRelease.common.丢弃权限") }}</el-button
              >
              <el-button
                size="mini"
                v-else
                plain
                @click="renounceOwnership()"
                >{{ $t("coinRelease.common.丢弃权限") }}</el-button
              >
            </el-card>
            <el-card v-show="partShow" shadow="always" class="box-card">
              <div slot="header" class="clearfix">
                <span>{{$t('coinRelease.common.流动性控制')}}</span>
              </div>
              <el-button
                size="mini"
                plain
                @click="dialogVisible.changeRemoveLiquidityVisible = true"
                >{{$t('coinRelease.common.设置撤池税率')}}</el-button
              >
              <el-dialog
                :title="$t('coinRelease.common.设置撤池税率')"
                :visible.sync="dialogVisible.changeRemoveLiquidityVisible"
                width="30%"
                center
              >
                <div>
                  <p>{{$t('coinRelease.common.撤池税率最大为25')}}</p>
                  <el-input
                    v-model="temValue"
                    @keyup.native="temValue = checkDecimal(temValue)"
                    placeholder="0.01"
                  >
                    <template slot="append">%</template>
                  </el-input>
                </div>

                <span slot="footer" class="dialog-footer">
                  <el-button
                    size="mini"
                    plain
                    @click="dialogVisible.changeRemoveLiquidityVisible = false"
                    >{{$t('coinRelease.common.取消')}}</el-button
                  >
                  <el-button
                    size="mini"
                    plain
                    v-if="loading.changeRemoveLiquidity"
                    type="primary"
                    :loading="true"
                    >{{$t('coinRelease.common.确定')}}</el-button
                  >
                  <el-button
                    size="mini"
                    plain
                    v-else
                    type="primary"
                    @click="setRemoveLiquidityFee(temValue)"
                    >{{$t('coinRelease.common.确定')}}</el-button
                  >
                </span>
              </el-dialog>
            </el-card>
            <el-card v-show="partShow" shadow="always" class="box-card">
              <div slot="header" class="clearfix">
                <span>{{ $t("coinRelease.common.交易控制") }}</span>
              </div>
              <!-- <div class="controlbutton">
                <el-button
                  size="mini"
                  v-if="loading.startLP"
                  :loading="true"
                  plain
                  >允许用户添加流动性</el-button
                >
                <el-button
                  size="mini"
                  v-else-if="!enable.enableOffTrade || enable.startLPBlock > 0"
                  plain
                  disabled
                  >允许用户添加流动性</el-button
                >
                <el-button size="mini" v-else plain @click="startLP()"
                  >允许用户添加流动性</el-button
                >
              </div> -->
              <div class="controlbutton">
                <el-button
                  size="mini"
                  v-if="loading.launch"
                  :loading="true"
                  plain
                  >{{ $t("coinRelease.common.开启交易") }}</el-button
                >
                <el-button
                  size="mini"
                  v-else-if="
                    !enable.enableOffTrade || enable.startTradeBlock > 0
                  "
                  plain
                  disabled
                  >{{ $t("coinRelease.common.开启交易") }}</el-button
                >
                <el-button size="mini" v-else plain @click="launch()"
                  >{{ $t("coinRelease.common.开启交易") }}</el-button
                >
              </div>

              <div class="controlbutton">
                <el-button
                  size="mini"
                  v-if="!enable.enableRewardList"
                  plain
                  disabled
                  >{{$t('coinRelease.common.设置黑名单')}}</el-button
                >
                <el-button
                  size="mini"
                  v-else
                  plain
                  @click="dialogVisible.multi_bclistVisible = true"
                  >{{$t('coinRelease.common.设置黑名单')}}</el-button
                >
                <el-dialog
                  :title="$t('coinRelease.common.设置黑名单')"
                  :visible.sync="dialogVisible.multi_bclistVisible"
                  width="35%"
                  center
                >
                  <el-radio-group v-model="temBool">
                    <el-radio :label="true">{{$t('coinRelease.common.添加黑名单')}}</el-radio>
                    <el-radio :label="false">{{$t('coinRelease.common.移除黑名单')}}</el-radio>
                  </el-radio-group>
                  <div style="margin-top: 30px">
                    <p>{{$t('coinRelease.common.黑名单地址')}}</p>
                    <el-input
                      v-model="temValue"
                      type="textarea"
                      :rows="5"
                      :placeholder="$t('coinRelease.common.请输入地址每行一个地址不要添加任何符号')"
                    ></el-input>
                  </div>

                  <span slot="footer" class="dialog-footer">
                    <el-button
                      size="mini"
                      @click="dialogVisible.multi_bclistVisible = false"
                      >{{$t('coinRelease.common.取消')}}</el-button
                    >
                    <el-button
                      size="mini"
                      v-if="loading.multi_bclist"
                      :loading="true"
                      type="primary"
                      plain
                      >{{$t('coinRelease.common.确定')}}</el-button
                    >
                    <el-button
                      size="mini"
                      v-else
                      type="primary"
                      plain
                      @click="multi_bclist(temValue, temBool)"
                      >{{$t('coinRelease.common.确定')}}</el-button
                    >
                  </span>
                </el-dialog>
              </div>
              <div class="controlbutton">
                <el-button
                  size="mini"
                  v-if="!enable.enableKillBlock"
                  plain
                  disabled
                  >{{$t('coinRelease.common.杀开盘抢跑机器人')}}</el-button
                >
                <el-button
                  size="mini"
                  v-else
                  plain
                  @click="dialogVisible.setkbVisible = true"
                  >{{$t('coinRelease.common.杀开盘抢跑机器人')}}</el-button
                >
                <el-dialog
                  :title="$t('coinRelease.common.杀开盘抢跑机器人')"
                  :visible.sync="dialogVisible.setkbVisible"
                  width="30%"
                  center
                >
                  <div>
                    <p>{{$t('coinRelease.common.抢跑区块')}}</p>
                    <el-input
                      v-model="temValue"
                      oninput="value=value.replace(/[^\d]/g,'')"
                      placeholder="0"
                    />
                    <p>
                      {{$t('coinRelease.common.注需手动开启交易开启交易的前X个区块内交易的地址被视为抢跑机器人自动加入黑名单')}}
                    </p>
                  </div>

                  <span slot="footer" class="dialog-footer">
                    <el-button
                      size="mini"
                      plain
                      @click="dialogVisible.setkbVisible = false"
                      >{{$t('coinRelease.common.取消')}}</el-button
                    >
                    <el-button
                      size="mini"
                      v-if="loading.setkb"
                      :loading="true"
                      type="primary"
                      plain
                      >{{$t('coinRelease.common.确定')}}</el-button
                    >
                    <el-button
                      size="mini"
                      v-else
                      type="primary"
                      plain
                      @click="setkb(temValue)"
                      >{{$t('coinRelease.common.确定')}}</el-button
                    >
                  </span>
                </el-dialog>
              </div>

              <!-- <div class="controlbutton">
                <el-button
                  size="mini"
                  plain
                  @click="dialogVisible.setSwapPairListVisible = true"
                  >添加交易对</el-button
                >
                <el-dialog
                  title="添加交易对"
                  :visible.sync="dialogVisible.setSwapPairListVisible"
                  width="30%"
                  center
                >
                  <div>
                    <p>交易对地址</p>
                    <el-input v-model="temValue" placeholder="0x..." />
                    <p>
                      注:如果您的代币在不同的交易所上架, 或存在不同的交易对,
                      请将对应的流动池地址添加进来,
                      否则税率、分红等机制将在其他交易对上不起作用!
                    </p>
                  </div>

                  <span slot="footer" class="dialog-footer">
                    <el-button
                      size="mini"
                      plain
                      @click="dialogVisible.setSwapPairListVisible = false"
                      >{{$t('coinRelease.common.取消')}}</el-button
                    >
                    <el-button
                      size="mini"
                      v-if="loading.setSwapPairList"
                      :loading="true"
                      type="primary"
                      plain
                      >{{$t('coinRelease.common.确定')}}</el-button
                    >
                    <el-button
                      size="mini"
                      v-else
                      type="primary"
                      plain
                      @click="setSwapPairList(temValue)"
                      >{{$t('coinRelease.common.确定')}}</el-button
                    >
                  </span>
                </el-dialog>
              </div> -->
            </el-card>
            <el-card v-show="partShow" shadow="always" class="box-card">
              <div slot="header" class="clearfix">
                <span>{{$t('coinRelease.common.税率控制')}}</span>
              </div>
              <div class="controlbutton">
                <el-button
                  size="mini"
                  v-if="!enable.enableChangeTax"
                  plain
                  disabled
                  >{{$t('coinRelease.common.修改税率')}}</el-button
                >
                <el-button
                  size="mini"
                  v-else
                  plain
                  @click="dialogVisible.changeExTaxVisible = true"
                  >{{$t('coinRelease.common.修改税率')}}</el-button
                >
                <el-dialog
                  :title="$t('coinRelease.common.修改税率')"
                  :visible.sync="dialogVisible.changeExTaxVisible"
                  width="30%"
                  center
                >
                  <div style="margin-top: 0px">
                    <p style="font-size: 16px">{{$t('coinRelease.common.买入税率最大25')}}</p>
                    <p> {{$t("coinRelease.common.买入回流税率")}}</p>
                    <el-input
                      size="small"
                      v-model="temTax._buyLPFee"
                      @keyup.native="
                        temTax._buyLPFee = checkDecimal(temTax._buyLPFee)
                      "
                      placeholder="0.01"
                    >
                      <template slot="append">%</template>
                    </el-input>

                    <p>{{ $t("coinRelease.common.买入营销税率") }}</p>
                    <el-input
                      size="small"
                      v-model="temTax._buyFundFee"
                      @keyup.native="
                        temTax._buyFundFee = checkDecimal(temTax._buyFundFee)
                      "
                      placeholder="0.01"
                    >
                      <template slot="append">%</template>
                    </el-input>
                    <p> {{$t("coinRelease.common.买入分红税率")}}</p>
                    <el-input
                      size="small"
                      v-model="temTax._buyRewardFee"
                      @keyup.native="
                        temTax._buyRewardFee = checkDecimal(
                          temTax._buyRewardFee
                        )
                      "
                      placeholder="0.01"
                    >
                      <template slot="append">%</template>
                    </el-input>
                    <p>{{ $t("coinRelease.common.买入销毁税率") }}</p>
                    <el-input
                      size="small"
                      v-model="temTax.buy_burnFee"
                      @keyup.native="
                        temTax.buy_burnFee = checkDecimal(temTax.buy_burnFee)
                      "
                      placeholder="0.01"
                    >
                      <template slot="append">%</template>
                    </el-input>
                  </div>
                  <div style="margin-top: 20px">
                    <p style="font-size: 16px">{{$t('coinRelease.common.卖出税率最大25')}}</p>
                    <p> {{$t("coinRelease.common.回流税率")}}</p>
                    <el-input
                      size="small"
                      v-model="temTax._sellLPFee"
                      @keyup.native="
                        temTax._sellLPFee = checkDecimal(temTax._sellLPFee)
                      "
                      placeholder="0.01"
                    >
                      <template slot="append">%</template>
                    </el-input>
                    <p>{{ $t("coinRelease.common.营销税率") }}</p>
                    <el-input
                      size="small"
                      v-model="temTax._sellFundFee"
                      @keyup.native="
                        temTax._sellFundFee = checkDecimal(temTax._sellFundFee)
                      "
                      placeholder="0.01"
                    >
                      <template slot="append">%</template>
                    </el-input>
                    <p> {{$t("coinRelease.common.分红税率")}}</p>
                    <el-input
                      size="small"
                      v-model="temTax._sellRewardFee"
                      @keyup.native="
                        temTax._sellRewardFee = checkDecimal(
                          temTax._sellRewardFee
                        )
                      "
                      placeholder="0.01"
                    >
                      <template slot="append">%</template>
                    </el-input>
                    <p>{{ $t("coinRelease.common.销毁税率") }}</p>
                    <el-input
                      size="small"
                      v-model="temTax.sell_burnFee"
                      @keyup.native="
                        temTax.sell_burnFee = checkDecimal(temTax.sell_burnFee)
                      "
                      placeholder="0.01"
                    >
                      <template slot="append">%</template>
                    </el-input>
                  </div>
                  <p>{{this.$t("coinRelease.common.买卖营销税之和必须大于0")}}</p>

                  <span slot="footer" class="dialog-footer">
                    <el-button
                      size="mini"
                      plain
                      @click="dialogVisible.changeExTaxVisible = false"
                      >{{$t('coinRelease.common.取消')}}</el-button
                    >
                    <el-button
                      size="mini"
                      v-if="loading.changeExTax"
                      :loading="true"
                      type="primary"
                      plain
                      >{{$t('coinRelease.common.确定')}}</el-button
                    >
                    <el-button
                      size="mini"
                      v-else
                      type="primary"
                      plain
                      @click="changeExTax(temTax)"
                      >{{$t('coinRelease.common.确定')}}</el-button
                    >
                  </span>
                </el-dialog>
              </div>
              <div class="controlbutton">
                <el-button
                  size="mini"
                  plain
                  @click="dialogVisible.changeWhiterVisible = true"
                  >{{$t('coinRelease.common.设置税率白名单')}}</el-button
                >
                <el-dialog
                  :title="$t('coinRelease.common.设置税率白名单')"
                  :visible.sync="dialogVisible.changeWhiterVisible"
                  width="35%"
                  center
                >
                  <el-radio-group v-model="temBool">
                    <el-radio :label="true">{{$t('coinRelease.common.添加税率白名单')}}</el-radio>
                    <el-radio :label="false">{{$t('coinRelease.common.移除税率白名单')}}</el-radio>
                  </el-radio-group>
                  <div style="margin-top: 30px">
                    <p>{{$t('coinRelease.common.税率白名单地址')}}</p>
                    <el-input
                      v-model="temValue"
                      type="textarea"
                      :rows="5"
                      :placeholder="$t('coinRelease.common.请输入地址每行一个地址不要添加任何符号')"
                    ></el-input>
                  </div>

                  <span slot="footer" class="dialog-footer">
                    <el-button
                      size="mini"
                      plain
                      @click="dialogVisible.changeWhiterVisible = false"
                      >{{$t('coinRelease.common.取消')}}</el-button
                    >
                    <el-button
                      size="mini"
                      v-if="loading.changeWhiter"
                      :loading="true"
                      type="primary"
                      plain
                      >{{$t('coinRelease.common.确定')}}</el-button
                    >
                    <el-button
                      size="mini"
                      v-else
                      type="primary"
                      plain
                      @click="setFeeWhiteList(temValue, temBool)"
                      >{{$t('coinRelease.common.确定')}}</el-button
                    >
                  </span>
                </el-dialog>
              </div>
              <div class="controlbutton">
                <el-button
                  size="mini"
                  plain
                  @click="dialogVisible.changeFundVisible = true"
                  >{{$t('coinRelease.common.修改营销钱包')}}</el-button
                >
                <el-dialog
                  :title="$t('coinRelease.common.修改营销钱包')"
                  :visible.sync="dialogVisible.changeFundVisible"
                  width="30%"
                  center
                >
                  <div>
                    <p>{{$t("coinRelease.common.营销钱包地址")}}</p>
                    <el-input v-model="temValue" placeholder="0x..." />
                  </div>

                  <span slot="footer" class="dialog-footer">
                    <el-button
                      size="mini"
                      plain
                      @click="dialogVisible.changeFundVisible = false"
                      >{{$t('coinRelease.common.取消')}}</el-button
                    >
                    <el-button
                      size="mini"
                      v-if="loading.changeFund"
                      :loading="true"
                      type="primary"
                      plain
                      >{{$t('coinRelease.common.确定')}}</el-button
                    >
                    <el-button
                      size="mini"
                      v-else
                      type="primary"
                      plain
                      @click="setFundAddress(temValue)"
                      >{{$t('coinRelease.common.确定')}}</el-button
                    >
                  </span>
                </el-dialog>
              </div>
            </el-card>
            <el-card v-show="partShow" shadow="always" class="box-card">
              <div slot="header" class="clearfix">
                <span>{{$t('coinRelease.common.分红控制')}}</span>
              </div>
              <div class="controlbutton">
                <el-button
                  size="mini"
                  v-if="loading.claimToken"
                  :loading="true"
                  plain
                  >{{$t('coinRelease.LPinviterDetail.提取合约内分红代币')}}</el-button
                >
                <el-button size="mini" v-else plain @click="claimToken()"
                  >{{$t('coinRelease.LPinviterDetail.提取合约内分红代币')}}</el-button
                >
              </div>
              <div class="controlbutton">
                <el-button
                  size="mini"
                  plain
                  @click="dialogVisible.setExcludeHolderVisible = true"
                  >{{$t('coinRelease.common.设置分红黑名单')}}</el-button
                >
                <el-dialog
                  :title="$t('coinRelease.common.设置分红黑名单')"
                  :visible.sync="dialogVisible.setExcludeHolderVisible"
                  width="30%"
                  center
                >
                  <div>
                    <p>{{$t('coinRelease.common.分红黑名单地址')}}</p>
                    <el-input v-model="temValue" placeholder="0x..." />
                    <p>{{$t('coinRelease.common.该地址将不会收到LP分红')}}</p>
                  </div>

                  <span slot="footer" class="dialog-footer">
                    <el-button
                      size="mini"
                      plain
                      @click="dialogVisible.setExcludeHolderVisible = false"
                      >{{$t('coinRelease.common.取消')}}</el-button
                    >
                    <el-button
                      size="mini"
                      v-if="loading.setExcludeHolder"
                      :loading="true"
                      type="primary"
                      plain
                      >{{$t('coinRelease.common.确定')}}</el-button
                    >
                    <el-button
                      size="mini"
                      v-else
                      type="primary"
                      plain
                      @click="setExcludeHolder(temValue)"
                      >{{$t('coinRelease.common.确定')}}</el-button
                    >
                  </span>
                </el-dialog>
              </div>
              <div class="controlbutton">
                <el-button
                  size="mini"
                  plain
                  @click="dialogVisible.setHolderRewardConditionVisible = true"
                  >{{$t('coinRelease.common.设置分红阈值')}}</el-button
                >
                <el-dialog
                  :title="$t('coinRelease.common.设置分红阈值')"
                  :visible.sync="dialogVisible.setHolderRewardConditionVisible"
                  width="30%"
                  center
                >
                  <div>
                    <p>{{$t('coinRelease.common.设置分红阈值')}}</p>
                    <el-input
                      v-model="temValue"
                      oninput="value=value.replace(/[^0-9.]/g,'')"
                      placeholder="0.1"
                    />
                    <p>
                      {{$t('coinRelease.common.分红阈值说明')}}
                    </p>
                  </div>

                  <span slot="footer" class="dialog-footer">
                    <el-button
                      size="mini"
                      plain
                      @click="
                        dialogVisible.setHolderRewardConditionVisible = false
                      "
                      >{{$t('coinRelease.common.取消')}}</el-button
                    >
                    <el-button
                      size="mini"
                      v-if="loading.setHolderRewardCondition"
                      :loading="true"
                      type="primary"
                      plain
                      >{{$t('coinRelease.common.确定')}}</el-button
                    >
                    <el-button
                      size="mini"
                      v-else
                      type="primary"
                      plain
                      @click="setHolderRewardCondition(temValue)"
                      >{{$t('coinRelease.common.确定')}}</el-button
                    >
                  </span>
                </el-dialog>
              </div>
              <!-- <div  class="controlbutton">
                        <el-button size="mini"  plain @click="dialogVisible.setProcessRewardWaitBlockVisible = true">设置分红间隔区块</el-button>
                            <el-dialog
                                title="设置分红间隔区块"
                                :visible.sync="dialogVisible.setProcessRewardWaitBlockVisible"
                                width="30%"
                                center>
                                <div>
                                    <p>分红间隔区块</p>
                                    <el-input v-model="temValue" oninput ="value=value.replace(/[^\d]/g,'')"  placeholder="3" />
                                    <p>注:每隔一段时间开启分红,例如BSC每个区块间隔3秒左右!此功能可避免高频交易中扣除过多用户手续费</p>
                                </div>

                                <span slot="footer" class="dialog-footer">
                                    <el-button size="mini" plain @click="dialogVisible.setProcessRewardWaitBlockVisible = false">{{$t('coinRelease.common.取消')}}</el-button>
                                    <el-button size="mini" v-if="loading.setProcessRewardWaitBlock" :loading="true" type="primary" plain >{{$t('coinRelease.common.确定')}}</el-button>
                                    <el-button size="mini" v-else type="primary" plain @click="setProcessRewardWaitBlock(temValue)">{{$t('coinRelease.common.确定')}}</el-button>
                                </span>
                            </el-dialog>
                    </div> -->
            </el-card>
            <el-card v-show="partShow" shadow="always" class="box-card">
              <div slot="header" class="clearfix">
                <span>{{$t('coinRelease.common.推荐奖励控制')}}</span>
              </div>
              <div class="controlbutton">
                <el-button
                  size="mini"
                  v-if="!enable.enableChangeTax"
                  plain
                  disabled
                  >{{$t('coinRelease.common.修改推荐税率')}}</el-button
                >
                <el-button
                  size="mini"
                  v-else
                  plain
                  @click="dialogVisible.changeInviterFeeVisible = true"
                  >{{$t('coinRelease.common.修改推荐税率')}}</el-button
                >
                <el-dialog
                  :title="$t('coinRelease.common.修改推荐税率')"
                  :visible.sync="dialogVisible.changeInviterFeeVisible"
                  width="30%"
                  center
                >
                  <div>
                    <p> {{$t("coinRelease.common.推荐税率")}}</p>
                    <el-input
                      v-model="temValue"
                      @keyup.native="temValue = checkDecimal(temValue)"
                      placeholder="0.01"
                    >
                      <template slot="append">%</template>
                    </el-input>
                    <p>
                      {{$t("coinRelease.common.推荐税率买卖相同需保证买入总税率不能超过25卖出总税率不能超过25")}}
                    </p>
                  </div>

                  <span slot="footer" class="dialog-footer">
                    <el-button
                      size="mini"
                      plain
                      @click="dialogVisible.changeInviterFeeVisible = false"
                      >{{$t('coinRelease.common.取消')}}</el-button
                    >
                    <el-button
                      size="mini"
                      v-if="loading.changeInviterFee"
                      :loading="true"
                      type="primary"
                      plain
                      >{{$t('coinRelease.common.确定')}}</el-button
                    >
                    <el-button
                      size="mini"
                      v-else
                      type="primary"
                      plain
                      @click="changeInviterFee(temValue)"
                      >{{$t('coinRelease.common.确定')}}</el-button
                    >
                  </span>
                </el-dialog>
              </div>
              <div class="controlbutton">
                <el-button
                  size="mini"
                  plain
                  @click="dialogVisible.changeInviterParamsVisible = true"
                  >{{$t('coinRelease.common.设置推荐奖励比例')}}</el-button
                >
                <el-dialog
                  :title="$t('coinRelease.common.设置推荐奖励比例')"
                  :visible.sync="dialogVisible.changeInviterParamsVisible"
                  width="30%"
                  center
                >
                  <div>
                    <p>{{$t('coinRelease.common.奖励代数')}}</p>
                    <el-input-number
                      v-model="temRate.generations"
                      :min="3"
                      :max="16"
                      size="small"
                    ></el-input-number>
                    <p>{{ $t("coinRelease.common.一代比例") }}</p>
                    <el-input
                      size="small"
                      v-model.number="temRate.fristRate"
                      oninput="value=value.replace(/[^\d]/g,'')"
                      placeholder="0"
                    >
                      <template slot="append">%</template>
                    </el-input>
                    <p>{{ $t("coinRelease.common.二代比例") }}</p>
                    <el-input
                      size="small"
                      v-model.number="temRate.secondRate"
                      oninput="value=value.replace(/[^\d]/g,'')"
                      placeholder="0"
                    >
                      <template slot="append">%</template>
                    </el-input>
                    <p>{{ $t("coinRelease.common.三代比例") }}</p>
                    <el-input
                      size="small"
                      v-model.number="temRate.thirdRate"
                      oninput="value=value.replace(/[^\d]/g,'')"
                      placeholder="0"
                    >
                      <template slot="append">%</template>
                    </el-input>
                    <p>{{ $t("coinRelease.common.其他各代比例") }}:</p>
                    <el-input size="small" v-model="temRate.leftRate" disabled>
                      <template slot="append">%</template>
                    </el-input>
                    <el-row> {{$t('coinRelease.common.总比例')}}:{{ getInviterFee }}% </el-row>
                    <p>{{$t('coinRelease.common.确认前请先核算确认总比例为100')}}</p>
                  </div>

                  <span slot="footer" class="dialog-footer">
                    <el-button size="mini" plain @click="getGenerationFee"
                      >{{$t('coinRelease.common.核算比例')}}</el-button
                    >
                    <el-button
                      size="mini"
                      plain
                      v-if="loading.changeInviterParams"
                      type="primary"
                      :loading="true"
                      >{{$t('coinRelease.common.确定修改')}}</el-button
                    >
                    <el-button
                      size="mini"
                      plain
                      v-else
                      type="primary"
                      @click="changeInviterParams(temRate)"
                      >{{$t('coinRelease.common.确定修改')}}</el-button
                    >
                  </span>
                </el-dialog>
              </div>
              <div class="controlbutton">
                <el-button
                  size="mini"
                  plain
                  @click="dialogVisible.setMinTransAmountVisible = true"
                  >{{$t('coinRelease.common.设置最小转账金额')}}</el-button
                >
                <el-dialog
                  :title="$t('coinRelease.common.设置最小转账金额')"
                  :visible.sync="dialogVisible.setMinTransAmountVisible"
                  width="30%"
                  center
                >
                  <div>
                    <p>{{$t('coinRelease.common.最小转账金额')}}</p>
                    <el-input
                      v-model="temValue"
                      oninput="value=value.replace(/[^0-9.]/g,'')"
                      placeholder="0.1"
                    />
                    <p>
                      {{$t('coinRelease.common.用于设定绑定推荐关系的最小转账金额如希望任意转账金额均可绑定推荐关系设置0即可')}}
                    </p>
                    <p>
                      {{$t('coinRelease.common.例设定最小转账金额为上级需转账至少给下级下级至少回转给上级转账成功即绑定')}}
                    </p>
                  </div>

                  <span slot="footer" class="dialog-footer">
                    <el-button
                      size="mini"
                      plain
                      @click="dialogVisible.setMinTransAmountVisible = false"
                      >{{$t('coinRelease.common.取消')}}</el-button
                    >
                    <el-button
                      size="mini"
                      v-if="loading.setMinTransAmount"
                      :loading="true"
                      type="primary"
                      plain
                      >{{$t('coinRelease.common.确定')}}</el-button
                    >
                    <el-button
                      size="mini"
                      v-else
                      type="primary"
                      plain
                      @click="setMinTransAmount(temValue)"
                      >{{$t('coinRelease.common.确定')}}</el-button
                    >
                  </span>
                </el-dialog>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </el-main>
    </div>
  </div>
</template>

<script>
import store from "@/store";
import chainParams from "@/contracts/coinReleaseParams.json";
import Mode1 from "@/contracts/pandaMode1.json";
import standardData from "@/contracts/standardCoin.json";
const { ethers, BigNumber } = require("ethers");
export default {
  name: "Dashboard",
  data() {
    return {
      standardData,
      chainParams,
      Mode1,
      ModeType: null,
      url: null,
      tokenAddress: null,
      tokenABI: null,
      temValue: null,
      temBool: true,
      temTax: {
        _buyFee: null,
        _buyFundFee: null,
        _buyLPFee: null,
        _buyRewardFee: null,
        buy_burnFee: null,
        _sellFundFee: null,
        _sellLPFee: null,
        _sellRewardFee: null,
        sell_burnFee: null,
      },
      temRate: {
        generations: null,
        fristRate: null,
        secondRate: null,
        thirdRate: null,
        leftRate: null,
      },
      basicParams: {
        _name: null,
        _symbol: null,
        _decimals: null,
        _supply: null,
        _owner: null,
      },
      ecoParams: {
        _swapRouter: null,
        _mainPair: null,
        _currency: null,
        _rewardToken: null,
        _buyFee: null,
        _buyFundFee: null,
        _buyLPFee: null,
        _buyRewardFee: null,
        buy_burnFee: null,
        _sellFundFee: null,
        _sellLPFee: null,
        _sellRewardFee: null,
        sell_burnFee: null,

        removeLiquidityFee: null,
        airdropNumbs: null,
        kb: null,
        fundAddress: null,

        _inviterFee: null,
        generations: null,
        fristRate: null,
        secondRate: null,
        thirdRate: null,
        leftRate: null,
        _minTransAmount: null,
      },
      modeType: null,
      partShow: true,
      dialogVisible: {
        changeOwnerVisible: false,

        changeRemoveLiquidityVisible: false,
        changeInviterFeeVisible: false,
        changeInviterParamsVisible: false,
        setMinTransAmountVisible: false,
        multi_bclistVisible: false,
        setkbVisible: false,

        setSwapPairListVisible: false,
        changeExTaxVisible: false,
        changeWhiterVisible: false,
        changeFundVisible: false,
        claimTokenVisible: false,
        setExcludeHolderVisible: false,
        setProcessRewardWaitBlockVisible: false,
        setHolderRewardConditionVisible: false,
      },
      loading: {
        changeOwner: false,
        renounceOwner: false,
        launch: false,
        startLP: false,
        changeInviterFee: false,
        changeInviterParams: false,
        setMinTransAmount: false,
        changeRemoveLiquidity: false,
        multi_bclist: false,
        setkb: false,

        setSwapPairList: false,
        changeExTax: false,
        changeWhiter: false,
        changeFund: false,
        claimToken: false,
        setExcludeHolder: false,
        setProcessRewardWaitBlock: false,
        setHolderRewardCondition: false,
      },
      enable: {
        enableOffTrade: true,
        startTradeBlock: null,
        startLPBlock: null,
        enableKillBlock: null,
        enableRewardList: null,
        enableChangeTax: null,
        airdropEnable: null,
      },
      rewardOptions: [],
      currencyOptions: [],
      swapOptions: [],

      CS: {
        "text-align": "left",
        "min-width": "120px",
        "word-break": "break-all",
      },
      LS: {
        "text-align": "center",
        height: "40px",
        "max-width": "70px",
        "word-break": "break-all",
      },
    };
  },

  created() {
    this.tokenAddress = this.$route.query.coinAddress;
    this.modeType = this.$route.query.modeType;
    this.url = window.location.href;
    this.tokenABI = standardData.Coin3abi;
    const provider = store.state.user.provider;
    const signer = provider.getSigner();

    const tokenContract = new ethers.Contract(
      this.tokenAddress,
      this.tokenABI,
      signer
    );
    this.getBasicParams(tokenContract);
    let Inval = setInterval(() => {
      if (this.basicParams._owner) {
        if (
          ethers.utils.getAddress(this.basicParams._owner) !=
          ethers.utils.getAddress(store.state.user.address)
        ) {
          this.partShow = false;
        }
      }
      if (store.state.user.chainId) {
        window.clearInterval(Inval);
        const chainId = store.state.user.chainId;

        const selectChainParams = chainParams[chainId][3];
        for (let i = 0; i < selectChainParams.rewardOptions.length; i++) {
          var temAddress = ethers.utils.getAddress(
            selectChainParams.rewardOptions[i].value
          );
          var temParam = {
            label: selectChainParams.rewardOptions[i].label,
            value: temAddress,
          };
          this.rewardOptions.push(temParam);
        }
        for (let i = 0; i < selectChainParams.currencyOptions.length; i++) {
          var temAddress = ethers.utils.getAddress(
            selectChainParams.currencyOptions[i].value
          );
          var temParam = {
            label: selectChainParams.currencyOptions[i].label,
            value: temAddress,
          };
          this.currencyOptions.push(temParam);
        }
        for (let i = 0; i < selectChainParams.swapOptions.length; i++) {
          var temAddress = ethers.utils.getAddress(
            selectChainParams.swapOptions[i].value
          );
          var temParam = {
            label: selectChainParams.swapOptions[i].label,
            value: temAddress,
          };
          this.swapOptions.push(temParam);
        }
      }
    }, 1000);
    this.getEcoParams(tokenContract);
    this.getEnableParams(tokenContract);
  },
  computed: {
    isMobile() {
      if (window.screen.width < 500) {
        return true;
      } else {
        return false;
      }
    },
    getBuyFee() {
      return (
        Number(this.ecoParams._buyLPFee) +
        Number(this.ecoParams._buyRewardFee) +
        Number(this.ecoParams._buyFundFee) +
        Number(this.ecoParams.buy_burnFee) +
        Number(this.ecoParams._inviterFee)
      );
    },
    getSellFee() {
      return (
        Number(this.ecoParams._sellLPFee) +
        Number(this.ecoParams._sellRewardFee) +
        Number(this.ecoParams._sellFundFee) +
        Number(this.ecoParams.sell_burnFee) +
        Number(this.ecoParams._inviterFee)
      );
    },
    getSupply() {
      if (this.basicParams._supply) {
        return parseFloat(
          new Number(this.basicParams._supply.toString()) /
            10 ** this.basicParams._decimals
        ).toFixed(6);
      } else {
        return null;
      }
    },
    getMinTransAmount() {
      if (this.ecoParams._minTransAmount) {
        return parseFloat(
          new Number(this.ecoParams._minTransAmount.toString()) /
            10 ** this.basicParams._decimals
        )
          .toFixed(6)
          .toString();
      } else {
        return null;
      }
    },
    getInviterFee() {
      return (
        Number(this.temRate.fristRate) +
        Number(this.temRate.secondRate) +
        Number(this.temRate.thirdRate) +
        Number(this.temRate.leftRate) * (this.temRate.generations - 3)
      );
    },
    getOwner() {
      if (this.basicParams._owner) {
        return (
          this.basicParams._owner.substring(0, 5) +
          "..." +
          this.basicParams._owner.substring(37)
        );
      } else {
        return null;
      }
    },
    getContract() {
      if (this.tokenAddress) {
        return (
          this.tokenAddress.substring(0, 5) +
          "..." +
          this.tokenAddress.substring(37)
        );
      } else {
        return null;
      }
    },
    getSwap() {
      if (this.ecoParams._swapRouter) {
        for (let i = 0; i < this.swapOptions.length; i++) {
          if (
            ethers.utils.getAddress(this.ecoParams._swapRouter) ==
            ethers.utils.getAddress(this.swapOptions[i].value)
          ) {
            return this.swapOptions[i].label;
          }
        }
        return (
          this.ecoParams._swapRouter.substring(0, 5) +
          "..." +
          this.ecoParams._swapRouter.substring(37)
        );
      } else {
        return null;
      }
    },
    getPool() {
      if (this.ecoParams._mainPair) {
        return (
          this.ecoParams._mainPair.substring(0, 5) +
          "..." +
          this.ecoParams._mainPair.substring(37)
        );
      } else {
        return null;
      }
    },
    getCurrency() {
      if (this.ecoParams._currency) {
        for (let i = 0; i < this.currencyOptions.length; i++) {
          if (
            ethers.utils.getAddress(this.ecoParams._currency) ==
            ethers.utils.getAddress(this.currencyOptions[i].value)
          ) {
            return this.currencyOptions[i].label;
          }
        }
        return (
          this.ecoParams._currency.substring(0, 5) +
          "..." +
          this.ecoParams._currency.substring(37)
        );
      } else {
        return null;
      }
    },
    getRewardToken() {
      if (this.ecoParams._rewardToken) {
        for (let i = 0; i < this.rewardOptions.length; i++) {
          if (
            ethers.utils.getAddress(this.ecoParams._rewardToken) ==
            ethers.utils.getAddress(this.rewardOptions[i].value)
          ) {
            return this.rewardOptions[i].label;
          }
        }
        return (
          this.ecoParams._rewardToken.substring(0, 5) +
          "..." +
          this.ecoParams._rewardToken.substring(37)
        );
      } else {
        return null;
      }
    },
    getFund() {
      if (this.ecoParams.fundAddress) {
        return (
          this.ecoParams.fundAddress.substring(0, 5) +
          "..." +
          this.ecoParams.fundAddress.substring(37)
        );
      } else {
        return null;
      }
    },
    getAirdropNumbs() {
      if (this.enable.airdropEnable) {
        if (this.ecoParams.airdropNumbs) {
          return this.ecoParams.airdropNumbs;
        } else {
          return this.$t('coinRelease.common.获取中');
        }
      } else {
        return this.$t('coinRelease.common.无上限');
      }
    },
    getkb() {
      if (this.enable.enableKillBlock) {
        if (this.ecoParams.kb) {
          return this.ecoParams.kb;
        } else {
          return this.$t('coinRelease.common.获取中');
        }
      } else {
        return this.$t('coinRelease.common.无上限');
      }
    },
  },

  methods: {
    getBasicParams(contract) {
      contract
        .owner()
        .then((owner) => {
          this.basicParams._owner = owner;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t('coinRelease.common.获取合约拥有者错误请检查网络'),
          });
        });

      contract
        .name()
        .then((name) => {
          this.basicParams._name = name;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t('coinRelease.common.获取名称错误请检查网络'),
          });
        });
      contract
        .symbol()
        .then((symbol) => {
          this.basicParams._symbol = symbol;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t('coinRelease.common.获取简称错误请检查网络'),
          });
        });
      contract
        .decimals()
        .then((decimals) => {
          this.basicParams._decimals = decimals;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t('coinRelease.common.获取精度错误请检查网络'),
          });
        });
      contract
        .totalSupply()
        .then((supply) => {
          console.log("supply", typeof supply);
          this.basicParams._supply = supply;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t('coinRelease.common.获取供应量错误请检查网络'),
          });
        });
    },
    getEcoParams(contract) {
      contract
        ._swapRouter()
        .then((_swapRouter) => {
          this.ecoParams._swapRouter = _swapRouter;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t('coinRelease.common.获取交易所错误请检查网络'),
          });
        });
      contract
        ._mainPair()
        .then((_mainPair) => {
          this.ecoParams._mainPair = _mainPair;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t('coinRelease.common.获取池子地址错误请检查网络'),
          });
        });
      contract
        .currency()
        .then((_currency) => {
          this.ecoParams._currency = _currency;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t('coinRelease.common.获取底池代币错误请检查网络'),
          });
        });
      contract
        .rewardToken()
        .then((_rewardToken) => {
          this.ecoParams._rewardToken = _rewardToken;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t('coinRelease.common.获取分红代币错误请检查网络'),
          });
        });
      contract
        ._buyFundFee()
        .then((_buyFundFee) => {
          this.ecoParams._buyFundFee = _buyFundFee / 100;
          this.temTax._buyFundFee = _buyFundFee / 100;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t('coinRelease.common.获取买入营销费率错误请检查网络'),
          });
        });
      contract
        ._buyLPFee()
        .then((_buyLPFee) => {
          this.ecoParams._buyLPFee = _buyLPFee / 100;
          this.temTax._buyLPFee = _buyLPFee / 100;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t('coinRelease.common.获取买入回流费率错误请检查网络'),
          });
        });
      contract
        ._buyRewardFee()
        .then((_buyRewardFee) => {
          this.ecoParams._buyRewardFee = _buyRewardFee / 100;
          this.temTax._buyRewardFee = _buyRewardFee / 100;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t('coinRelease.common.获取买入分红费率错误请检查网络'),
          });
        });
      contract
        .buy_burnFee()
        .then((buy_burnFee) => {
          this.ecoParams.buy_burnFee = buy_burnFee / 100;
          this.temTax.buy_burnFee = buy_burnFee / 100;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t('coinRelease.common.获取买入分红费率错误请检查网络'),
          });
        });
      contract
        ._sellFundFee()
        .then((_sellFundFee) => {
          this.ecoParams._sellFundFee = _sellFundFee / 100;
          this.temTax._sellFundFee = _sellFundFee / 100;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t('coinRelease.common.获取卖出营销费率错误请检查网络'),
          });
        });
      contract
        ._sellLPFee()
        .then((_sellLPFee) => {
          this.ecoParams._sellLPFee = _sellLPFee / 100;
          this.temTax._sellLPFee = _sellLPFee / 100;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t('coinRelease.common.获取卖出回流费率错误请检查网络'),
          });
        });

      contract
        ._sellRewardFee()
        .then((_sellRewardFee) => {
          this.ecoParams._sellRewardFee = _sellRewardFee / 100;
          this.temTax._sellRewardFee = _sellRewardFee / 100;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t('coinRelease.common.获取卖出分红费率错误请检查网络'),
          });
        });
      contract
        .sell_burnFee()
        .then((sell_burnFee) => {
          this.ecoParams.sell_burnFee = sell_burnFee / 100;
          this.temTax.sell_burnFee = sell_burnFee / 100;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t('coinRelease.common.获取卖出销毁费率错误请检查网络'),
          });
        });
      contract
        ._inviterFee()
        .then((_inviterFee) => {
          this.ecoParams._inviterFee = _inviterFee / 100;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t('coinRelease.common.获取邀请费率错误请检查网络'),
          });
        });
      contract
        .generations()
        .then((generations) => {
          this.ecoParams.generations = generations;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t('coinRelease.LPinviterDetail.获取邀请代数错误请检查网络'),
          });
        });
      contract
        .fristRate()
        .then((fristRate) => {
          this.ecoParams.fristRate = fristRate;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t('coinRelease.LPinviterDetail.获取fristRate错误请检查网络'),
          });
        });
      contract
        .secondRate()
        .then((secondRate) => {
          this.ecoParams.secondRate = secondRate;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t('coinRelease.LPinviterDetail.获取secondRate错误请检查网络'),
          });
        });
      contract
        .thirdRate()
        .then((thirdRate) => {
          this.ecoParams.thirdRate = thirdRate;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t('coinRelease.LPinviterDetail.获取thirdRate错误请检查网络'),
          });
        });
      contract
        .leftRate()
        .then((leftRate) => {
          this.ecoParams.leftRate = leftRate;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t('coinRelease.LPinviterDetail.获取leftRate错误请检查网络'),
          });
        });
      contract
        ._minTransAmount()
        .then((_minTransAmount) => {
          this.ecoParams._minTransAmount = _minTransAmount;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t('coinRelease.LPinviterDetail.获取_minTransAmount错误请检查网络'),
          });
        });
      contract
        .removeLiquidityFee()
        .then((removeLiquidityFee) => {
          this.ecoParams.removeLiquidityFee = removeLiquidityFee / 100;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t('coinRelease.common.获取撤池费率错误请检查网络'),
          });
        });
      contract
        .airdropNumbs()
        .then((airdropNumbs) => {
          this.ecoParams.airdropNumbs = airdropNumbs;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message:this.$t('coinRelease.common.获取空投数量错误请检查网络'),
          });
        });
      contract
        .kb()
        .then((kb) => {
          this.ecoParams.kb = kb;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t('coinRelease.common.获取杀机器人区块错误请检查网络'),
          });
        });
      contract
        .fundAddress()
        .then((fundAddress) => {
          this.ecoParams.fundAddress = fundAddress;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t('coinRelease.common.获取营销钱包错误请检查网络'),
          });
        });
    },
    getEnableParams(contract) {
      contract
        .enableOffTrade()
        .then((enableOffTrade) => {
          this.enable.enableOffTrade = enableOffTrade;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t('coinRelease.common.获取手动交易开关错误请检查网络'),
          });
        });
      contract
        .startTradeBlock()
        .then((startTradeBlock) => {
          this.enable.startTradeBlock = startTradeBlock;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t('coinRelease.common.获取startTradeBlock错误请检查网络'),
          });
        });
      contract
        .startLPBlock()
        .then((startLPBlock) => {
          this.enable.startLPBlock = startLPBlock;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t('coinRelease.common.获取startLPBlock错误请检查网络'),
          });
        });
      contract
        .enableKillBlock()
        .then((enableKillBlock) => {
          this.enable.enableKillBlock = enableKillBlock;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t('coinRelease.LPinviterDetail.获取杀机器人错误请检查网络'),
        
          });
        });
      contract
        .enableRewardList()
        .then((enableRewardList) => {
          this.enable.enableRewardList = enableRewardList;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t('coinRelease.common.获取黑名单开关错误请检查网络'),
          });
        });
      contract
        .enableChangeTax()
        .then((enableChangeTax) => {
          this.enable.enableChangeTax = enableChangeTax;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t('coinRelease.common.获取税率开关错误请检查网络'),
          });
        });
      contract
        .airdropEnable()
        .then((airdropEnable) => {
          this.enable.airdropEnable = airdropEnable;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t('coinRelease.common.获取空投开关错误请检查网络'),
          });
        });
    },

    transferOwnership(temValue) {
      let isAddress = ethers.utils.isAddress(temValue);
      if (isAddress) {
        this.loading.changeOwner = true;
        const provider = store.state.user.provider;
        const signer = provider.getSigner();
        const tokenContract = new ethers.Contract(
          this.tokenAddress,
          this.tokenABI,
          signer
        );
        tokenContract
          .transferOwnership(temValue)
          .then((rs) => {
            this.dialogVisible.changeOwnerVisible = false;
            this.loading.changeOwner = false;
            this.temValue = null;
            this.$message({
              type: "success",
              message: this.$t('coinRelease.common.已提交等待区块确认'),
            });

            rs.wait()
              .then(() => {
                this.$message({
                  type: "success",
                  message: this.$t('coinRelease.common.修改成功'),
                });

                this.getBasicParams(tokenContract);
                this.partShow = false;
              })
              .catch((error) => {
                console.log("错误!", error);
                this.$message({
                  type: "danger",
                  message: this.$t('coinRelease.common.确认失败请前往浏览器查看'),
                });
              });
          })
          .catch((error) => {
            console.log("错误!", error);
            this.$message({
              type: "danger",
              message: this.$t('coinRelease.common.出错了'),
            });
            this.dialogVisible.changeOwnerVisible = false;
            this.loading.changeOwner = false;
            this.temValue = null;
          });
      } else {
        this.$message({
          type: "danger",
          message: this.$t('coinRelease.common.地址格式不正确'),
        });
        this.loading.changeOwner = false;
      }
    },
    renounceOwnership() {
      this.loading.renounceOwner = true;
      const provider = store.state.user.provider;
      const signer = provider.getSigner();
      const tokenContract = new ethers.Contract(
        this.tokenAddress,
        this.tokenABI,
        signer
      );
      tokenContract
        .renounceOwnership()
        .then((rs) => {
          this.$message({
            type: "success",
            message: this.$t('coinRelease.common.已提交等待区块确认'),
          });

          rs.wait()
            .then(() => {
              this.$message({
                type: "success",
                message: this.$t('coinRelease.common.修改成功'),
              });
              this.loading.renounceOwner = false;
              this.getBasicParams(tokenContract);
              this.partShow = false;
            })
            .catch((error) => {
              console.log("错误!", error);
              this.$message({
                type: "danger",
                message: this.$t('coinRelease.common.确认失败请前往浏览器查看'),
              });
            });
        })
        .catch((error) => {
          console.log("错误!", error);
          this.$message({
            type: "danger",
            message: this.$t('coinRelease.common.出错了'),
          });

          this.loading.renounceOwner = false;
          this.temValue = null;
        });
    },

    setRemoveLiquidityFee(temValue) {
      this.loading.changeRemoveLiquidity = true;
      const provider = store.state.user.provider;
      const signer = provider.getSigner();
      const tokenContract = new ethers.Contract(
        this.tokenAddress,
        this.tokenABI,
        signer
      );
      tokenContract
        .setRemoveLiquidityFee(temValue * 100)
        .then((rs) => {
          this.dialogVisible.changeRemoveLiquidityVisible = false;
          this.loading.changeRemoveLiquidity = false;
          this.temValue = null;
          this.$message({
            type: "success",
            message: this.$t('coinRelease.common.已提交等待区块确认'),
          });

          rs.wait()
            .then(() => {
              this.$message({
                type: "success",
                message: this.$t('coinRelease.common.修改成功'),
              });

              this.getEcoParams(tokenContract);
            })
            .catch((error) => {
              console.log("错误!", error);
              this.$message({
                type: "danger",
                message: this.$t('coinRelease.common.确认失败请前往浏览器查看'),
              });
            });
        })
        .catch((error) => {
          console.log("错误!", error);
          this.$message({
            type: "danger",
            message: this.$t('coinRelease.common.出错了'),
          });
          this.dialogVisible.changeRemoveLiquidityVisible = false;
          this.loading.changeRemoveLiquidity = false;
          this.temValue = null;
        });
    },
    launch() {
      this.loading.launch = true;
      const provider = store.state.user.provider;
      const signer = provider.getSigner();
      const tokenContract = new ethers.Contract(
        this.tokenAddress,
        this.tokenABI,
        signer
      );
      tokenContract
        .launch()
        .then((rs) => {
          this.$message({
            type: "success",
            message: this.$t('coinRelease.common.已提交等待区块确认'),
          });

          rs.wait()
            .then(() => {
              this.$message({
                type: "success",
                message: this.$t('coinRelease.common.修改成功'),
              });

              this.loading.launch = false;
              this.enable.startTradeBlock = 1;
            })
            .catch((error) => {
              console.log("错误!", error);
              this.$message({
                type: "danger",
                message: this.$t('coinRelease.common.确认失败请前往浏览器查看'),
              });
            });
        })
        .catch((error) => {
          console.log("错误!", error);
          this.$message({
            type: "danger",
            message: this.$t('coinRelease.common.出错了'),
          });
          this.loading.launch = false;
          this.temValue = null;
        });
    },
    startLP() {
      this.loading.startLP = true;
      const provider = store.state.user.provider;
      const signer = provider.getSigner();
      const tokenContract = new ethers.Contract(
        this.tokenAddress,
        this.tokenABI,
        signer
      );
      tokenContract
        .startLP()
        .then((rs) => {
          this.$message({
            type: "success",
            message: this.$t('coinRelease.common.已提交等待区块确认'),
          });

          rs.wait()
            .then(() => {
              this.$message({
                type: "success",
                message: this.$t('coinRelease.common.修改成功'),
              });

              this.loading.startLP = false;
              this.enable.startLPBlock = 1;
            })
            .catch((error) => {
              console.log("错误!", error);
              this.$message({
                type: "danger",
                message: this.$t('coinRelease.common.确认失败请前往浏览器查看'),
              });
            });
        })
        .catch((error) => {
          console.log("错误!", error);
          this.$message({
            type: "danger",
            message: this.$t('coinRelease.common.出错了'),
          });
          this.loading.startLP = false;
        });
    },

    multi_bclist(temValue, temBool) {
      this.loading.multi_bclist = true;
      const provider = store.state.user.provider;
      const signer = provider.getSigner();
      const tokenContract = new ethers.Contract(
        this.tokenAddress,
        this.tokenABI,
        signer
      );
      const addressList = temValue.split("\n");
      console.log(addressList);
      tokenContract
        .multi_bclist(addressList, temBool)
        .then((rs) => {
          this.dialogVisible.multi_bclistVisible = false;
          this.loading.multi_bclist = false;
          this.temValue = null;
          this.$message({
            type: "success",
            message: this.$t('coinRelease.common.已提交等待区块确认'),
          });

          rs.wait()
            .then(() => {
              this.$message({
                type: "success",
                message: this.$t('coinRelease.common.修改成功'),
              });

              this.getEcoParams(tokenContract);
            })
            .catch((error) => {
              console.log("错误!", error);
              this.$message({
                type: "danger",
                message: this.$t('coinRelease.common.确认失败请前往浏览器查看'),
              });
            });
        })
        .catch((error) => {
          console.log("错误!", error);
          this.$message({
            type: "danger",
            message: this.$t('coinRelease.common.出错了'),
          });
          this.dialogVisible.multi_bclistVisible = false;
          this.loading.multi_bclist = false;
          this.temValue = null;
        });
    },
    setkb(temValue) {
      this.loading.setkb = true;
      const provider = store.state.user.provider;
      const signer = provider.getSigner();
      const tokenContract = new ethers.Contract(
        this.tokenAddress,
        this.tokenABI,
        signer
      );
      tokenContract
        .setkb(Number(temValue))
        .then((rs) => {
          this.dialogVisible.setkbVisible = false;
          this.loading.setkb = false;
          this.temValue = null;
          this.$message({
            type: "success",
            message: this.$t('coinRelease.common.已提交等待区块确认'),
          });

          rs.wait()
            .then(() => {
              this.$message({
                type: "success",
                message: this.$t('coinRelease.common.修改成功'),
              });

              this.getEcoParams(tokenContract);
            })
            .catch((error) => {
              console.log("错误!", error);
              this.$message({
                type: "danger",
                message: this.$t('coinRelease.common.确认失败请前往浏览器查看'),
              });
            });
        })
        .catch((error) => {
          console.log("错误!", error);
          this.$message({
            type: "danger",
            message: this.$t('coinRelease.common.出错了'),
          });
          this.dialogVisible.setkbVisible = false;
          this.loading.setkb = false;
          this.temValue = null;
        });
    },

    setSwapPairList(temValue) {
      let isAddress = ethers.utils.isAddress(temValue);
      if (isAddress) {
        this.loading.setSwapPairList = true;
        const provider = store.state.user.provider;
        const signer = provider.getSigner();
        const tokenContract = new ethers.Contract(
          this.tokenAddress,
          this.tokenABI,
          signer
        );
        tokenContract
          .setSwapPairList(temValue, true)
          .then((rs) => {
            this.dialogVisible.setSwapPairListVisible = false;
            this.loading.setSwapPairList = false;
            this.temValue = null;
            this.$message({
              type: "success",
              message: this.$t('coinRelease.common.已提交等待区块确认'),
            });

            rs.wait()
              .then(() => {
                this.$message({
                  type: "success",
                  message: this.$t('coinRelease.common.修改成功'),
                });

                this.getEcoParams(tokenContract);
              })
              .catch((error) => {
                console.log("错误!", error);
                this.$message({
                  type: "danger",
                  message: this.$t('coinRelease.common.确认失败请前往浏览器查看'),
                });
              });
          })
          .catch((error) => {
            console.log("错误!", error);
            this.$message({
              type: "danger",
              message: this.$t('coinRelease.common.出错了'),
            });
            this.dialogVisible.setSwapPairListVisible = false;
            this.loading.setSwapPairList = false;
            this.temValue = null;
          });
      } else {
        this.$message({
          type: "danger",
          message: this.$t('coinRelease.common.地址格式不正确'),
        });
        this.loading.setSwapPairList = false;
      }
    },
    changeExTax(temTax) {
      const temTaxList = [
        Number(temTax._buyFundFee) * 100,
        Number(temTax._buyLPFee) * 100,
        Number(temTax._buyRewardFee) * 100,
        Number(temTax.buy_burnFee) * 100,
        Number(temTax._sellFundFee) * 100,
        Number(temTax._sellLPFee) * 100,
        Number(temTax._sellRewardFee) * 100,
        Number(temTax.sell_burnFee) * 100,
        Number(this.ecoParams._inviterFee) * 100,
      ];
      this.loading.changeExTax = true;
      const provider = store.state.user.provider;
      const signer = provider.getSigner();
      const tokenContract = new ethers.Contract(
        this.tokenAddress,
        this.tokenABI,
        signer
      );

      tokenContract
        .completeCustoms(temTaxList)
        .then((rs) => {
          this.dialogVisible.changeExTaxVisible = false;
          this.loading.changeExTax = false;
          this.$message({
            type: "success",
            message: this.$t('coinRelease.common.已提交等待区块确认'),
          });

          rs.wait()
            .then(() => {
              this.$message({
                type: "success",
                message: this.$t('coinRelease.common.修改成功'),
              });

              this.getEcoParams(tokenContract);
            })
            .catch((error) => {
              console.log("错误!", error);
              this.$message({
                type: "danger",
                message: this.$t('coinRelease.common.确认失败请前往浏览器查看'),
              });
            });
        })
        .catch((error) => {
          console.log("错误!", error);
          this.$message({
            type: "danger",
            message: this.$t('coinRelease.common.出错了'),
          });
          this.dialogVisible.changeExTaxVisible = false;
          this.loading.changeExTax = false;
        });
    },
    changeInviterFee(temValue) {
      const temTaxList = [
        Number(this.ecoParams._buyFundFee) * 100,
        Number(this.ecoParams._buyLPFee) * 100,
        Number(this.ecoParams._buyRewardFee) * 100,
        Number(this.ecoParams.buy_burnFee) * 100,
        Number(this.ecoParams._sellFundFee) * 100,
        Number(this.ecoParams._sellLPFee) * 100,
        Number(this.ecoParams._sellRewardFee) * 100,
        Number(this.ecoParams.sell_burnFee) * 100,
        Number(temValue) * 100,
      ];
      this.loading.changeInviterFee = true;
      const provider = store.state.user.provider;
      const signer = provider.getSigner();
      const tokenContract = new ethers.Contract(
        this.tokenAddress,
        this.tokenABI,
        signer
      );

      tokenContract
        .completeCustoms(temTaxList)
        .then((rs) => {
          this.dialogVisible.changeInviterFeeVisible = false;
          this.loading.changeInviterFee = false;
          this.$message({
            type: "success",
            message: this.$t('coinRelease.common.已提交等待区块确认'),
          });

          rs.wait()
            .then(() => {
              this.$message({
                type: "success",
                message: this.$t('coinRelease.common.修改成功'),
              });

              this.getEcoParams(tokenContract);
            })
            .catch((error) => {
              console.log("错误!", error);
              this.$message({
                type: "danger",
                message: this.$t('coinRelease.common.确认失败请前往浏览器查看'),
              });
            });
        })
        .catch((error) => {
          console.log("错误!", error);
          this.$message({
            type: "danger",
            message: this.$t('coinRelease.common.出错了'),
          });
          this.dialogVisible.changeInviterFeeVisible = false;
          this.loading.changeInviterFee = false;
        });
    },
    changeInviterParams() {
      var totalInviterRate =
        Number(this.temRate.fristRate) +
        Number(this.temRate.secondRate) +
        Number(this.temRate.thirdRate) +
        Number(this.temRate.leftRate) * (this.temRate.generations - 3);
      if (totalInviterRate != 100) {
        this.$message({
          type: "danger",
          message: this.$t("coinRelease.common.邀请奖励比例错误"),
        });
      } else {
        const temRateList = [
          Number(this.temRate.generations),
          Number(this.temRate.fristRate),
          Number(this.temRate.secondRate),
          Number(this.temRate.thirdRate),
          Number(this.temRate.leftRate),
        ];
        this.loading.changeInviterParams = true;
        const provider = store.state.user.provider;
        const signer = provider.getSigner();
        const tokenContract = new ethers.Contract(
          this.tokenAddress,
          this.tokenABI,
          signer
        );

        tokenContract
          .changeInviteRate(temRateList)
          .then((rs) => {
            this.dialogVisible.changeInviterParamsVisible = false;
            this.loading.changeInviterParams = false;
            this.$message({
              type: "success",
              message: this.$t('coinRelease.common.已提交等待区块确认'),
            });

            rs.wait()
              .then(() => {
                this.$message({
                  type: "success",
                  message: this.$t('coinRelease.common.修改成功'),
                });

                this.getEcoParams(tokenContract);
              })
              .catch((error) => {
                console.log("错误!", error);
                this.$message({
                  type: "danger",
                  message: this.$t('coinRelease.common.确认失败请前往浏览器查看'),
                });
              });
          })
          .catch((error) => {
            console.log("错误!", error);
            this.$message({
              type: "danger",
              message: this.$t('coinRelease.common.出错了'),
            });
            this.dialogVisible.changeInviterParamsVisible = false;
            this.loading.changeInviterParams = false;
          });
      }
    },
    setFeeWhiteList(temValue, temBool) {
      this.loading.changeWhiter = true;
      const provider = store.state.user.provider;
      const signer = provider.getSigner();
      const tokenContract = new ethers.Contract(
        this.tokenAddress,
        this.tokenABI,
        signer
      );
      const addressList = temValue.split("\n");
      tokenContract
        .setFeeWhiteList(addressList, temBool)
        .then((rs) => {
          this.dialogVisible.changeWhiterVisible = false;
          this.loading.changeWhiter = false;
          this.temValue = null;
          this.$message({
            type: "success",
            message: this.$t('coinRelease.common.已提交等待区块确认'),
          });

          rs.wait()
            .then(() => {
              this.$message({
                type: "success",
                message: this.$t('coinRelease.common.修改成功'),
              });

              this.getEcoParams(tokenContract);
            })
            .catch((error) => {
              console.log("错误!", error);
              this.$message({
                type: "danger",
                message: this.$t('coinRelease.common.确认失败请前往浏览器查看'),
              });
            });
        })
        .catch((error) => {
          console.log("错误!", error);
          this.$message({
            type: "danger",
            message: this.$t('coinRelease.common.出错了'),
          });
          this.dialogVisible.changeWhiterVisible = false;
          this.loading.changeWhiter = false;
          this.temValue = null;
        });
    },
    setFundAddress(temValue) {
      let isAddress = ethers.utils.isAddress(temValue);
      if (isAddress) {
        this.loading.changeFund = true;
        const provider = store.state.user.provider;
        const signer = provider.getSigner();
        const tokenContract = new ethers.Contract(
          this.tokenAddress,
          this.tokenABI,
          signer
        );
        tokenContract
          .setFundAddress(temValue)
          .then((rs) => {
            this.dialogVisible.changeFundVisible = false;
            this.loading.changeFund = false;
            this.temValue = null;
            this.$message({
              type: "success",
              message: this.$t('coinRelease.common.已提交等待区块确认'),
            });

            rs.wait()
              .then(() => {
                this.$message({
                  type: "success",
                  message: this.$t('coinRelease.common.修改成功'),
                });

                this.getEcoParams(tokenContract);
              })
              .catch((error) => {
                console.log("错误!", error);
                this.$message({
                  type: "danger",
                  message: this.$t('coinRelease.common.确认失败请前往浏览器查看'),
                });
              });
          })
          .catch((error) => {
            console.log("错误!", error);
            this.$message({
              type: "danger",
              message: this.$t('coinRelease.common.出错了'),
            });
            this.dialogVisible.changeFundVisible = false;
            this.loading.changeFund = false;
            this.temValue = null;
          });
      } else {
        this.$message({
          type: "danger",
          message: this.$t('coinRelease.common.地址格式不正确'),
        });
        this.loading.changeFund = false;
      }
    },

    claimToken() {
      this.loading.claimToken = true;
      const provider = store.state.user.provider;
      const signer = provider.getSigner();
      const tokenContract = new ethers.Contract(
        this.tokenAddress,
        this.tokenABI,
        signer
      );
      const rewardTokenContract = new ethers.Contract(
        this.ecoParams._rewardToken,
        this.tokenABI,
        signer
      );
      rewardTokenContract
        .balanceOf(this.tokenAddress)
        .then((balance) => {
          if (balance > 0) {
            tokenContract
              .claimToken(
                this.ecoParams._rewardToken,
                balance,
                this.basicParams._owner
              )
              .then((rs) => {
                this.$message({
                  type: "success",
                  message: this.$t('coinRelease.common.已提交等待区块确认'),
                });

                rs.wait()
                  .then(() => {
                    this.$message({
                      type: "success",
                      message: this.$t('coinRelease.common.修改成功'),
                    });

                    this.loading.claimToken = false;
                  })
                  .catch((error) => {
                    console.log("错误!", error);
                    this.$message({
                      type: "danger",
                      message: this.$t('coinRelease.common.确认失败请前往浏览器查看'),
                    });
                  });
              })
              .catch((error) => {
                console.log("错误!", error);
                this.$message({
                  type: "danger",
                  message: this.$t('coinRelease.common.出错了'),
                });
                this.loading.claimToken = false;
              });
          } else {
            this.$message({
              type: "info",
              message: his.$t('coinRelease.LPinviterDetail.余额为0'),
            });
            this.loading.claimToken = false;
          }
        })
        .catch((error) => {
          console.log("错误!", error);
          this.$message({
            type: "danger",
            message: this.$t('coinRelease.common.出错了'),
          });
          this.loading.claimToken = false;
        });
    },

    setExcludeHolder(temValue) {
      let isAddress = ethers.utils.isAddress(temValue);
      if (isAddress) {
        this.loading.setExcludeHolder = true;
        const provider = store.state.user.provider;
        const signer = provider.getSigner();
        const tokenContract = new ethers.Contract(
          this.tokenAddress,
          this.tokenABI,
          signer
        );
        tokenContract
          .setExcludeHolder(temValue, true)
          .then((rs) => {
            this.dialogVisible.setExcludeHolderVisible = false;
            this.loading.setExcludeHolder = false;
            this.temValue = null;
            this.$message({
              type: "success",
              message: this.$t('coinRelease.common.已提交等待区块确认'),
            });

            rs.wait()
              .then(() => {
                this.$message({
                  type: "success",
                  message: this.$t('coinRelease.common.修改成功'),
                });

                this.getEcoParams(tokenContract);
              })
              .catch((error) => {
                console.log("错误!", error);
                this.$message({
                  type: "danger",
                  message: this.$t('coinRelease.common.确认失败请前往浏览器查看'),
                });
              });
          })
          .catch((error) => {
            console.log("错误!", error);
            this.$message({
              type: "danger",
              message: this.$t('coinRelease.common.出错了'),
            });
            this.dialogVisible.setExcludeHolderVisible = false;
            this.loading.setExcludeHolder = false;
            this.temValue = null;
          });
      } else {
        this.$message({
          type: "danger",
          message: this.$t('coinRelease.common.地址格式不正确'),
        });
        this.loading.setExcludeHolder = false;
      }
    },

    setProcessRewardWaitBlock(temValue) {
      this.loading.setProcessRewardWaitBlock = true;
      const provider = store.state.user.provider;
      const signer = provider.getSigner();
      const tokenContract = new ethers.Contract(
        this.tokenAddress,
        this.tokenABI,
        signer
      );
      tokenContract
        .setProcessRewardWaitBlock(Number(temValue))
        .then((rs) => {
          this.dialogVisible.setProcessRewardWaitBlockVisible = false;
          this.loading.setProcessRewardWaitBlock = false;
          this.temValue = null;
          this.$message({
            type: "success",
            message: this.$t('coinRelease.common.已提交等待区块确认'),
          });

          rs.wait()
            .then(() => {
              this.$message({
                type: "success",
                message: this.$t('coinRelease.common.修改成功'),
              });

              this.getEcoParams(tokenContract);
            })
            .catch((error) => {
              console.log("错误!", error);
              this.$message({
                type: "danger",
                message: this.$t('coinRelease.common.确认失败请前往浏览器查看'),
              });
            });
        })
        .catch((error) => {
          console.log("错误!", error);
          this.$message({
            type: "danger",
            message: this.$t('coinRelease.common.出错了'),
          });
          this.dialogVisible.setProcessRewardWaitBlockVisible = false;
          this.loading.setProcessRewardWaitBlock = false;
          this.temValue = null;
        });
    },

    setHolderRewardCondition(temValue) {
      this.loading.setHolderRewardCondition = true;
      const provider = store.state.user.provider;
      const signer = provider.getSigner();
      const tokenContract = new ethers.Contract(
        this.tokenAddress,
        this.tokenABI,
        signer
      );
      const rewardTokenContract = new ethers.Contract(
        this.ecoParams._rewardToken,
        this.tokenABI,
        signer
      );
      rewardTokenContract
        .decimals()
        .then((decimals) => {
          var amountArray = temValue.toString().split(".");

          console.log("amountArray", amountArray);
          if (amountArray.length == 1) {
            let RewardCondition = BigNumber.from(amountArray[0]).mul(
              BigNumber.from(10).pow(decimals)
            );
            console.log(RewardCondition);
            tokenContract
              .setHolderRewardCondition(RewardCondition)
              .then((rs) => {
                this.dialogVisible.setHolderRewardConditionVisible = false;
                this.loading.setHolderRewardCondition = false;
                this.temValue = null;
                this.$message({
                  type: "success",
                  message: this.$t('coinRelease.common.已提交等待区块确认'),
                });

                rs.wait()
                  .then(() => {
                    this.$message({
                      type: "success",
                      message: this.$t('coinRelease.common.修改成功'),
                    });

                    this.getEcoParams(tokenContract);
                  })
                  .catch((error) => {
                    console.log("错误!", error);
                    this.$message({
                      type: "danger",
                      message: this.$t('coinRelease.common.确认失败请前往浏览器查看'),
                    });
                  });
              })
              .catch((error) => {
                console.log("错误!", error);
                this.$message({
                  type: "danger",
                  message: this.$t('coinRelease.common.出错了'),
                });
                this.dialogVisible.setHolderRewardConditionVisible = false;
                this.loading.setHolderRewardCondition = false;
                this.temValue = null;
              });
          } else if (amountArray.length == 2) {
            var intPart = BigNumber.from(amountArray[0]).mul(
              BigNumber.from(10).pow(decimals)
            );
            var floatPart = BigNumber.from(amountArray[1]).mul(
              BigNumber.from(10).pow(decimals - amountArray[1].length)
            );
            let RewardCondition = intPart.add(floatPart);
            console.log(RewardCondition);
            tokenContract
              .setHolderRewardCondition(RewardCondition)
              .then((rs) => {
                this.dialogVisible.setHolderRewardConditionVisible = false;
                this.loading.setHolderRewardCondition = false;
                this.temValue = null;
                this.$message({
                  type: "success",
                  message: this.$t('coinRelease.common.已提交等待区块确认'),
                });

                rs.wait()
                  .then(() => {
                    this.$message({
                      type: "success",
                      message: this.$t('coinRelease.common.修改成功'),
                    });

                    this.getEcoParams(tokenContract);
                  })
                  .catch((error) => {
                    console.log("错误!", error);
                    this.$message({
                      type: "danger",
                      message: this.$t('coinRelease.common.确认失败请前往浏览器查看'),
                    });
                  });
              })
              .catch((error) => {
                console.log("错误!", error);
                this.$message({
                  type: "danger",
                  message: this.$t('coinRelease.common.出错了'),
                });
                this.dialogVisible.setHolderRewardConditionVisible = false;
                this.loading.setHolderRewardCondition = false;
                this.temValue = null;
              });
          } else {
            this.$message({
              type: "danger",
              message: this.$t("coinRelease.common.请输入正确的数字格式"),
            });
            this.loading.setHolderRewardCondition = false;
            this.temValue = null;
          }
        })
        .catch((error) => {
          console.log("错误!", error);
          this.$message({
            type: "danger",
            message: this.$t('coinRelease.common.出错了'),
          });
          this.dialogVisible.setHolderRewardConditionVisible = false;
          this.loading.setHolderRewardCondition = false;
          this.temValue = null;
        });
    },
    setMinTransAmount(temValue) {
      this.loading.setMinTransAmount = true;
      const provider = store.state.user.provider;
      const signer = provider.getSigner();
      const tokenContract = new ethers.Contract(
        this.tokenAddress,
        this.tokenABI,
        signer
      );

      var amountArray = temValue.toString().split(".");

      console.log("amountArray", amountArray);
      if (amountArray.length == 1) {
        let MinTransAmount = BigNumber.from(amountArray[0]).mul(
          BigNumber.from(10).pow(this.basicParams._decimals)
        );
        console.log(MinTransAmount);
        tokenContract
          .setMinTransAmount(MinTransAmount)
          .then((rs) => {
            this.dialogVisible.setMinTransAmountVisible = false;
            this.loading.setMinTransAmount = false;
            this.temValue = null;
            this.$message({
              type: "success",
              message: this.$t('coinRelease.common.已提交等待区块确认'),
            });

            rs.wait()
              .then(() => {
                this.$message({
                  type: "success",
                  message: this.$t('coinRelease.common.修改成功'),
                });

                this.getEcoParams(tokenContract);
              })
              .catch((error) => {
                console.log("错误!", error);
                this.$message({
                  type: "danger",
                  message: this.$t('coinRelease.common.确认失败请前往浏览器查看'),
                });
              });
          })
          .catch((error) => {
            console.log("错误!", error);
            this.$message({
              type: "danger",
              message: this.$t('coinRelease.common.出错了'),
            });
            this.dialogVisible.setMinTransAmountVisible = false;
            this.loading.setMinTransAmount = false;
            this.temValue = null;
          });
      } else if (amountArray.length == 2) {
        var intPart = BigNumber.from(amountArray[0]).mul(
          BigNumber.from(10).pow(18)
        );
        console.log(intPart);
        var floatPart = BigNumber.from(amountArray[1]).mul(
          BigNumber.from(10).pow(
            Number(this.basicParams._decimals) - amountArray[1].length
          )
        );
        let MinTransAmount = intPart.add(floatPart);
        console.log(MinTransAmount);
        tokenContract
          .setMinTransAmount(MinTransAmount)
          .then((rs) => {
            this.dialogVisible.setMinTransAmountVisible = false;
            this.loading.setMinTransAmount = false;
            this.temValue = null;
            this.$message({
              type: "success",
              message: this.$t('coinRelease.common.已提交等待区块确认'),
            });

            rs.wait()
              .then(() => {
                this.$message({
                  type: "success",
                  message: this.$t('coinRelease.common.修改成功'),
                });

                this.getEcoParams(tokenContract);
              })
              .catch((error) => {
                console.log("错误!", error);
                this.$message({
                  type: "danger",
                  message: this.$t('coinRelease.common.确认失败请前往浏览器查看'),
                });
              });
          })
          .catch((error) => {
            console.log("错误!", error);
            this.$message({
              type: "danger",
              message: this.$t('coinRelease.common.出错了'),
            });
            this.dialogVisible.setMinTransAmountVisible = false;
            this.loading.setMinTransAmount = false;
            this.temValue = null;
          });
      } else {
        this.$message({
          type: "danger",
          message: this.$t("coinRelease.common.请输入正确的数字格式"),
        });
        this.loading.setMinTransAmount = false;
        this.temValue = null;
      }
    },

    getGenerationFee() {
      console.log(this.temRate.generations);
      console.log(
        Number(this.temRate.fristRate) + Number(this.temRate.secondRate)
      );
      if (this.temRate.generations == 1) {
        this.temRate.fristRate = 100;
        this.temRate.secondRate = 0;
        this.temRate.thirdRate = 0;
      } else if (this.temRate.generations == 2) {
        if (this.temRate.fristRate > 100) {
          this.temRate.fristRate = 100;
          this.temRate.secondRate = 0;
          this.temRate.thirdRate = 0;
          this.temRate.leftRate = 0;
        } else {
          this.temRate.secondRate = 100 - this.temRate.fristRate;
          this.temRate.thirdRate = 0;
          this.temRate.leftRate = 0;
        }
      } else if (this.temRate.generations == 3) {
        if (this.temRate.fristRate > 100) {
          this.temRate.fristRate = 100;
          this.temRate.secondRate = 0;
          this.temRate.thirdRate = 0;
          this.temRate.leftRate = 0;
        } else if (
          Number(this.temRate.fristRate) + Number(this.temRate.secondRate) >
          100
        ) {
          this.temRate.secondRate = 100 - this.temRate.fristRate;
          this.temRate.thirdRate = 0;
          this.temRate.leftRate = 0;
        } else {
          this.temRate.thirdRate =
            100 - this.temRate.fristRate - this.temRate.secondRate;
          this.temRate.leftRate = 0;
        }
      } else {
        if (this.temRate.fristRate > 100) {
          this.temRate.fristRate = 100;
          this.temRate.secondRate = 0;
          this.temRate.thirdRate = 0;
          this.temRate.leftRate = 0;
        } else if (
          Number(this.temRate.fristRate) + Number(this.temRate.secondRate) >
          100
        ) {
          this.temRate.secondRate = 100 - this.temRate.fristRate;
          this.temRate.thirdRate = 0;
          this.temRate.leftRate = 0;
        } else if (
          Number(this.temRate.fristRate) +
            Number(this.temRate.secondRate) +
            Number(this.temRate.thirdRate) >
          100
        ) {
          this.temRate.thirdRate =
            100 - this.temRate.fristRate - this.temRate.secondRate;
          this.temRate.leftRate = 0;
        } else {
          var leftRate =
            (100 -
              this.temRate.fristRate -
              this.temRate.secondRate -
              this.temRate.thirdRate) /
            (this.temRate.generations - 3);
          var floatRate =
            100 -
            this.temRate.fristRate -
            this.temRate.secondRate -
            this.temRate.thirdRate -
            parseInt(leftRate) * (this.temRate.generations - 3);
          this.temRate.fristRate += floatRate;
          this.temRate.leftRate = parseInt(leftRate);
        }
      }
    },

    copy_str(text) {
      if (navigator.clipboard) {
        // clipboard api 复制
        navigator.clipboard.writeText(text);
      } else {
        var textarea = document.createElement("textarea");
        document.body.appendChild(textarea);
        // 隐藏此输入框
        textarea.style.position = "fixed";
        textarea.style.clip = "rect(0 0 0 0)";
        textarea.style.top = "10px";
        // 赋值
        textarea.value = text;
        // 选中
        textarea.select();
        // 复制
        document.execCommand("copy", true);
        // 移除输入框
        document.body.removeChild(textarea);
      }
      this.$message({
        type: "success",
        message: this.$t('coinRelease.common.已复制'),
      });
    },
    checkDecimal(num) {
      var str = num;
      var len1 = str.substr(0, 1);
      var len2 = str.substr(1, 1);
      //如果第一位是0，第二位不是点，就用数字把点替换掉
      if (str.length > 1 && len1 == 0 && len2 != ".") {
        str = str.substr(1, 1);
      }
      //第一位不能是.
      if (len1 == ".") {
        str = "";
      }
      //限制只能输入一个小数点
      if (str.indexOf(".") != -1) {
        var str_ = str.substr(str.indexOf(".") + 1);
        if (str_.indexOf(".") != -1) {
          str = str.substr(0, str.indexOf(".") + str_.indexOf(".") + 1);
        }
      }
      //正则替换
      str = str.replace(/[^\d^\.]+/g, ""); // 保留数字和小数点
      str = str.replace(/\.\d\d\d$/, ""); // 小数点后只能输两位
      return str;
    },
  },
};
</script>

<style scoped>
@media screen and (max-width: 500px) {
  .main {
    text-align: center;
    margin-top: 5%;
    padding-left: 0;
    padding-right: 0;
  }
  .row {
    width: 100%;
    margin-bottom: 10px;
  }
  .box-card {
    margin-bottom: 20px;
    text-align: center;
    width: 100%;
  }
  .controlbutton {
    margin-bottom: 10px;
  }
}
@media screen and (min-width: 500px) {
  .header {
    text-align: center;
    margin-top: 0px;
    font-size: larger;
  }
  .main {
    /* display: flexbox;
    justify-content: center; */
    margin-top: 0%;
    padding-left: 5%;
    padding-right: 5%;
  }
  .row {
    width: 100%;
    margin-bottom: 10px;
  }

  .col {
    /* margin-left: 20px; */
    border-radius: 4px;
  }
  .box-card {
    margin-bottom: 20px;
    text-align: center;
    width: 100%;
  }
  .controlbutton {
    margin-bottom: 10px;
  }
}
</style>
