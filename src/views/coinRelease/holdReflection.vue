<template>
  <div class="app-container">
    <div class="inner">
      <el-alert
        v-show="support"
        :title="$t('coinRelease.common.暂不支持此链')"
        type="error"
        :description="$t('coinRelease.common.如有需要请联系管理员定制')"
        center
        :closable="false"
        show-icon
      >
      </el-alert>
      <el-header class="header">
        <p>
          {{ $t("coinRelease.holdReflection.分红本币") }}
          <a
            style="
              margin-left: 10px;
              font-size: 12px;
              font-weight: 400;
              color: #409eff;
            "
            :href="helpURL"
            target="_blank"
            >{{ $t("coinRelease.common.教程") }}</a
          >
        </p>
        <p style="font-size: 14px; font-weight: 300">
          {{
            $t(
              "coinRelease.holdReflection.简单干净合约无黑白名单无权限加池自动开盘持币即可获益"
            )
          }}
        </p>
      </el-header>
      <el-main class="main">
        <el-form :model="form">
          <el-form-item :label="$t('coinRelease.common.代币全称')">
            <el-input
              v-model="form._name"
              onkeyup="this.value=this.value.replace(/[\u4E00-\u9FA5]/g,'')"
              placeholder="Name"
            />
          </el-form-item>
          <el-form-item :label="$t('coinRelease.common.代币简称')">
            <el-input
              v-model="form._symbol"
              onkeyup="this.value=this.value.replace(/[\u4E00-\u9FA5]/g,'')"
              placeholder="symbol"
            />
          </el-form-item>
          <el-form-item :label="$t('coinRelease.common.发行量')">
            <el-input
              v-model="form._supply"
              oninput="value=value.replace(/[^0-9.]/g,'')"
              placeholder="202304"
            />
          </el-form-item>
          <el-form-item :label="$t('coinRelease.common.精度')">
            <el-input-number
              v-model="form._decimals"
              :min="1"
              :max="18"
              size="small"
            ></el-input-number>
          </el-form-item>
          <el-form-item :label="$t('coinRelease.common.买入税率')">
            <el-row
              >{{ getBuyFee }} %
              <el-tooltip placement="top" effect="light">
                <div style="font-size: 14px; font-weight: 300" slot="content">
                  {{ $t("coinRelease.common.税率说明") }}<br /><br />

                  {{ $t("coinRelease.common.买入回流税率") }}:
                  {{
                    $t(
                      "coinRelease.common.交易中指定额度的代币将会自动添加到流动池内保证交易始终存在流动性"
                    )
                  }}<br /><br />

                  {{ $t("coinRelease.common.买入营销税率") }}:
                  {{
                    $t(
                      "coinRelease.common.交易中指定额度的代币将会自动转入营销钱包中用于项目方做其他营销"
                    )
                  }}<br /><br />

                  {{ $t("coinRelease.common.买入销毁税率") }}:
                  {{
                    $t(
                      "coinRelease.common.交易中指定额度的代币将会被打入黑洞地址变相实现通缩机制"
                    )
                  }}
                  <br /><br />

                  {{ $t("coinRelease.common.买入分红税率") }}:
                  {{
                    $t(
                      "coinRelease.holdReflection.交易中指定额度的代币会按持币比例分配给所有持币者实现持币分红"
                    )
                  }}<br /><br />

                  {{
                    $t(
                      "coinRelease.common.买入总税率不能超过25交易总税率不能超过50"
                    )
                  }}<br />
                </div>
                <i class="el-icon-warning-outline" />
              </el-tooltip>
            </el-row>
            <el-row :gutter="20">
              <el-col :xs="6" :sm="3" style="margin-top: 10px"
                >{{ $t("coinRelease.common.买入营销税率") }}:</el-col
              >

              <el-col :xs="18" :sm="9" style="margin-top: 10px">
                <el-input
                  v-model="form._buyFundFee"
                  @keyup.native="
                    form._buyFundFee = checkDecimal(form._buyFundFee)
                  "
                  placeholder="0.01"
                >
                  <template slot="append">%</template>
                </el-input>
              </el-col>

              <el-col :xs="6" :sm="3" style="margin-top: 10px"
                >{{ $t("coinRelease.common.买入销毁税率") }}:</el-col
              >

              <el-col :xs="18" :sm="9" style="margin-top: 10px">
                <el-input
                  v-model="form.buy_burnFee"
                  @keyup.native="
                    form.buy_burnFee = checkDecimal(form.buy_burnFee)
                  "
                  placeholder="0.01"
                >
                  <template slot="append">%</template>
                </el-input>
              </el-col>

              <el-col :xs="6" :sm="3" style="margin-top: 10px">
                {{ $t("coinRelease.common.买入回流税率") }}:</el-col
              >

              <el-col :xs="18" :sm="9" style="margin-top: 10px">
                <el-input
                  v-model="form._buyLPFee"
                  @keyup.native="form._buyLPFee = checkDecimal(form._buyLPFee)"
                  placeholder="0.01"
                >
                  <template slot="append">%</template>
                </el-input>
              </el-col>

              <el-col :xs="6" :sm="3" style="margin-top: 10px">
                {{ $t("coinRelease.common.买入分红税率") }}:</el-col
              >

              <el-col :xs="18" :sm="9" style="margin-top: 10px">
                <el-input
                  v-model="form._buyReflectFee"
                  @keyup.native="
                    form._buyReflectFee = checkDecimal(form._buyReflectFee)
                  "
                  placeholder="0.01"
                >
                  <template slot="append">%</template>
                </el-input>
              </el-col>
            </el-row>
          </el-form-item>
          <el-form-item :label="$t('coinRelease.common.卖出税率')">
            <el-row
              >{{ getSellFee }} %
              <el-tooltip placement="top" effect="light">
                <div style="font-size: 14px; font-weight: 300" slot="content">
                  {{ $t("coinRelease.common.税率说明") }}<br /><br />

                  {{ $t("coinRelease.common.回流税率") }}:
                  {{
                    $t(
                      "coinRelease.common.交易中指定额度的代币将会自动添加到流动池内保证交易始终存在流动性"
                    )
                  }}<br /><br />

                  {{ $t("coinRelease.common.营销税率") }}:
                  {{
                    $t(
                      "coinRelease.holdReflection.交易中指定额度的代币将会转换为底池代币并自动转入营销钱包中用于项目方做其他营销"
                    )
                  }}<br /><br />

                  {{ $t("coinRelease.common.销毁税率") }}:
                  {{
                    $t(
                      "coinRelease.common.交易中指定额度的代币将会被打入黑洞地址变相实现通缩机制"
                    )
                  }}<br /><br />

                  {{ $t("coinRelease.common.分红税率") }}:
                  {{
                    $t(
                      "coinRelease.holdReflection.交易中指定额度的代币会按持币比例分配给所有持币者实现持币分红"
                    )
                  }}<br /><br />

                  {{
                    $t(
                      "coinRelease.common.买入总税率不能超过25交易总税率不能超过50"
                    )
                  }}<br />
                </div>
                <i class="el-icon-warning-outline" />
              </el-tooltip>
            </el-row>
            <el-row :gutter="20">
              <el-col :xs="6" :sm="3" style="margin-top: 10px"
                >{{ $t("coinRelease.common.营销税率") }}:</el-col
              >

              <el-col :xs="18" :sm="9" style="margin-top: 10px">
                <el-input
                  v-model="form._sellFundFee"
                  @keyup.native="
                    form._sellFundFee = checkDecimal(form._sellFundFee)
                  "
                  placeholder="0.01"
                >
                  <template slot="append">%</template>
                </el-input>
              </el-col>

              <el-col :xs="6" :sm="3" style="margin-top: 10px"
                >{{ $t("coinRelease.common.销毁税率") }}:</el-col
              >

              <el-col :xs="18" :sm="9" style="margin-top: 10px">
                <el-input
                  v-model="form.sell_burnFee"
                  @keyup.native="
                    form.sell_burnFee = checkDecimal(form.sell_burnFee)
                  "
                  placeholder="0.01"
                >
                  <template slot="append">%</template>
                </el-input>
              </el-col>

              <el-col :xs="6" :sm="3" style="margin-top: 10px">
                {{ $t("coinRelease.common.回流税率") }}:</el-col
              >

              <el-col :xs="18" :sm="9" style="margin-top: 10px">
                <el-input
                  v-model="form._sellLPFee"
                  @keyup.native="
                    form._sellLPFee = checkDecimal(form._sellLPFee)
                  "
                  placeholder="0.01"
                >
                  <template slot="append">%</template>
                </el-input>
              </el-col>

              <el-col :xs="6" :sm="3" style="margin-top: 10px">
                {{ $t("coinRelease.common.分红税率") }}:</el-col
              >

              <el-col :xs="18" :sm="9" style="margin-top: 10px">
                <el-input
                  v-model="form._sellReflectFee"
                  @keyup.native="
                    form._sellReflectFee = checkDecimal(form._sellReflectFee)
                  "
                  placeholder="0.01"
                >
                  <template slot="append">%</template>
                </el-input>
              </el-col>
            </el-row>
          </el-form-item>

          <el-form-item :label="$t('coinRelease.common.营销钱包')">
            <el-input v-model="form._fundAddress" placeholder="0x">
              <el-button
                slot="append"
                @click="form._fundAddress = store.state.user.address"
                >{{ $t("coinRelease.common.使用当前钱包") }}</el-button
              >
            </el-input>
          </el-form-item>
          <el-form-item :label="$t('coinRelease.common.选择交易所')">
            <el-select
              v-model="form.selectSwap"
              @change="getSwap"
              style="width: 100%"
              :placeholder="$t('coinRelease.common.请选择')"
            >
              <el-option
                v-for="item in swapOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
                <span style="float: left">{{ item.label }}</span>
              </el-option>
            </el-select>
            <el-input
              v-if="form.otherSwap"
              v-model="form._swapRouter"
              placeholder="0x..."
              style="margin-top: 10px"
            />
            <p
              v-if="form.otherSwap"
              style="
                font-size: 12px;
                font-weight: 300;
                margin-top: -10px;
                margin-bottom: -15px;
              "
            >
              {{
                $t(
                  "coinRelease.common.添加流动性时需要添加代币与底池代币之间的交易对否则无法进行正常分红与回流"
                )
              }}
            </p>
          </el-form-item>
          <el-form-item :label="$t('coinRelease.common.选择底池代币')">
            <el-select
              v-model="form.selectCurrency"
              @change="getCurrency"
              style="width: 100%"
              :placeholder="$t('coinRelease.common.请选择')"
            >
              <el-option
                v-for="item in currencyOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
                <span style="float: left">{{ item.label }}</span>
              </el-option>
              <el-option
                :label="$t('coinRelease.common.其他代币')"
                value="1"
              ></el-option>
            </el-select>
            <el-input
              v-if="form.otherCurrency"
              v-model="form._currency"
              placeholder="0x..."
              style="margin-top: 10px"
            />
            <p
              v-if="form.otherCurrency"
              style="
                font-size: 12px;
                font-weight: 300;
                margin-top: -10px;
                margin-bottom: -15px;
              "
            >
              {{
                $t(
                  "coinRelease.common.添加流动性时需要添加代币与底池代币之间的交易对否则无法进行正常分红与回流"
                )
              }}
            </p>
          </el-form-item>
        </el-form>

        <el-button type="primary" @click="getReadytoCreate">{{
          $t("coinRelease.common.创建合约")
        }}</el-button>
        <span style="font-size: 12px; margin-left: 10px"
          >{{ $t("coinRelease.common.费用") }}:{{ getCostAndSymbol }}</span
        >
        <el-dialog
          :title="$t('coinRelease.common.创建合约')"
          :visible.sync="dialogVisible"
          show-close
        >
          <el-collapse v-model="activeNames">
            <el-collapse-item
              :title="$t('coinRelease.common.合约地址')"
              name="1"
            >
              <div style="font-size: 14px">
                {{ $t("coinRelease.common.预计生成地址") }}:
              </div>
              <div style="font-size: 16px; margin-top: 10px">
                {{ coinAddress }}
              </div>

              <div>
                {{ $t("coinRelease.common.预估手续费") }}:<span
                  style="color: red"
                  >{{ gasFee }}</span
                >,{{ $t("coinRelease.common.请确保钱包余额充足余额不足将")
                }}<span style="color: red">{{
                  $t("coinRelease.common.创建失败")
                }}</span
                >！
              </div>
            </el-collapse-item>
            <el-collapse-item
              :title="$t('coinRelease.common.开源参数')"
              name="2"
            >
              <div class="flex-container">
                <el-tag type="success">Optimization: YES</el-tag>
                <el-tag style="margin-left: 5px"> Runs: 200</el-tag>
              </div>
              <div class="flex-container">
                <el-tag type="success">Solidity Version: 0.8.18</el-tag>
                <el-tag style="margin-left: 5px"> License: MIT</el-tag>
              </div>
              <div class="flex-container">
                <el-link
                  icon="el-icon-discover"
                  :href="this.scanURL + coinAddress"
                  target="_blank"
                  >{{ $t("coinRelease.common.浏览器查看") }}</el-link
                >
                <el-link
                  style="margin-left: 10px"
                  icon="el-icon-video-play"
                  :href="tutorialLink"
                  target="_blank"
                  >{{ $t("coinRelease.common.开源教程") }}</el-link
                >
              </div>
              <div class="flex-container">
                <el-button
                  type="info"
                  size="small"
                  plain
                  @click="copy_str(holdReflectionCode)"
                  >{{ $t("coinRelease.common.复制源代码") }}</el-button
                >
                <el-button
                  type="info"
                  size="small"
                  plain
                  @click="copy_str(constructorArgs)"
                  >{{ $t("coinRelease.common.复制构造参数") }}</el-button
                >
              </div>
              <p>
                {{
                  $t(
                    "coinRelease.common.构造参数无法找回若不立即开源请复制后保存到本地文档"
                  )
                }}
              </p>
            </el-collapse-item>
          </el-collapse>
          <span slot="footer" class="dialog-footer">
            <el-button v-show="console" @click="enterConsole">{{
              $t("coinRelease.common.进入控制台")
            }}</el-button>
            <el-button v-if="loading" type="primary" :loading="true">{{
              $t("coinRelease.common.创建合约")
            }}</el-button>
            <el-button v-else-if="console" type="primary" disabled>{{
              $t("coinRelease.common.创建合约")
            }}</el-button>
            <el-button v-else type="primary" @click="onSubmit">{{
              $t("coinRelease.common.创建合约")
            }}</el-button>
          </span>
        </el-dialog>
        <!-- <el-button @click="onCancel">Vertify</el-button> -->
      </el-main>
    </div>
  </div>
</template>

<script>
import CoinData from "@/contracts/standardCoin.json";
import chainParams from "@/contracts/coinReleaseParams.json";
import { holdReflectionCode } from "@/contracts/sourceCode.js";
import Mode1 from "@/contracts/pandaMode1.json";
import store from "@/store";
const { ethers, BigNumber } = require("ethers");
const supportChain = [97, 56, 1116, 8453, 42161, 84532];
export default {
  name: "Dashboard",
  data() {
    return {
      CoinData,
      holdReflectionCode,
      Mode1,
      store,
      supportChain,
      support: null,
      activeNames: ["1", "2"],
      ModeAddress: null,
      scanURL: null,
      dialogVisible: false,
      form: {
        _name: null,
        _symbol: null,
        _supply: null,
        _decimals: 18,

        _buyFundFee: null,
        buy_burnFee: null,
        _buyLPFee: null,
        _buyReflectFee: null,

        _sellFundFee: null,
        _sellLPFee: null,
        sell_burnFee: null,
        _sellReflectFee: null,

        _fundAddress: null,

        selectSwap: null,
        otherSwap: false,
        _swapRouter: null,

        selectCurrency: null,
        otherCurrency: false,
        _currency: null,

        _WETH: null,

        airdropEnable: false,
        airdropNumbs: 0,

        bigSupply: null,
      },

      currencyOptions: [],
      swapOptions: [],
      loading: false,
      console: false,
      Fee: null,
      value: null,
      chainSymbol: null,
      gasFee: null,
      salt: null,
      coinAddress: null,
      tutorialLink:
        this.$i18n.locale == "en-US"
          ? "https://help.pandatool.org/english/createtoken/verify-and-publish"
          : "https://help.pandatool.org/createtoken/verify-and-publish",
      helpURL:
        this.$i18n.locale == "en-US"
          ? "https://help.pandatool.org/english/createtoken/holdreflection"
          : "https://help.pandatool.org/createtoken/holdreflection",
      constructorArgs: null,
    };
  },

  created() {
    let Inval = setInterval(() => {
      if (store.state.user.chainId) {
        window.clearInterval(Inval);
        const chainId = store.state.user.chainId;
        if (supportChain.includes(chainId)) {
          this.support = false;
        } else {
          this.support = true;
        }
        this.scanURL = chainParams[chainId][0];
        const selectChainParams = chainParams[chainId][6];
        this.ModeAddress = ethers.utils.getAddress(
          selectChainParams.ModeAddress
        );
        console.log("mode", this.ModeAddress);
        this.Fee = selectChainParams.Fee;
        this.chainSymbol = selectChainParams.chainSymbol;
        this.form._WETH = ethers.utils.getAddress(selectChainParams._WETH);

        for (let i = 0; i < selectChainParams.currencyOptions.length; i++) {
          var temAddress = ethers.utils.getAddress(
            selectChainParams.currencyOptions[i].value
          );
          var temParam = {
            label: selectChainParams.currencyOptions[i].label,
            value: temAddress,
          };
          this.currencyOptions.push(temParam);
        }
        for (let i = 0; i < selectChainParams.swapOptions.length; i++) {
          var temAddress = ethers.utils.getAddress(
            selectChainParams.swapOptions[i].value
          );
          var temParam = {
            label: selectChainParams.swapOptions[i].label,
            value: temAddress,
          };
          this.swapOptions.push(temParam);
        }
      }
    }, 1000);
  },
  computed: {
    getBuyFee() {
      return (
        Number(this.form._buyLPFee) +
        Number(this.form._buyFundFee) +
        Number(this.form.buy_burnFee) +
        Number(this.form._buyReflectFee)
      );
    },
    getSellFee() {
      return (
        Number(this.form._sellLPFee) +
        Number(this.form._sellFundFee) +
        Number(this.form.sell_burnFee) +
        Number(this.form._sellReflectFee)
      );
    },

    getCostAndSymbol() {
      return this.Fee + " " + this.chainSymbol;
    },
  },
  methods: {
    getCurrency(selectCurrency) {
      console.log("selectCurrency", selectCurrency);

      if (selectCurrency == 1) {
        this.form.otherCurrency = true;
        this.form._currency = null;
      } else {
        this.form.otherCurrency = false;
        this.form._currency = selectCurrency;

        if (this.form._currency == this.form._WETH) {
          this.form.currencyIsEth = true;
        } else {
          this.form.currencyIsEth = false;
        }
      }
    },
    getSwap(selectSwap) {
      console.log("selectSwap", selectSwap);
      if (selectSwap == 1) {
        this.form.otherSwap = true;
        this.form._swapRouter = null;
      } else {
        this.form.otherSwap = false;
        this.form._swapRouter = selectSwap;
      }
    },

    checkParams() {
      let isFund = ethers.utils.isAddress(this.form._fundAddress);
      if (!isFund) {
        this.$message({
          type: "error",
          message: this.$t("coinRelease.common.营销钱包地址不正确"),
        });
        return false;
      }

      var supplyArray = this.form._supply.toString().split(".");

      if (supplyArray.length == 1) {
        this.form.bigSupply = BigNumber.from(supplyArray[0]).mul(
          BigNumber.from(10).pow(this.form._decimals)
        );
      } else if (supplyArray.length == 2) {
        var intPart = BigNumber.from(supplyArray[0]).mul(
          BigNumber.from(10).pow(this.form._decimals)
        );
        var floatPart = BigNumber.from(supplyArray[1]).mul(
          BigNumber.from(10).pow(this.form._decimals - supplyArray[1].length)
        );
        this.form.bigSupply = intPart.add(floatPart);
      } else {
        this.$message({
          type: "danger",
          message: this.$t("coinRelease.common.初始供应量格式不正确"),
        });
        return false;
      }

      return true;
    },
    onSubmit() {
      this.loading = true;
      const Mode1Abi = this.Mode1;
      const provider = store.state.user.provider;
      const signer = provider.getSigner();
      const Mode1 = new ethers.Contract(this.ModeAddress, Mode1Abi, signer);
      provider.getNetwork().then((network) => {
        console.log(network.chainId);
        if (network.chainId == store.state.user.chainId) {
          this.DeployContract(Mode1, this.value)
            .then(() => {})
            .catch((error) => {
              console.log("error", error);
              this.loading = false;
              this.$message({
                type: "error",
                message: this.$t("coinRelease.common.创建失败请重试"),
              });
            });
        } else {
          this.$message({
            type: "error",
            message: this.$t(
              "coinRelease.common.公链错误请确保钱包与选择的公链一致"
            ),
          });
        }
      });
    },
    async DeployContract(Mode1, _value) {
      const stringParam = [this.form._name, this.form._symbol];
      const addressParam = [
        this.form._fundAddress,
        this.form._currency,
        this.form._swapRouter,
        store.state.user.address,
      ];
      const numberParam = [
        Number(this.form._decimals),
        this.form.bigSupply,
        Math.floor(Number(this.form._buyFundFee) * 100),
        Math.floor(Number(this.form._buyLPFee) * 100),
        Math.floor(Number(this.form._buyReflectFee) * 100),
        Math.floor(Number(this.form.buy_burnFee) * 100),

        Math.floor(Number(this.form._sellFundFee) * 100),
        Math.floor(Number(this.form._sellLPFee) * 100),
        Math.floor(Number(this.form._sellReflectFee) * 100),
        Math.floor(Number(this.form.sell_burnFee) * 100),

        Number(this.form.airdropNumbs),
      ];
      const boolParam = [this.form.airdropEnable];
      let overrides = {
        // The maximum units of gas for the transaction to use
        // gasLimit: 50000000,

        // The price (in wei) per unit of gas
        // gasPrice: utils.parseUnits('9.0', 'gwei'),

        // The nonce to use in the transaction
        // nonce: 123,

        // The amount to send with the transaction (i.e. msg.value)
        value: _value,

        // The chain ID (or network ID) to use
        // chainId: 1
      };
      // 常见合约工厂实例
      console.log("stringParam", stringParam);
      console.log("addressParam", addressParam);
      console.log("numberParam", numberParam);
      console.log("boolParam", boolParam);

      const contract = await Mode1.CreateContract(
        this.salt,
        stringParam,
        addressParam,
        numberParam,
        boolParam,
        overrides
      );
      console.log("result", contract.hash);
      console.log("contract", contract);

      await contract.wait();
      this.loading = false;
      this.console = true;
      this.$message({
        type: "success",
        message: this.$t("coinRelease.common.创建成功"),
      });
    },

    onCancel() {
      console.log("sss", standard);
      // console.log('chainId',store.state.user.chainID)
      // console.log('address',store.state.user.chainName)
    },
    async getReadytoCreate() {
      if (this.checkParams()) {
        var FeeArray = this.Fee.toString().split(".");
        var intPart = BigNumber.from(FeeArray[0]).mul(
          BigNumber.from(10).pow(18)
        );
        var floatPart = BigNumber.from(FeeArray[1]).mul(
          BigNumber.from(10).pow(18 - FeeArray[1].length)
        );
        this.value = intPart.add(floatPart);
        const encodedbytes = this.CoinData.Coin6encodebytes;
        this.salt = ethers.utils.id("pandatoken");
        console.log("salt", this.salt);
        const stringParam = [this.form._name, this.form._symbol];
        const addressParam = [
          this.form._fundAddress,
          this.form._currency,
          this.form._swapRouter,
          store.state.user.address,
        ];
        const numberParam = [
          Number(this.form._decimals),
          this.form.bigSupply,
          Math.floor(Number(this.form._buyFundFee) * 100),
          Math.floor(Number(this.form._buyLPFee) * 100),
          Math.floor(Number(this.form._buyReflectFee) * 100),
          Math.floor(Number(this.form.buy_burnFee) * 100),

          Math.floor(Number(this.form._sellFundFee) * 100),
          Math.floor(Number(this.form._sellLPFee) * 100),
          Math.floor(Number(this.form._sellReflectFee) * 100),
          Math.floor(Number(this.form.sell_burnFee) * 100),

          Number(this.form.airdropNumbs),
        ];
        const boolParam = [this.form.airdropEnable];

        var abiCoder = new ethers.utils.AbiCoder();
        this.constructorArgs = abiCoder
          .encode(
            ["string[]", "address[]", "uint256[]", "bool[]"],
            [stringParam, addressParam, numberParam, boolParam]
          )
          .slice(2);
        // console.log("encodeABI", this.constructorArgs);

        const initCode = encodedbytes + this.constructorArgs;

        // console.log("initCode", initCode);
        const initCodeHash = ethers.utils.keccak256(initCode);
        // console.log("initCodeHash", initCodeHash);
        this.coinAddress = ethers.utils.getCreate2Address(
          this.ModeAddress,
          this.salt,
          initCodeHash
        );

        console.log(this.coinAddress);

        const Mode1Abi = this.Mode1;
        const provider = store.state.user.provider;

        const Mode1 = new ethers.Contract(this.ModeAddress, Mode1Abi, provider);
        let overrides = { value: this.value };
        console.log("value", overrides);
        const GasPrice = await provider.getGasPrice();
        const estimateGas = await Mode1.estimateGas.CreateContract(
          this.salt,
          stringParam,
          addressParam,
          numberParam,
          boolParam
        );
        console.log(this.Fee);
        this.gasFee = parseFloat(
          new Number(GasPrice.mul(estimateGas)) / 10 ** 18 + Number(this.Fee)
        ).toFixed(6);
        console.log("gasfee", this.gasFee);
        this.dialogVisible = true;
      }
    },
    enterConsole() {
      this.$router.push({
        path: "/coinrelease/holdReflectionDetail/",
        query: {
          coinAddress: this.coinAddress,
          modeType: this.$t("coinRelease.modeType.分红本币"),
        },
      });
    },

    copy_str(text) {
      if (navigator.clipboard) {
        // clipboard api 复制
        navigator.clipboard.writeText(text);
      } else {
        var textarea = document.createElement("textarea");
        document.body.appendChild(textarea);
        // 隐藏此输入框
        textarea.style.position = "fixed";
        textarea.style.clip = "rect(0 0 0 0)";
        textarea.style.top = "10px";
        // 赋值
        textarea.value = text;
        // 选中
        textarea.select();
        // 复制
        document.execCommand("copy", true);
        // 移除输入框
        document.body.removeChild(textarea);
      }
      this.$message({
        type: "success",
        message: this.$t("coinRelease.common.已复制"),
      });
    },
    checkDecimal(num) {
      var str = num;
      var len1 = str.substr(0, 1);
      var len2 = str.substr(1, 1);
      //如果第一位是0，第二位不是点，就用数字把点替换掉
      if (str.length > 1 && len1 == 0 && len2 != ".") {
        str = str.substr(1, 1);
      }
      //第一位不能是.
      if (len1 == ".") {
        str = "";
      }
      //限制只能输入一个小数点
      if (str.indexOf(".") != -1) {
        var str_ = str.substr(str.indexOf(".") + 1);
        if (str_.indexOf(".") != -1) {
          str = str.substr(0, str.indexOf(".") + str_.indexOf(".") + 1);
        }
      }
      //正则替换
      str = str.replace(/[^\d^\.]+/g, ""); // 保留数字和小数点
      str = str.replace(/\.\d\d\d$/, ""); // 小数点后只能输两位
      return str;
    },
  },
};
</script>

<style scoped>
@media screen and (max-width: 500px) {
}
@media screen and (min-width: 500px) {
  .el-input {
    width: 80%;
  }
  .el-select {
    width: 80%;
  }
}
</style>
