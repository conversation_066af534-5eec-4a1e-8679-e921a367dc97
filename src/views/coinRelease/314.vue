<template>
  <div class="app-container">
    <div class="inner">
      <el-alert
        v-show="support"
        :title="$t('coinRelease.common.暂不支持此链')"
        type="error"
        :description="$t('coinRelease.common.如有需要请联系管理员定制')"
        center
        :closable="false"
        show-icon
      >
      </el-alert>
      <el-header class="header">
        <p>
          {{ $t("coinRelease.a314.三14协议") }}
          <a
            style="
              margin-left: 10px;
              font-size: 12px;
              font-weight: 400;
              color: #409eff;
            "
            :href="helpURL"
            target="_blank"
            >{{ $t("coinRelease.common.教程") }}</a
          >
        </p>
        <p style="font-size: 14px; font-weight: 300">
          {{
            $t(
              "coinRelease.a314.席卷全球创新玩法无需swap即可兑换交易冷却防夹子"
            )
          }}
        </p>
      </el-header>
      <el-main class="main">
        <el-form :model="form">
          <el-form-item :label="$t('coinRelease.common.代币全称')">
            <el-input
              v-model="form._name"
              onkeyup="this.value=this.value.replace(/[\u4E00-\u9FA5]/g,'')"
              placeholder="Name"
            />
          </el-form-item>
          <el-form-item :label="$t('coinRelease.common.代币简称')">
            <el-input
              v-model="form._symbol"
              onkeyup="this.value=this.value.replace(/[\u4E00-\u9FA5]/g,'')"
              placeholder="symbol"
            />
          </el-form-item>
          <el-form-item :label="$t('coinRelease.common.发行量')">
            <el-input
              v-model="form._supply"
              oninput="value=value.replace(/[^0-9.]/g,'')"
              placeholder="202304"
            />
          </el-form-item>
          <el-form-item :label="$t('coinRelease.common.精度')">
            <el-input-number
              v-model="form._decimals"
              :min="1"
              :max="18"
              size="small"
              disabled
            ></el-input-number>
          </el-form-item>
          <el-form-item :label="$t('coinRelease.a314.a314设置')">
            <el-row>
              <el-tooltip placement="top" effect="light">
                <div style="font-size: 14px; font-weight: 300" slot="content">
                  {{
                    $t(
                      "coinRelease.a314.流动性占比发币时自动转入合约地址的代币比例用以提供流动性"
                    )
                  }}<br /><br />
                  {{ $t("coinRelease.a314.冷却时间") }}:{{
                    $t(
                      "coinRelease.a314.每次买入之间的间隔时间单个每次卖出的间隔时间"
                    )
                  }}
                  <br /><br />
                </div>
                <i class="el-icon-warning-outline" />
              </el-tooltip>
            </el-row>

            <el-row>
              <el-col :xs="8" :sm="4" style="margin-top: 10px"
                >{{ $t("coinRelease.a314.流动性占比") }}:</el-col
              >

              <el-col :xs="16" :sm="8" style="margin-top: 10px">
                <el-input
                  v-model="form.liquidityPct"
                  @keyup.native="
                    form.liquidityPct = checkDecimal(form.liquidityPct)
                  "
                  placeholder="0.01"
                >
                  <template slot="append">%</template>
                </el-input>
              </el-col>

              <el-col :xs="8" :sm="4" style="margin-top: 10px"
                >{{ $t("coinRelease.a314.冷却时间") }}:</el-col
              >

              <el-col :xs="16" :sm="8" style="margin-top: 10px">
                <el-input
                  v-model="form.cooldownSec"
                  type="number"
                  oninput="value=value.replace(/[^0-9.]/g,'')"
                  placeholder="1"
                >
                </el-input>
              </el-col>
            </el-row>
          </el-form-item>
          <el-form-item :label="$t('coinRelease.common.买入税率')">
            <el-row
              >{{ getBuyFee }} %
              <el-tooltip placement="top" effect="light">
                <div style="font-size: 14px; font-weight: 300" slot="content">
                  {{ $t("coinRelease.common.税率说明") }}<br /><br />

                  {{ $t("coinRelease.common.买入营销税率") }}:
                  {{
                    $t(
                      "coinRelease.a314.交易中指定额度的BNB将自动转入营销钱包中用于项目方做其他营销"
                    )
                  }}<br /><br />

                  {{ $t("coinRelease.common.买入销毁税率") }}:
                  {{
                    $t(
                      "coinRelease.a314.交易中指定额度的代币将会被打入黑洞地址实现通缩机制"
                    )
                  }}<br /><br />

                  {{
                    $t("coinRelease.a314.买入总税率不能超过交易总税率不能超过")
                  }}<br />
                </div>
                <i class="el-icon-warning-outline" />
              </el-tooltip>
            </el-row>
            <el-row :gutter="20">
              <el-col :xs="6" :sm="3" style="margin-top: 10px">
                {{ $t("coinRelease.common.买入营销税率") }}:</el-col
              >

              <el-col :xs="18" :sm="9" style="margin-top: 10px">
                <el-input
                  v-model="form._buyFundFee"
                  @keyup.native="
                    form._buyFundFee = checkDecimal(form._buyFundFee)
                  "
                  placeholder="0.01"
                >
                  <template slot="append">%</template>
                </el-input>
              </el-col>

              <el-col :xs="6" :sm="3" style="margin-top: 10px"
                >{{ $t("coinRelease.common.买入销毁税率") }}:</el-col
              >

              <el-col :xs="18" :sm="9" style="margin-top: 10px">
                <el-input
                  v-model="form.buy_burnFee"
                  @keyup.native="
                    form.buy_burnFee = checkDecimal(form.buy_burnFee)
                  "
                  placeholder="0.01"
                >
                  <template slot="append">%</template>
                </el-input>
              </el-col>
            </el-row>
          </el-form-item>
          <el-form-item :label="$t('coinRelease.common.卖出税率')">
            <el-row
              >{{ getSellFee }} %
              <el-tooltip placement="top" effect="light">
                <div style="font-size: 14px; font-weight: 300" slot="content">
                  {{ $t("coinRelease.common.税率说明") }}<br /><br />

                  {{ $t("coinRelease.common.营销税率") }}:
                  {{
                    $t(
                      "coinRelease.a314.交易中指定额度的BNB将自动转入营销钱包中用于项目方做其他营销"
                    )
                  }}<br /><br />

                  {{ $t("coinRelease.common.销毁税率") }}:
                  {{
                    $t(
                      "coinRelease.a314.交易中指定额度的代币将会被打入黑洞地址实现通缩机制"
                    )
                  }}<br /><br />

                  {{
                    $t("coinRelease.a314.买入总税率不能超过交易总税率不能超过")
                  }}<br />
                </div>
                <i class="el-icon-warning-outline" />
              </el-tooltip>
            </el-row>
            <el-row :gutter="20">
              <el-col :xs="6" :sm="3" style="margin-top: 10px">
                {{ $t("coinRelease.common.营销税率") }}:</el-col
              >

              <el-col :xs="18" :sm="9" style="margin-top: 10px">
                <el-input
                  v-model="form._sellFundFee"
                  @keyup.native="
                    form._sellFundFee = checkDecimal(form._sellFundFee)
                  "
                  placeholder="0.01"
                >
                  <template slot="append">%</template>
                </el-input>
              </el-col>

              <el-col :xs="6" :sm="3" style="margin-top: 10px"
                >{{ $t("coinRelease.common.销毁税率") }}:</el-col
              >

              <el-col :xs="18" :sm="9" style="margin-top: 10px">
                <el-input
                  v-model="form.sell_burnFee"
                  @keyup.native="
                    form.sell_burnFee = checkDecimal(form.sell_burnFee)
                  "
                  placeholder="0.01"
                >
                  <template slot="append">%</template>
                </el-input>
              </el-col>
            </el-row>
          </el-form-item>

          <el-form-item :label="$t('coinRelease.common.营销钱包')">
            <el-input v-model="form._fundAddress" placeholder="0x">
              <el-button
                slot="append"
                @click="form._fundAddress = store.state.user.address"
                >{{ $t("coinRelease.common.使用当前钱包") }}</el-button
              >
            </el-input>
            <p
              style="
                font-size: 12px;
                font-weight: 300;
                margin-top: -10px;
                margin-bottom: -15px;
              "
            >
              {{
                $t(
                  "coinRelease.a314.仅营销钱包有权限添加撤出流动性延长锁池时间"
                )
              }}
            </p>
          </el-form-item>
          <el-form-item :label="$t('coinRelease.common.最大持币量')">
            <el-switch v-model="form.enableWalletLimit"></el-switch>
            <el-input
              v-show="form.enableWalletLimit"
              v-model="form.maxWalletAmount"
              oninput="value=value.replace(/[^0-9.]/g,'')"
              placeholder="5"
            />
            <p
              style="
                font-size: 12px;
                font-weight: 300;
                margin-top: -10px;
                margin-bottom: -15px;
              "
            >
              {{
                $t(
                  "coinRelease.common.可设置单个钱包持有的最大代币数量此开关关闭后无法再次开启"
                )
              }}
            </p>
          </el-form-item>
        </el-form>

        <el-button type="primary" @click="getReadytoCreate">{{
          $t("coinRelease.common.创建合约")
        }}</el-button>
        <span style="font-size: 12px; margin-left: 10px"
          >{{ $t("coinRelease.common.费用") }}:{{ getCostAndSymbol }}</span
        >
        <el-dialog
          :title="$t('coinRelease.common.创建合约')"
          :visible.sync="dialogVisible"
          show-close
        >
          <el-collapse v-model="activeNames">
            <el-collapse-item
              :title="$t('coinRelease.common.合约地址')"
              name="1"
            >
              <div style="font-size: 14px">
                {{ $t("coinRelease.common.预计生成地址") }}:
              </div>
              <div style="font-size: 16px; margin-top: 10px">
                {{ coinAddress }}
              </div>

              <div>
                {{ $t("coinRelease.common.预估手续费") }}:<span
                  style="color: red"
                  >{{ gasFee }}</span
                >,{{ $t("coinRelease.common.请确保钱包余额充足余额不足将")
                }}<span style="color: red">{{
                  $t("coinRelease.common.创建失败")
                }}</span
                >！
              </div>
            </el-collapse-item>
            <el-collapse-item
              :title="$t('coinRelease.common.开源参数')"
              name="2"
            >
              <div class="flex-container">
                <el-tag type="success">Optimization: YES</el-tag>
                <el-tag style="margin-left: 5px"> Runs: 200</el-tag>
              </div>
              <div class="flex-container">
                <el-tag type="success">Solidity Version: 0.8.18</el-tag>
                <el-tag style="margin-left: 5px"> License: MIT</el-tag>
              </div>
              <div class="flex-container">
                <el-link
                  icon="el-icon-discover"
                  :href="this.scanURL + coinAddress"
                  target="_blank"
                  >{{ $t("coinRelease.common.浏览器查看") }}</el-link
                >
                <el-link
                  style="margin-left: 10px"
                  icon="el-icon-video-play"
                  :href="tutorialLink"
                  target="_blank"
                  >{{ $t("coinRelease.common.开源教程") }}</el-link
                >
              </div>
              <div class="flex-container">
                <el-button
                  type="info"
                  size="small"
                  plain
                  @click="copy_str(threeOneFourCode)"
                  >{{ $t("coinRelease.common.复制源代码") }}</el-button
                >
                <el-button
                  type="info"
                  size="small"
                  plain
                  @click="copy_str(constructorArgs)"
                  >{{ $t("coinRelease.common.复制构造参数") }}</el-button
                >
              </div>
              <p>
                {{
                  $t(
                    "coinRelease.common.构造参数无法找回若不立即开源请复制后保存到本地文档"
                  )
                }}
              </p>
            </el-collapse-item>
          </el-collapse>
          <span slot="footer" class="dialog-footer">
            <el-button v-show="console" @click="enterConsole">{{
              $t("coinRelease.common.进入控制台")
            }}</el-button>
            <el-button v-if="loading" type="primary" :loading="true">{{
              $t("coinRelease.common.创建合约")
            }}</el-button>
            <el-button v-else-if="console" type="primary" disabled>{{
              $t("coinRelease.common.创建合约")
            }}</el-button>
            <el-button v-else type="primary" @click="onSubmit">{{
              $t("coinRelease.common.创建合约")
            }}</el-button>
          </span>
        </el-dialog>
        <!-- <el-button @click="onCancel">Vertify</el-button> -->
      </el-main>
    </div>
  </div>
</template>

<script>
import CoinData from "@/contracts/standardCoin.json";
import chainParams from "@/contracts/coinReleaseParams.json";
import { threeOneFourCode } from "@/contracts/sourceCode.js";
import Mode1 from "@/contracts/pandaMode1.json";
import store from "@/store";
const { ethers, BigNumber } = require("ethers");
const supportChain = [97, 56, 84532, 8453];
export default {
  name: "Dashboard",
  data() {
    return {
      CoinData,
      threeOneFourCode,
      Mode1,
      store,
      supportChain,
      support: null,
      activeNames: ["1", "2"],
      ModeAddress: null,
      scanURL: null,
      dialogVisible: false,
      form: {
        _name: null,
        _symbol: null,
        _supply: null,
        _decimals: 18,

        _buyFundFee: null,
        buy_burnFee: null,

        _sellFundFee: null,
        sell_burnFee: null,

        liquidityPct: null,
        cooldownSec: null,
        _fundAddress: null,

        enableWalletLimit: false,
        maxWalletAmount: 0,
        bigMaxWalletAmount: null,
        bigSupply: null,
      },

      currencyOptions: [],
      swapOptions: [],
      loading: false,
      console: false,
      Fee: null,
      value: null,
      chainSymbol: null,
      gasFee: null,
      salt: null,
      coinAddress: null,
      tutorialLink:
        this.$i18n.locale == "en-US"
          ? "https://help.pandatool.org/english/createtoken/verify-and-publish"
          : "https://help.pandatool.org/createtoken/verify-and-publish",
      helpURL:
        this.$i18n.locale == "en-US"
          ? "https://help.pandatool.org/english/createtoken/314"
          : "https://help.pandatool.org/createtoken/314",
      constructorArgs: null,
    };
  },

  created() {
    let Inval = setInterval(() => {
      if (store.state.user.chainId) {
        window.clearInterval(Inval);
        const chainId = store.state.user.chainId;
        if (supportChain.includes(chainId)) {
          this.support = false;
        } else {
          this.support = true;
        }
        this.scanURL = chainParams[chainId][0];
        const selectChainParams = chainParams[chainId][9];
        this.ModeAddress = ethers.utils.getAddress(
          selectChainParams.ModeAddress
        );
        console.log("mode", this.ModeAddress);
        this.Fee = selectChainParams.Fee;
        this.chainSymbol = selectChainParams.chainSymbol;
      }
    }, 1000);
  },
  computed: {
    getBuyFee() {
      return Number(this.form._buyFundFee) + Number(this.form.buy_burnFee);
    },
    getSellFee() {
      return Number(this.form._sellFundFee) + Number(this.form.sell_burnFee);
    },

    getCostAndSymbol() {
      return this.Fee + " " + this.chainSymbol;
    },
  },
  methods: {
    checkParams() {
      let isFund = ethers.utils.isAddress(this.form._fundAddress);
      if (!isFund) {
        this.$message({
          type: "error",
          message: this.$t("coinRelease.common.营销钱包地址不正确"),
        });
        return false;
      }
      if (Number(this.form.liquidityPct > 100 || this.form.liquidityPct < 0)) {
        this.$message({
          type: "error",
          message: this.$t("coinRelease.a314.流动性占比需在之间"),
        });
        return false;
      }
      if (Number(this.form.cooldownSec > 60 || this.form.cooldownSec < 0)) {
        this.$message({
          type: "error",
          message: this.$t("coinRelease.a314.冷却时间不得大于60秒"),
        });
        return false;
      }
      var supplyArray = this.form._supply.toString().split(".");

      if (supplyArray.length == 1) {
        this.form.bigSupply = BigNumber.from(supplyArray[0]).mul(
          BigNumber.from(10).pow(this.form._decimals)
        );
      } else if (supplyArray.length == 2) {
        var intPart = BigNumber.from(supplyArray[0]).mul(
          BigNumber.from(10).pow(this.form._decimals)
        );
        var floatPart = BigNumber.from(supplyArray[1]).mul(
          BigNumber.from(10).pow(this.form._decimals - supplyArray[1].length)
        );
        this.form.bigSupply = intPart.add(floatPart);
      } else {
        this.$message({
          type: "danger",
          message: this.$t("coinRelease.common.初始供应量格式不正确"),
        });
        return false;
      }
      var maxWalletAmountArray = this.form.maxWalletAmount
        .toString()
        .split(".");

      if (maxWalletAmountArray.length == 1) {
        this.form.bigMaxWalletAmount = BigNumber.from(
          maxWalletAmountArray[0]
        ).mul(BigNumber.from(10).pow(this.form._decimals));
      } else if (maxWalletAmountArray.length == 2) {
        var intPart = BigNumber.from(maxWalletAmountArray[0]).mul(
          BigNumber.from(10).pow(this.form._decimals)
        );
        var floatPart = BigNumber.from(maxWalletAmountArray[1]).mul(
          BigNumber.from(10).pow(
            this.form._decimals - maxWalletAmountArray[1].length
          )
        );
        this.form.bigMaxWalletAmount = intPart.add(floatPart);
      } else {
        this.$message({
          type: "danger",
          message: this.$t("coinRelease.common.最大持币金额格式不正确"),
        });
        return false;
      }

      return true;
    },
    onSubmit() {
      this.loading = true;
      const Mode1Abi = this.Mode1;
      const provider = store.state.user.provider;
      const signer = provider.getSigner();
      const Mode1 = new ethers.Contract(this.ModeAddress, Mode1Abi, signer);
      provider.getNetwork().then((network) => {
        console.log(network.chainId);
        if (network.chainId == store.state.user.chainId) {
          this.DeployContract(Mode1, this.value)
            .then(() => {})
            .catch((error) => {
              console.log("error", error);
              this.loading = false;
              this.$message({
                type: "error",
                message: this.$t("coinRelease.common.创建失败请重试"),
              });
            });
        } else {
          this.$message({
            type: "error",
            message: this.$t(
              "coinRelease.common.公链错误请确保钱包与选择的公链一致"
            ),
          });
        }
      });
    },
    async DeployContract(Mode1, _value) {
      const stringParam = [this.form._name, this.form._symbol];
      const addressParam = [this.form._fundAddress, store.state.user.address];
      const numberParam = [
        this.form.bigSupply,
        this.form.bigMaxWalletAmount,
        Math.floor(Number(this.form._buyFundFee) * 100),
        Math.floor(Number(this.form.buy_burnFee) * 100),

        Math.floor(Number(this.form._sellFundFee) * 100),
        Math.floor(Number(this.form.sell_burnFee) * 100),
        Math.floor(Number(this.form.liquidityPct) * 100),
        Number(this.form.cooldownSec),
      ];
      const boolParam = [this.form.enableWalletLimit];
      let overrides = {
        // The maximum units of gas for the transaction to use
        // gasLimit: 50000000,

        // The price (in wei) per unit of gas
        // gasPrice: utils.parseUnits('9.0', 'gwei'),

        // The nonce to use in the transaction
        // nonce: 123,

        // The amount to send with the transaction (i.e. msg.value)
        value: _value,

        // The chain ID (or network ID) to use
        // chainId: 1
      };
      // 常见合约工厂实例
      console.log("stringParam", stringParam);
      console.log("addressParam", addressParam);
      console.log("numberParam", numberParam);
      console.log("boolParam", boolParam);

      const contract = await Mode1.CreateContract(
        this.salt,
        stringParam,
        addressParam,
        numberParam,
        boolParam,
        overrides
      );
      console.log("result", contract.hash);
      console.log("contract", contract);

      await contract.wait();
      this.loading = false;
      this.console = true;
      this.$message({
        type: "success",
        message: this.$t("coinRelease.common.创建成功"),
      });
    },

    onCancel() {
      console.log("sss", standard);
      // console.log('chainId',store.state.user.chainID)
      // console.log('address',store.state.user.chainName)
    },
    async getReadytoCreate() {
      if (this.checkParams()) {
        var FeeArray = this.Fee.toString().split(".");
        var intPart = BigNumber.from(FeeArray[0]).mul(
          BigNumber.from(10).pow(18)
        );
        var floatPart = BigNumber.from(FeeArray[1]).mul(
          BigNumber.from(10).pow(18 - FeeArray[1].length)
        );
        this.value = intPart.add(floatPart);
        const encodedbytes = this.CoinData.Coin9encodebytes;
        this.salt = ethers.utils.id("pandatoken");
        console.log("salt", this.salt);
        const stringParam = [this.form._name, this.form._symbol];
        const addressParam = [this.form._fundAddress, store.state.user.address];
        const numberParam = [
          this.form.bigSupply,
          this.form.bigMaxWalletAmount,
          Math.floor(Number(this.form._buyFundFee) * 100),
          Math.floor(Number(this.form.buy_burnFee) * 100),

          Math.floor(Number(this.form._sellFundFee) * 100),
          Math.floor(Number(this.form.sell_burnFee) * 100),
          Math.floor(Number(this.form.liquidityPct) * 100),
          Number(this.form.cooldownSec),
        ];
        const boolParam = [this.form.enableWalletLimit];

        var abiCoder = new ethers.utils.AbiCoder();
        this.constructorArgs = abiCoder
          .encode(
            ["string[]", "address[]", "uint256[]", "bool[]"],
            [stringParam, addressParam, numberParam, boolParam]
          )
          .slice(2);
        // console.log("encodeABI", this.constructorArgs);

        const initCode = encodedbytes + this.constructorArgs;

        // console.log("initCode", initCode);
        const initCodeHash = ethers.utils.keccak256(initCode);
        // console.log("initCodeHash", initCodeHash);
        this.coinAddress = ethers.utils.getCreate2Address(
          this.ModeAddress,
          this.salt,
          initCodeHash
        );

        console.log(this.coinAddress);

        const Mode1Abi = this.Mode1;
        const provider = store.state.user.provider;

        const Mode1 = new ethers.Contract(this.ModeAddress, Mode1Abi, provider);
        let overrides = { value: this.value };
        console.log("value", overrides);
        const GasPrice = await provider.getGasPrice();
        const estimateGas = await Mode1.estimateGas.CreateContract(
          this.salt,
          stringParam,
          addressParam,
          numberParam,
          boolParam
        );
        console.log(this.Fee);
        this.gasFee = parseFloat(
          new Number(GasPrice.mul(estimateGas)) / 10 ** 18 + Number(this.Fee)
        ).toFixed(6);
        console.log("gasfee", this.gasFee);
        this.dialogVisible = true;
      }
    },
    enterConsole() {
      this.$router.push({
        path: "/coinrelease/314Detail/",
        query: {
          coinAddress: this.coinAddress,
          modeType: this.$t("coinRelease.modeType.三14协议"),
        },
      });
    },

    copy_str(text) {
      if (navigator.clipboard) {
        // clipboard api 复制
        navigator.clipboard.writeText(text);
      } else {
        var textarea = document.createElement("textarea");
        document.body.appendChild(textarea);
        // 隐藏此输入框
        textarea.style.position = "fixed";
        textarea.style.clip = "rect(0 0 0 0)";
        textarea.style.top = "10px";
        // 赋值
        textarea.value = text;
        // 选中
        textarea.select();
        // 复制
        document.execCommand("copy", true);
        // 移除输入框
        document.body.removeChild(textarea);
      }
      this.$message({
        type: "success",
        message: this.$t("coinRelease.common.已复制"),
      });
    },
    checkDecimal(num) {
      var str = num;
      var len1 = str.substr(0, 1);
      var len2 = str.substr(1, 1);
      //如果第一位是0，第二位不是点，就用数字把点替换掉
      if (str.length > 1 && len1 == 0 && len2 != ".") {
        str = str.substr(1, 1);
      }
      //第一位不能是.
      if (len1 == ".") {
        str = "";
      }
      //限制只能输入一个小数点
      if (str.indexOf(".") != -1) {
        var str_ = str.substr(str.indexOf(".") + 1);
        if (str_.indexOf(".") != -1) {
          str = str.substr(0, str.indexOf(".") + str_.indexOf(".") + 1);
        }
      }
      //正则替换
      str = str.replace(/[^\d^\.]+/g, ""); // 保留数字和小数点
      str = str.replace(/\.\d\d\d$/, ""); // 小数点后只能输两位
      return str;
    },
  },
};
</script>

<style scoped>
@media screen and (max-width: 500px) {
}
@media screen and (min-width: 500px) {
  .el-input {
    width: 80%;
  }
  .el-select {
    width: 80%;
  }
}
</style>
