<template>
  <div class="app-container">
    <div class="inner">
      <el-header class="header">
        <p>
          {{$t("coinRelease.common.代币详情")}}
          <i
            class="el-icon-copy-document"
            style="
              margin-left: 10px;
              font-size: 12px;
              font-weight: 400;
              color: #409eff;
            "
            @click="copy_str(url)"
            >{{$t("coinRelease.common.复制链接")}}</i
          >
        </p>
      </el-header>
      <el-main class="main">
        <el-row :gutter="24">
          <el-col :xs="24" :sm="18">
            <el-card shadow="always" class="box-card">
              <div slot="header" class="clearfix">
                <span>{{$t('coinRelease.common.基本信息')}}</span>
              </div>
              <el-descriptions size="medium" :column="isMobile ? 1 : 2" border>
                <el-descriptions-item :label="$t('coinRelease.common.全称')">{{
                  basicParams._name
                }}</el-descriptions-item>
                <el-descriptions-item :label="$t('coinRelease.common.简称')">{{
                  basicParams._symbol
                }}</el-descriptions-item>
                <el-descriptions-item :label="$t('coinRelease.common.精度')">{{
                  basicParams._decimals
                }}</el-descriptions-item>
                <el-descriptions-item :label="$t('coinRelease.common.总量')">{{
                  getSupply
                }}</el-descriptions-item>
                <el-descriptions-item
                  :label="$t('coinRelease.common.代币模版')"
                  >{{ modeType }}</el-descriptions-item
                >
                <el-descriptions-item :label="$t('coinRelease.common.所有者')">
                  {{ getOwner }}
                  <i
                    class="el-icon-copy-document"
                    @click="copy_str(basicParams._owner)"
                  ></i>
                </el-descriptions-item>
                <el-descriptions-item :label="$t('coinRelease.common.合约地址')">
                  {{ getContract }}
                  <i
                    class="el-icon-copy-document"
                    @click="copy_str(tokenAddress)"
                  ></i>
                </el-descriptions-item>
              </el-descriptions>
            </el-card>

            <el-card shadow="always" class="box-card">
              <div slot="header" class="clearfix">
                <span>{{ $t("coinRelease.common.经济模型") }}</span>
              </div>
              <el-descriptions
                size="medium"
                :column="1"
                border
                :contentStyle="CS"
                :label-style="LS"
              >
                <el-descriptions-item :label="$t('coinRelease.common.交易所')">
                  {{ getSwap }}
                  <i
                    class="el-icon-copy-document"
                    @click="copy_str(ecoParams._swapRouter)"
                  ></i>
                </el-descriptions-item>
                <el-descriptions-item :label="$t('coinRelease.common.池子地址')">
                  {{ getPool }}
                  <i
                    class="el-icon-copy-document"
                    @click="copy_str(ecoParams._mainPair)"
                  ></i>
                </el-descriptions-item>
                <el-descriptions-item
                  :label="$t('coinRelease.common.底池代币')"
                >
                  {{ getCurrency }}
                  <i
                    class="el-icon-copy-document"
                    @click="copy_str(ecoParams._currency)"
                  ></i>
                </el-descriptions-item>

                <el-descriptions-item :label="$t('coinRelease.common.买入税率')"
                  >{{ getBuyFee }}% ({{ $t("coinRelease.common.营销")
                  }}{{ ecoParams._buyFundFee }}% +
                  {{ $t("coinRelease.common.销毁")
                  }}{{ ecoParams.buy_burnFee }}% +
                  {{ $t("coinRelease.common.回流") }}{{ ecoParams._buyLPFee }}%
                  + {{$t('coinRelease.common.分红')}}{{ ecoParams._buyReflectFee }}%)
                </el-descriptions-item>
                <el-descriptions-item :label="$t('coinRelease.common.卖出税率')"
                  >{{ getSellFee }}% ({{ $t("coinRelease.common.营销")
                  }}{{ ecoParams._sellFundFee }}% +
                  {{ $t("coinRelease.common.销毁")
                  }}{{ ecoParams.sell_burnFee }}% +
                  {{ $t("coinRelease.common.回流") }}{{ ecoParams._sellLPFee }}%
                  + {{$t('coinRelease.common.分红')}}{{ ecoParams._sellReflectFee }}%)
                </el-descriptions-item>
              </el-descriptions>
            </el-card>
          </el-col>
        </el-row>
      </el-main>
    </div>
  </div>
</template>

<script>
import store from "@/store";
import chainParams from "@/contracts/coinReleaseParams.json";
import Mode1 from "@/contracts/pandaMode1.json";
import standardData from "@/contracts/standardCoin.json";
const { ethers, BigNumber } = require("ethers");
export default {
  name: "Dashboard",
  data() {
    return {
      standardData,
      chainParams,
      Mode1,
      ModeType: null,
      url: null,
      tokenAddress: null,
      tokenABI: null,

      basicParams: {
        _name: null,
        _symbol: null,
        _decimals: null,
        _supply: null,
        _owner: null,
      },
      ecoParams: {
        _swapRouter: null,
        _currency: null,
        _mainPair: null,

        _interestStartTime: null,
        _days: null,
        oneday: null,
        _interestRate: null,

        _buyFee: null,
        _buyFundFee: null,
        _buyLPFee: null,
        _buyReflectFee: null,

        buy_burnFee: null,

        _sellFundFee: null,
        _sellLPFee: null,
        _sellReflectFee: null,
        sell_burnFee: null,
        airdropNumbs: null,

        fundAddress: null,
      },
      modeType: null,

      currencyOptions: [],
      swapOptions: [],

      CS: {
        "text-align": "left",
        "min-width": "120px",
        "word-break": "break-all",
      },
      LS: {
        "text-align": "center",
        height: "40px",
        "max-width": "70px",
        "word-break": "break-all",
      },
    };
  },

  created() {
    this.tokenAddress = this.$route.query.coinAddress;
    this.modeType = this.$route.query.modeType;
    this.url = window.location.href;
    this.tokenABI = standardData.Coin6abi;
    const provider = store.state.user.provider;
    const signer = provider.getSigner();

    const tokenContract = new ethers.Contract(
      this.tokenAddress,
      this.tokenABI,
      signer
    );
    this.getBasicParams(tokenContract);
    let Inval = setInterval(() => {
      if (store.state.user.chainId) {
        window.clearInterval(Inval);
        const chainId = store.state.user.chainId;

        const selectChainParams = chainParams[chainId][5];

        for (let i = 0; i < selectChainParams.currencyOptions.length; i++) {
          var temAddress = ethers.utils.getAddress(
            selectChainParams.currencyOptions[i].value
          );
          var temParam = {
            label: selectChainParams.currencyOptions[i].label,
            value: temAddress,
          };
          this.currencyOptions.push(temParam);
        }
        for (let i = 0; i < selectChainParams.swapOptions.length; i++) {
          var temAddress = ethers.utils.getAddress(
            selectChainParams.swapOptions[i].value
          );
          var temParam = {
            label: selectChainParams.swapOptions[i].label,
            value: temAddress,
          };
          this.swapOptions.push(temParam);
        }
      }
    }, 1000);
    this.getEcoParams(tokenContract);
  },
  computed: {
    isMobile() {
      if (window.screen.width < 500) {
        return true;
      } else {
        return false;
      }
    },

    getBuyFee() {
      return (
        Number(this.ecoParams._buyLPFee) +
        Number(this.ecoParams._buyFundFee) +
        Number(this.ecoParams.buy_burnFee) +
        Number(this.ecoParams._buyReflectFee)
      );
    },
    getSellFee() {
      return (
        Number(this.ecoParams._sellLPFee) +
        Number(this.ecoParams._sellFundFee) +
        Number(this.ecoParams.sell_burnFee) +
        Number(this.ecoParams._sellReflectFee)
      );
    },
    getSupply() {
      if (this.basicParams._supply) {
        return parseFloat(
          new Number(this.basicParams._supply.toString()) /
            10 ** this.basicParams._decimals
        ).toFixed(6);
      } else {
        return null;
      }
    },

    getOwner() {
      if (this.basicParams._owner) {
        return (
          this.basicParams._owner.substring(0, 5) +
          "..." +
          this.basicParams._owner.substring(37)
        );
      } else {
        return null;
      }
    },
    getContract() {
      if (this.tokenAddress) {
        return (
          this.tokenAddress.substring(0, 5) +
          "..." +
          this.tokenAddress.substring(37)
        );
      } else {
        return null;
      }
    },
    getSwap() {
      if (this.ecoParams._swapRouter) {
        for (let i = 0; i < this.swapOptions.length; i++) {
          if (
            ethers.utils.getAddress(this.ecoParams._swapRouter) ==
            ethers.utils.getAddress(this.swapOptions[i].value)
          ) {
            return this.swapOptions[i].label;
          }
        }
        return (
          this.ecoParams._swapRouter.substring(0, 5) +
          "..." +
          this.ecoParams._swapRouter.substring(37)
        );
      } else {
        return null;
      }
    },
    getCurrency() {
      if (this.ecoParams._currency) {
        for (let i = 0; i < this.currencyOptions.length; i++) {
          if (
            ethers.utils.getAddress(this.ecoParams._currency) ==
            ethers.utils.getAddress(this.currencyOptions[i].value)
          ) {
            return this.currencyOptions[i].label;
          }
        }
        return (
          this.ecoParams._currency.substring(0, 5) +
          "..." +
          this.ecoParams._currency.substring(37)
        );
      } else {
        return null;
      }
    },
    getPool() {
      if (this.ecoParams._mainPair) {
        return (
          this.ecoParams._mainPair.substring(0, 5) +
          "..." +
          this.ecoParams._mainPair.substring(37)
        );
      } else {
        return null;
      }
    },
    getFund() {
      if (this.ecoParams.fundAddress) {
        return (
          this.ecoParams.fundAddress.substring(0, 5) +
          "..." +
          this.ecoParams.fundAddress.substring(37)
        );
      } else {
        return null;
      }
    },
    getAirdropNumbs() {
      if (this.ecoParams.airdropNumbs) {
        return this.ecoParams.airdropNumbs;
      } else {
        return null;
      }
    },
  },

  methods: {
    getBasicParams(contract) {
      contract
        .owner()
        .then((owner) => {
          this.basicParams._owner = owner;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t('coinRelease.common.获取合约拥有者错误请检查网络'),
          });
        });

      contract
        .name()
        .then((name) => {
          this.basicParams._name = name;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t('coinRelease.common.获取名称错误请检查网络'),
          });
        });
      contract
        .symbol()
        .then((symbol) => {
          this.basicParams._symbol = symbol;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t('coinRelease.common.获取简称错误请检查网络'),
          });
        });
      contract
        .decimals()
        .then((decimals) => {
          this.basicParams._decimals = decimals;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t('coinRelease.common.获取精度错误请检查网络'),
          });
        });
      contract
        .totalSupply()
        .then((supply) => {
          console.log("supply", typeof supply);
          this.basicParams._supply = supply;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t('coinRelease.common.获取供应量错误请检查网络'),
          });
        });
    },
    getEcoParams(contract) {
      contract
        ._swapRouter()
        .then((_swapRouter) => {
          this.ecoParams._swapRouter = _swapRouter;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t('coinRelease.common.获取交易所错误请检查网络'),
          });
        });
      contract
        ._mainPair()
        .then((_mainPair) => {
          this.ecoParams._mainPair = _mainPair;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t('coinRelease.common.获取池子地址错误请检查网络'),
          });
        });
      contract
        .currency()
        .then((_currency) => {
          this.ecoParams._currency = _currency;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t('coinRelease.common.获取底池代币错误请检查网络'),
          });
        });

      contract
        ._buyFundFee()
        .then((_buyFundFee) => {
          this.ecoParams._buyFundFee = _buyFundFee / 100;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t('coinRelease.common.获取买入营销费率错误请检查网络'),
          });
        });
      contract
        ._buyLPFee()
        .then((_buyLPFee) => {
          this.ecoParams._buyLPFee = _buyLPFee / 100;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t('coinRelease.common.获取买入回流费率错误请检查网络'),
          });
        });
      contract
        ._buyReflectFee()
        .then((_buyReflectFee) => {
          this.ecoParams._buyReflectFee = _buyReflectFee / 100;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t('coinRelease.common.获取买入分红费率错误请检查网络'),
          });
        });

      contract
        .buy_burnFee()
        .then((buy_burnFee) => {
          this.ecoParams.buy_burnFee = buy_burnFee / 100;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t('coinRelease.common.获取买入销毁费率错误请检查网络'),
          });
        });
      contract
        ._sellFundFee()
        .then((_sellFundFee) => {
          this.ecoParams._sellFundFee = _sellFundFee / 100;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t('coinRelease.common.获取卖出营销费率错误请检查网络'),
          });
        });
      contract
        ._sellLPFee()
        .then((_sellLPFee) => {
          this.ecoParams._sellLPFee = _sellLPFee / 100;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t('coinRelease.common.获取卖出回流费率错误请检查网络'),
          });
        });
      contract
        ._sellReflectFee()
        .then((_sellReflectFee) => {
          this.ecoParams._sellReflectFee = _sellReflectFee / 100;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t('coinRelease.common.获取买入分红费率错误请检查网络'),
          });
        });

      contract
        .sell_burnFee()
        .then((sell_burnFee) => {
          this.ecoParams.sell_burnFee = sell_burnFee / 100;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t('coinRelease.common.获取卖出销毁费率错误请检查网络'),
          });
        });

      contract
        .fundAddress()
        .then((fundAddress) => {
          this.ecoParams.fundAddress = fundAddress;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t('coinRelease.common.获取营销钱包错误请检查网络'),
          });
        });
    },

    copy_str(text) {
      if (navigator.clipboard) {
        // clipboard api 复制
        navigator.clipboard.writeText(text);
      } else {
        var textarea = document.createElement("textarea");
        document.body.appendChild(textarea);
        // 隐藏此输入框
        textarea.style.position = "fixed";
        textarea.style.clip = "rect(0 0 0 0)";
        textarea.style.top = "10px";
        // 赋值
        textarea.value = text;
        // 选中
        textarea.select();
        // 复制
        document.execCommand("copy", true);
        // 移除输入框
        document.body.removeChild(textarea);
      }
      this.$message({
        type: "success",
        message: this.$t('coinRelease.common.已复制'),
      });
    },
  },
};
</script>

<style scoped>
@media screen and (max-width: 500px) {
  .main {
    text-align: center;
    margin-top: 5%;
    padding-left: 0;
    padding-right: 0;
  }
  .row {
    width: 100%;
    margin-bottom: 10px;
  }
  .box-card {
    margin-bottom: 20px;
    text-align: center;
    width: 100%;
  }
  .controlbutton {
    margin-bottom: 10px;
  }
}
@media screen and (min-width: 500px) {
  .header {
    text-align: center;
    margin-top: 0px;
    font-size: larger;
  }
  .main {
    /* display: flexbox;
    justify-content: center; */
    margin-top: 0%;
    padding-left: 5%;
    padding-right: 5%;
  }
  .row {
    width: 100%;
    margin-bottom: 10px;
  }

  .col {
    /* margin-left: 20px; */
    border-radius: 4px;
  }
  .box-card {
    margin-bottom: 20px;
    text-align: center;
    width: 100%;
  }
  .controlbutton {
    margin-bottom: 10px;
  }
}
</style>
