<template>
  <div class="app-container">
    <div class="inner">
      <el-header class="header">
        <p>
          {{ $t("coinRelease.common.代币详情") }}
          <i
            class="el-icon-copy-document"
            style="
              margin-left: 10px;
              font-size: 12px;
              font-weight: 400;
              color: #409eff;
            "
            @click="copy_str(url)"
            >{{ $t("coinRelease.common.复制链接") }}</i
          >
        </p>
      </el-header>
      <el-main class="main">
        <el-row :gutter="24">
          <el-col :xs="24" :sm="18">
            <el-card shadow="always" class="box-card">
              <div slot="header" class="clearfix">
                <span>{{ $t("coinRelease.common.基本信息") }}</span>
              </div>
              <el-descriptions size="medium" :column="isMobile ? 1 : 2" border>
                <el-descriptions-item :label="$t('coinRelease.common.全称')">{{
                  basicParams._name
                }}</el-descriptions-item>
                <el-descriptions-item :label="$t('coinRelease.common.简称')">{{
                  basicParams._symbol
                }}</el-descriptions-item>
                <el-descriptions-item :label="$t('coinRelease.common.精度')">{{
                  basicParams._decimals
                }}</el-descriptions-item>
                <el-descriptions-item :label="$t('coinRelease.common.总量')">{{
                  getSupply
                }}</el-descriptions-item>
                <el-descriptions-item
                  :label="$t('coinRelease.common.代币模版')"
                  >{{ modeType }}</el-descriptions-item
                >
                <el-descriptions-item :label="$t('coinRelease.common.所有者')">
                  {{ getOwner }}
                  <i
                    class="el-icon-copy-document"
                    @click="copy_str(basicParams._owner)"
                  ></i>
                </el-descriptions-item>
                <el-descriptions-item
                  :label="$t('coinRelease.common.合约地址')"
                >
                  {{ getContract }}
                  <i
                    class="el-icon-copy-document"
                    @click="copy_str(tokenAddress)"
                  ></i>
                </el-descriptions-item>
              </el-descriptions>
            </el-card>

            <el-card shadow="always" class="box-card">
              <div slot="header" class="clearfix">
                <span>{{ $t("coinRelease.common.经济模型") }}</span>
              </div>
              <el-descriptions
                size="medium"
                :column="1"
                border
                :contentStyle="CS"
                :label-style="LS"
              >
                <el-descriptions-item :label="$t('coinRelease.common.交易所')">
                  {{ getSwap }}
                  <i
                    class="el-icon-copy-document"
                    @click="copy_str(ecoParams._swapRouter)"
                  ></i>
                </el-descriptions-item>
                <el-descriptions-item
                  :label="$t('coinRelease.common.池子地址')"
                >
                  {{ getPool }}
                  <i
                    class="el-icon-copy-document"
                    @click="copy_str(ecoParams._mainPair)"
                  ></i>
                </el-descriptions-item>
                <el-descriptions-item
                  :label="$t('coinRelease.common.底池代币')"
                >
                  {{ getCurrency }}
                  <i
                    class="el-icon-copy-document"
                    @click="copy_str(ecoParams._currency)"
                  ></i>
                </el-descriptions-item>
                <el-descriptions-item
                  :label="$t('coinRelease.common.分红代币')"
                >
                  {{ getRewardToken }}
                  <i
                    class="el-icon-copy-document"
                    @click="copy_str(ecoParams._rewardToken)"
                  ></i>
                </el-descriptions-item>
                <el-descriptions-item :label="$t('coinRelease.common.买入税率')"
                  >{{ getBuyFee }}% ({{ $t("coinRelease.common.营销")
                  }}{{ ecoParams._buyFundFee }}% +
                  {{ $t("coinRelease.common.回流") }}{{ ecoParams._buyLPFee }}%
                  + {{ $t("coinRelease.common.买入分红") }}
                  {{ ecoParams._buyRewardFee }}%)
                </el-descriptions-item>
                <el-descriptions-item :label="$t('coinRelease.common.卖出税率')"
                  >{{ getSellFee }}% ({{ $t("coinRelease.common.营销")
                  }}{{ ecoParams._sellFundFee }}% +
                  {{ $t("coinRelease.common.回流") }}{{ ecoParams._sellLPFee }}%
                  + {{ $t("coinRelease.common.分红") }}
                  {{ ecoParams._sellRewardFee }}% )
                </el-descriptions-item>
                <el-descriptions-item
                  :label="$t('coinRelease.common.营销钱包')"
                >
                  {{ getFund }}
                  <i
                    class="el-icon-copy-document"
                    @click="copy_str(ecoParams.fundAddress)"
                  ></i>
                </el-descriptions-item>
                <!-- <el-descriptions-item :label="$t('coinRelease.common.跟卖比例')"
                  >{{ ecoParams.numTokensSellRate }}%</el-descriptions-item
                > -->
                <el-descriptions-item
                  :label="$t('coinRelease.blackHole.最小销毁量')"
                  >{{ getMinBurnAmount }}</el-descriptions-item
                >
                <el-descriptions-item
                  :label="$t('coinRelease.common.单钱包持币上限')"
                  >{{ getMaxWalletAmount }}</el-descriptions-item
                >
              </el-descriptions>
            </el-card>
          </el-col>
          <el-col :xs="24" :sm="6">
            <el-card v-show="partShow" shadow="always" class="box-card">
              <div slot="header" class="clearfix">
                <span>{{ $t("coinRelease.common.权限控制") }}</span>
              </div>
              <el-button
                size="mini"
                plain
                @click="dialogVisible.changeOwnerVisible = true"
                >{{ $t("coinRelease.common.转让所有权") }}</el-button
              >
              <el-dialog
                :title="$t('coinRelease.common.转让所有权')"
                :visible.sync="dialogVisible.changeOwnerVisible"
                width="30%"
                center
              >
                <div>
                  <p>{{ $t("coinRelease.common.转让地址") }}</p>
                  <el-input v-model="temValue" placeholder="0x..." />
                </div>

                <span slot="footer" class="dialog-footer">
                  <el-button
                    size="mini"
                    plain
                    @click="dialogVisible.changeOwnerVisible = false"
                    >{{ $t("coinRelease.common.取消") }}</el-button
                  >
                  <el-button
                    size="mini"
                    plain
                    v-if="loading.changeOwner"
                    type="primary"
                    :loading="true"
                    >{{ $t("coinRelease.common.确定") }}</el-button
                  >
                  <el-button
                    size="mini"
                    plain
                    v-else
                    type="primary"
                    @click="transferOwnership(temValue)"
                    >{{ $t("coinRelease.common.确定") }}</el-button
                  >
                </span>
              </el-dialog>
              <el-button
                size="mini"
                v-if="loading.renounceOwner"
                :loading="true"
                plain
                >{{ $t("coinRelease.common.丢弃权限") }}</el-button
              >
              <el-button
                size="mini"
                v-else
                plain
                @click="renounceOwnership()"
                >{{ $t("coinRelease.common.丢弃权限") }}</el-button
              >
            </el-card>

            <el-card v-show="partShow" shadow="always" class="box-card">
              <div slot="header" class="clearfix">
                <span>{{ $t("coinRelease.common.交易控制") }}</span>
              </div>

              <div class="controlbutton">
                <el-button
                  size="mini"
                  v-if="loading.launch"
                  :loading="true"
                  plain
                  >{{ $t("coinRelease.common.开启交易") }}</el-button
                >
                <el-button
                  size="mini"
                  v-else-if="
                    !enable.enableOffTrade || enable.startTradeBlock > 0
                  "
                  plain
                  disabled
                  >{{ $t("coinRelease.common.开启交易") }}</el-button
                >
                <el-button size="mini" v-else plain @click="launch()">{{
                  $t("coinRelease.common.开启交易")
                }}</el-button>
              </div>
              <div class="controlbutton">
                <el-button
                  size="mini"
                  v-if="enable.enableWalletLimit"
                  plain
                  @click="dialogVisible.changeMAXHoldVisible = true"
                  >{{ $t("coinRelease.common.修改持币上限") }}</el-button
                >
                <el-button size="mini" v-else plain disabled>{{
                  $t("coinRelease.common.修改持币上限")
                }}</el-button>
                <el-dialog
                  :title="$t('coinRelease.common.修改持币上限')"
                  :visible.sync="dialogVisible.changeMAXHoldVisible"
                  width="30%"
                  center
                >
                  <div>
                    <p>{{ $t("coinRelease.common.持币上限") }}</p>
                    <el-input
                      v-model="temValue"
                      oninput="value=value.replace(/[^0-9.]/g,'')"
                      placeholder="..."
                    />
                  </div>

                  <span slot="footer" class="dialog-footer">
                    <el-button
                      @click="dialogVisible.changeMAXHoldVisible = false"
                      >{{ $t("coinRelease.common.取消") }}</el-button
                    >
                    <el-button
                      v-if="loading.changeMAXHold"
                      type="primary"
                      :loading="true"
                      >{{ $t("coinRelease.common.确定") }}</el-button
                    >
                    <el-button
                      v-else
                      type="primary"
                      @click="changeWalletLimit(temValue)"
                      >{{ $t("coinRelease.common.确定") }}</el-button
                    >
                  </span>
                </el-dialog>
                <el-button
                  size="mini"
                  v-if="loading.disableWalletLimit"
                  :loading="true"
                  plain
                  >{{ $t("coinRelease.common.关闭持币限制") }}</el-button
                >
                <el-button
                  size="mini"
                  v-else-if="!enable.enableWalletLimit"
                  plain
                  disabled
                  >{{ $t("coinRelease.common.关闭持币限制") }}</el-button
                >
                <el-button
                  size="mini"
                  v-else
                  plain
                  @click="disableWalletLimit()"
                  >{{ $t("coinRelease.common.关闭持币限制") }}</el-button
                >
              </div>
              <div class="controlbutton">
                <el-button
                  size="mini"
                  v-if="loading.setisMaxEatExempt"
                  :loading="true"
                  plain
                  >{{ $t("coinRelease.common.设置持币白名单") }}</el-button
                >
                <el-button
                  size="mini"
                  v-else-if="!enable.enableWalletLimit"
                  plain
                  disabled
                  >{{ $t("coinRelease.common.设置持币白名单") }}</el-button
                >
                <el-button
                  size="mini"
                  v-else
                  plain
                  @click="dialogVisible.setisMaxEatExempt = true"
                  >{{ $t("coinRelease.common.设置持币白名单") }}</el-button
                >
                <el-dialog
                  :title="$t('coinRelease.common.设置持币白名单')"
                  :visible.sync="dialogVisible.setisMaxEatExempt"
                  width="30%"
                  center
                >
                  <el-radio-group v-model="temBool">
                    <el-radio :label="true">{{
                      $t("coinRelease.common.添加白名单")
                    }}</el-radio>
                    <el-radio :label="false">{{
                      $t("coinRelease.common.移除白名单")
                    }}</el-radio>
                  </el-radio-group>
                  <div style="margin-top: 30px">
                    <p>{{ $t("coinRelease.common.白名单地址") }}</p>
                    <el-input v-model="temValue" placeholder="0x..." />
                  </div>

                  <span slot="footer" class="dialog-footer">
                    <el-button
                      size="mini"
                      @click="dialogVisible.setisMaxEatExempt = false"
                      >{{ $t("coinRelease.common.取消") }}</el-button
                    >
                    <el-button
                      size="mini"
                      v-if="loading.setisMaxEatExempt"
                      :loading="true"
                      type="primary"
                      plain
                      >{{ $t("coinRelease.common.确定") }}</el-button
                    >
                    <el-button
                      size="mini"
                      v-else
                      type="primary"
                      plain
                      @click="setisMaxEatExempt(temValue, temBool)"
                      >{{ $t("coinRelease.common.确定") }}</el-button
                    >
                  </span>
                </el-dialog>
              </div>

              <div class="controlbutton">
                <el-button
                  size="mini"
                  v-if="!enable.enableRewardList"
                  plain
                  disabled
                  >{{ $t("coinRelease.common.设置黑名单") }}</el-button
                >
                <el-button
                  size="mini"
                  v-else
                  plain
                  @click="dialogVisible.multi_bclistVisible = true"
                  >{{ $t("coinRelease.common.设置黑名单") }}</el-button
                >
                <el-dialog
                  :title="$t('coinRelease.common.设置黑名单')"
                  :visible.sync="dialogVisible.multi_bclistVisible"
                  width="35%"
                  center
                >
                  <el-radio-group v-model="temBool">
                    <el-radio :label="true">{{
                      $t("coinRelease.common.添加黑名单")
                    }}</el-radio>
                    <el-radio :label="false">{{
                      $t("coinRelease.common.移除黑名单")
                    }}</el-radio>
                  </el-radio-group>
                  <div style="margin-top: 30px">
                    <p>{{ $t("coinRelease.common.黑名单地址") }}</p>
                    <el-input
                      v-model="temValue"
                      type="textarea"
                      :rows="5"
                      :placeholder="
                        $t(
                          'coinRelease.common.请输入地址每行一个地址不要添加任何符号'
                        )
                      "
                    ></el-input>
                  </div>

                  <span slot="footer" class="dialog-footer">
                    <el-button
                      size="mini"
                      @click="dialogVisible.multi_bclistVisible = false"
                      >{{ $t("coinRelease.common.取消") }}</el-button
                    >
                    <el-button
                      size="mini"
                      v-if="loading.multi_bclist"
                      :loading="true"
                      type="primary"
                      plain
                      >{{ $t("coinRelease.common.确定") }}</el-button
                    >
                    <el-button
                      size="mini"
                      v-else
                      type="primary"
                      plain
                      @click="multi_bclist(temValue, temBool)"
                      >{{ $t("coinRelease.common.确定") }}</el-button
                    >
                  </span>
                </el-dialog>
              </div>
            </el-card>
            <el-card v-show="partShow" shadow="always" class="box-card">
              <div slot="header" class="clearfix">
                <span>{{ $t("coinRelease.common.税率控制") }}</span>
              </div>
              <div class="controlbutton">
                <el-button
                  size="mini"
                  v-if="!enable.enableChangeTax"
                  plain
                  disabled
                  >{{ $t("coinRelease.common.修改税率") }}</el-button
                >
                <el-button
                  size="mini"
                  v-else
                  plain
                  @click="dialogVisible.changeExTaxVisible = true"
                  >{{ $t("coinRelease.common.修改税率") }}</el-button
                >
                <el-dialog
                  :title="$t('coinRelease.common.修改税率')"
                  :visible.sync="dialogVisible.changeExTaxVisible"
                  width="30%"
                  center
                >
                  <div style="margin-top: 0px">
                    <p style="font-size: 16px">
                      {{ $t("coinRelease.common.买入税率最大25") }}
                    </p>
                    <p>{{ $t("coinRelease.common.买入回流税率") }}</p>
                    <el-input
                      size="small"
                      v-model="temTax._buyLPFee"
                      @keyup.native="
                        temTax._buyLPFee = checkDecimal(temTax._buyLPFee)
                      "
                      placeholder="0.01"
                    >
                      <template slot="append">%</template>
                    </el-input>

                    <p>{{ $t("coinRelease.common.买入营销税率") }}</p>
                    <el-input
                      size="small"
                      v-model="temTax._buyFundFee"
                      @keyup.native="
                        temTax._buyFundFee = checkDecimal(temTax._buyFundFee)
                      "
                      placeholder="0.01"
                    >
                      <template slot="append">%</template>
                    </el-input>
                    <p>{{ $t("coinRelease.common.买入分红税率") }}</p>
                    <el-input
                      size="small"
                      v-model="temTax._buyRewardFee"
                      @keyup.native="
                        temTax._buyRewardFee = checkDecimal(
                          temTax._buyRewardFee
                        )
                      "
                      placeholder="0.01"
                    >
                      <template slot="append">%</template>
                    </el-input>
                  </div>
                  <div style="margin-top: 20px">
                    <p style="font-size: 16px">
                      {{ $t("coinRelease.common.卖出税率最大25") }}
                    </p>
                    <p>{{ $t("coinRelease.common.回流税率") }}</p>
                    <el-input
                      size="small"
                      v-model="temTax._sellLPFee"
                      @keyup.native="
                        temTax._sellLPFee = checkDecimal(temTax._sellLPFee)
                      "
                      placeholder="0.01"
                    >
                      <template slot="append">%</template>
                    </el-input>
                    <p>{{ $t("coinRelease.common.营销税率") }}</p>
                    <el-input
                      size="small"
                      v-model="temTax._sellFundFee"
                      @keyup.native="
                        temTax._sellFundFee = checkDecimal(temTax._sellFundFee)
                      "
                      placeholder="0.01"
                    >
                      <template slot="append">%</template>
                    </el-input>
                    <p>{{ $t("coinRelease.common.分红税率") }}</p>
                    <el-input
                      size="small"
                      v-model="temTax._sellRewardFee"
                      @keyup.native="
                        temTax._sellRewardFee = checkDecimal(
                          temTax._sellRewardFee
                        )
                      "
                      placeholder="0.01"
                    >
                      <template slot="append">%</template>
                    </el-input>
                  </div>
                  <p>{{ $t("coinRelease.common.买卖营销税之和必须大于0") }}!</p>

                  <span slot="footer" class="dialog-footer">
                    <el-button
                      size="mini"
                      plain
                      @click="dialogVisible.changeExTaxVisible = false"
                      >{{ $t("coinRelease.common.取消") }}</el-button
                    >
                    <el-button
                      size="mini"
                      v-if="loading.changeExTax"
                      :loading="true"
                      type="primary"
                      plain
                      >{{ $t("coinRelease.common.确定") }}</el-button
                    >
                    <el-button
                      size="mini"
                      v-else
                      type="primary"
                      plain
                      @click="changeExTax(temTax)"
                      >{{ $t("coinRelease.common.确定") }}</el-button
                    >
                  </span>
                </el-dialog>
              </div>

              <div class="controlbutton">
                <el-button
                  size="mini"
                  plain
                  @click="dialogVisible.changeWhiterVisible = true"
                  >{{ $t("coinRelease.common.设置税率白名单") }}</el-button
                >
                <el-dialog
                  :title="$t('coinRelease.common.设置税率白名单')"
                  :visible.sync="dialogVisible.changeWhiterVisible"
                  width="35%"
                  center
                >
                  <el-radio-group v-model="temBool">
                    <el-radio :label="true">{{
                      $t("coinRelease.common.添加税率白名单")
                    }}</el-radio>
                    <el-radio :label="false">{{
                      $t("coinRelease.common.移除税率白名单")
                    }}</el-radio>
                  </el-radio-group>
                  <div style="margin-top: 30px">
                    <p>{{ $t("coinRelease.common.税率白名单地址") }}</p>
                    <el-input
                      v-model="temValue"
                      type="textarea"
                      :rows="5"
                      :placeholder="
                        $t(
                          'coinRelease.common.请输入地址每行一个地址不要添加任何符号'
                        )
                      "
                    ></el-input>
                  </div>

                  <span slot="footer" class="dialog-footer">
                    <el-button
                      size="mini"
                      plain
                      @click="dialogVisible.changeWhiterVisible = false"
                      >{{ $t("coinRelease.common.取消") }}</el-button
                    >
                    <el-button
                      size="mini"
                      v-if="loading.changeWhiter"
                      :loading="true"
                      type="primary"
                      plain
                      >{{ $t("coinRelease.common.确定") }}</el-button
                    >
                    <el-button
                      size="mini"
                      v-else
                      type="primary"
                      plain
                      @click="setFeeWhiteList(temValue, temBool)"
                      >{{ $t("coinRelease.common.确定") }}</el-button
                    >
                  </span>
                </el-dialog>
              </div>
              <div class="controlbutton">
                <el-button
                  size="mini"
                  plain
                  @click="dialogVisible.changeFundVisible = true"
                  >{{ $t("coinRelease.common.修改营销钱包") }}</el-button
                >
                <el-dialog
                  :title="$t('coinRelease.common.修改营销钱包')"
                  :visible.sync="dialogVisible.changeFundVisible"
                  width="30%"
                  center
                >
                  <div>
                    <p>{{ $t("coinRelease.common.营销钱包地址") }}</p>
                    <el-input v-model="temValue" placeholder="0x..." />
                  </div>

                  <span slot="footer" class="dialog-footer">
                    <el-button
                      size="mini"
                      plain
                      @click="dialogVisible.changeFundVisible = false"
                      >{{ $t("coinRelease.common.取消") }}</el-button
                    >
                    <el-button
                      size="mini"
                      v-if="loading.changeFund"
                      :loading="true"
                      type="primary"
                      plain
                      >{{ $t("coinRelease.common.确定") }}</el-button
                    >
                    <el-button
                      size="mini"
                      v-else
                      type="primary"
                      plain
                      @click="setFundAddress(temValue)"
                      >{{ $t("coinRelease.common.确定") }}</el-button
                    >
                  </span>
                </el-dialog>
              </div>
            </el-card>
            <el-card v-show="partShow" shadow="always" class="box-card">
              <div slot="header" class="clearfix">
                <span>{{ $t("coinRelease.common.分红控制") }}</span>
              </div>

              <div class="controlbutton">
                <el-button
                  size="mini"
                  plain
                  @click="dialogVisible.setClaims = true"
                  >{{ $t("coinRelease.common.提取合约内代币") }}</el-button
                >
                <el-dialog
                  :title="$t('coinRelease.common.提取合约内代币')"
                  :visible.sync="dialogVisible.setClaims"
                  width="35%"
                  center
                >
                  <el-radio-group v-model="temBool">
                    <el-radio :label="true">BNB</el-radio>
                    <el-radio :label="false">{{
                      $t("coinRelease.common.本币")
                    }}</el-radio>
                  </el-radio-group>

                  <div style="margin-top: 30px">
                    <p>{{ $t("coinRelease.common.数量") }}</p>
                    <el-input
                      v-model="temValue"
                      type="number"
                      placeholder="10"
                    ></el-input>
                  </div>

                  <span slot="footer" class="dialog-footer">
                    <el-button
                      size="mini"
                      plain
                      @click="dialogVisible.setClaims = false"
                      >{{ $t("coinRelease.common.取消") }}</el-button
                    >
                    <el-button
                      size="mini"
                      v-if="loading.setClaims"
                      :loading="true"
                      type="primary"
                      plain
                      >{{ $t("coinRelease.common.确定") }}</el-button
                    >
                    <el-button
                      size="mini"
                      v-else
                      type="primary"
                      plain
                      @click="setClaims(temValue, temBool)"
                      >{{ $t("coinRelease.common.确定") }}</el-button
                    >
                  </span>
                </el-dialog>
              </div>
              <div class="controlbutton">
                <el-button
                  size="mini"
                  plain
                  @click="dialogVisible.setExcludeHolderVisible = true"
                  >{{ $t("coinRelease.common.设置分红黑名单") }}</el-button
                >
                <el-dialog
                  :title="$t('coinRelease.common.设置分红黑名单')"
                  :visible.sync="dialogVisible.setExcludeHolderVisible"
                  width="30%"
                  center
                >
                  <div>
                    <p>{{ $t("coinRelease.common.分红黑名单地址") }}</p>
                    <el-input v-model="temValue" placeholder="0x..." />
                    <p>
                      {{
                        $t(
                          "coinRelease.blackHole.该地址将不会收到黑洞分红永久拉黑不可撤销"
                        )
                      }}
                    </p>
                  </div>

                  <span slot="footer" class="dialog-footer">
                    <el-button
                      size="mini"
                      plain
                      @click="dialogVisible.setExcludeHolderVisible = false"
                      >{{ $t("coinRelease.common.取消") }}</el-button
                    >
                    <el-button
                      size="mini"
                      v-if="loading.setExcludeHolder"
                      :loading="true"
                      type="primary"
                      plain
                      >{{ $t("coinRelease.common.确定") }}</el-button
                    >
                    <el-button
                      size="mini"
                      v-else
                      type="primary"
                      plain
                      @click="setExcludeHolder(temValue)"
                      >{{ $t("coinRelease.common.确定") }}</el-button
                    >
                  </span>
                </el-dialog>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </el-main>
    </div>
  </div>
</template>

<script>
import store from "@/store";
import chainParams from "@/contracts/coinReleaseParams.json";
import Mode1 from "@/contracts/pandaMode1.json";
import standardData from "@/contracts/standardCoin.json";
const { ethers, BigNumber } = require("ethers");
export default {
  name: "Dashboard",
  data() {
    return {
      standardData,
      chainParams,
      Mode1,
      ModeType: null,
      url: null,
      tokenAddress: null,
      tokenABI: null,
      temValue: null,
      temBool: true,

      temTax: {
        _buyFundFee: null,
        _buyLPFee: null,
        _buyRewardFee: null,

        _sellFundFee: null,
        _sellLPFee: null,
        _sellRewardFee: null,
      },
      basicParams: {
        _name: null,
        _symbol: null,
        _decimals: null,
        _supply: null,
        _owner: null,
      },
      ecoParams: {
        _swapRouter: null,
        _currency: null,
        _mainPair: null,
        _rewardToken: null,

        price: null,
        amountPerUnits: null,
        mintLimit: null,

        _buyFundFee: null,
        _buyLPFee: null,
        _buyRewardFee: null,

        _sellFundFee: null,
        _sellLPFee: null,
        _sellRewardFee: null,

        numTokensSellRate: null,
        fundAddress: null,
        minBurnAmount: null,
        maxWalletAmount: null,
      },
      modeType: null,
      partShow: true,
      dialogVisible: {
        changeOwnerVisible: false,

        changeMAXHoldVisible: false,
        setisMaxEatExempt: false,

        multi_bclistVisible: false,

        setSwapPairListVisible: false,
        changeExTaxVisible: false,
        changeWhiterVisible: false,
        changeFundVisible: false,
        claimTokenVisible: false,

        setNumTokensSellRate: false,
        setExcludeHolderVisible: false,
        setClaims: false,
      },
      loading: {
        changeOwner: false,
        renounceOwner: false,
        launch: false,

        disableWalletLimit: false,
        setisMaxEatExempt: false,
        changeAddLiquidity: false,
        changeRemoveLiquidity: false,
        multi_bclist: false,
        setkb: false,

        setSwapPairList: false,
        changeExTax: false,
        changeWhiter: false,
        changeFund: false,

        setNumTokensSellRate: false,
        setExcludeHolderVisible: false,
        setClaims: false,
      },
      enable: {
        enableOffTrade: true,
        startTradeBlock: null,
        startLPBlock: null,
        enableKillBlock: null,
        enableRewardList: null,
        enableChangeTax: null,
        airdropEnable: null,
        enableWalletLimit: null,
      },
      currencyOptions: [],
      swapOptions: [],
      rewardOptions: [],

      CS: {
        "text-align": "left",
        "min-width": "120px",
        "word-break": "break-all",
      },
      LS: {
        "text-align": "center",
        height: "40px",
        "max-width": "70px",
        "word-break": "break-all",
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now();
        },
      },
    };
  },

  created() {
    this.tokenAddress = this.$route.query.coinAddress;
    this.modeType = this.$route.query.modeType;
    this.url = window.location.href;
    this.tokenABI = standardData.Coin10abi;
    const provider = store.state.user.provider;
    const signer = provider.getSigner();

    const tokenContract = new ethers.Contract(
      this.tokenAddress,
      this.tokenABI,
      signer
    );
    this.getBasicParams(tokenContract);
    let Inval = setInterval(() => {
      if (this.basicParams._owner) {
        if (
          ethers.utils.getAddress(this.basicParams._owner) !=
          ethers.utils.getAddress(store.state.user.address)
        ) {
          this.partShow = false;
        }
      }
      if (store.state.user.chainId) {
        window.clearInterval(Inval);
        const chainId = store.state.user.chainId;
        const selectChainParams = chainParams[chainId][8];

        for (let i = 0; i < selectChainParams.rewardOptions.length; i++) {
          var temAddress = ethers.utils.getAddress(
            selectChainParams.rewardOptions[i].value
          );
          var temParam = {
            label: selectChainParams.rewardOptions[i].label,
            value: temAddress,
          };
          this.rewardOptions.push(temParam);
        }
        for (let i = 0; i < selectChainParams.currencyOptions.length; i++) {
          var temAddress = ethers.utils.getAddress(
            selectChainParams.currencyOptions[i].value
          );
          var temParam = {
            label: selectChainParams.currencyOptions[i].label,
            value: temAddress,
          };
          this.currencyOptions.push(temParam);
        }
        for (let i = 0; i < selectChainParams.swapOptions.length; i++) {
          var temAddress = ethers.utils.getAddress(
            selectChainParams.swapOptions[i].value
          );
          var temParam = {
            label: selectChainParams.swapOptions[i].label,
            value: temAddress,
          };
          this.swapOptions.push(temParam);
        }
      }
    }, 1000);
    this.getEcoParams(tokenContract);
    this.getEnableParams(tokenContract);
  },
  computed: {
    isMobile() {
      if (window.screen.width < 500) {
        return true;
      } else {
        return false;
      }
    },
    getInterestStartTime() {
      var date = new Date(this.ecoParams._interestStartTime * 1000);
      var y = date.getFullYear(),
        m = date.getMonth() + 1,
        d = date.getDate(),
        h = date.getHours(),
        i = date.getMinutes(),
        s = date.getSeconds();
      if (m < 10) {
        m = "0" + m;
      }
      if (d < 10) {
        d = "0" + d;
      }
      if (h < 10) {
        h = "0" + h;
      }
      if (i < 10) {
        i = "0" + i;
      }
      if (s < 10) {
        s = "0" + s;
      }
      var t = y + "-" + m + "-" + d + " " + h + ":" + i + ":" + s;
      return t;
    },

    getBuyFee() {
      return (
        Number(this.ecoParams._buyLPFee) +
        Number(this.ecoParams._buyFundFee) +
        Number(this.ecoParams._buyRewardFee)
      );
    },
    getSellFee() {
      return (
        Number(this.ecoParams._sellLPFee) +
        Number(this.ecoParams._sellFundFee) +
        Number(this.ecoParams._sellRewardFee)
      );
    },
    getSupply() {
      if (this.basicParams._supply) {
        return parseFloat(
          new Number(this.basicParams._supply.toString()) /
            10 ** this.basicParams._decimals
        ).toFixed(6);
      } else {
        return null;
      }
    },
    getMinBurnAmount() {
      if (this.ecoParams.minBurnAmount) {
        return parseFloat(
          new Number(this.ecoParams.minBurnAmount.toString()) /
            10 ** this.basicParams._decimals
        ).toFixed(6);
      } else {
        return this.$t("coinRelease.common.获取中");
      }
    },
    getMaxWalletAmount() {
      console.log("钱包限制", this.enable.enableWalletLimit);
      if (this.enable.enableWalletLimit) {
        if (this.ecoParams.maxWalletAmount) {
          return parseFloat(
            new Number(this.ecoParams.maxWalletAmount.toString()) /
              10 ** this.basicParams._decimals
          ).toFixed(6);
        } else {
          return this.$t("coinRelease.common.获取中");
        }
      } else {
        return this.$t("coinRelease.common.无上限");
      }
    },

    getOwner() {
      if (this.basicParams._owner) {
        return (
          this.basicParams._owner.substring(0, 5) +
          "..." +
          this.basicParams._owner.substring(37)
        );
      } else {
        return null;
      }
    },
    getContract() {
      if (this.tokenAddress) {
        return (
          this.tokenAddress.substring(0, 5) +
          "..." +
          this.tokenAddress.substring(37)
        );
      } else {
        return null;
      }
    },
    getSwap() {
      if (this.ecoParams._swapRouter) {
        for (let i = 0; i < this.swapOptions.length; i++) {
          if (
            ethers.utils.getAddress(this.ecoParams._swapRouter) ==
            ethers.utils.getAddress(this.swapOptions[i].value)
          ) {
            return this.swapOptions[i].label;
          }
        }
        return (
          this.ecoParams._swapRouter.substring(0, 5) +
          "..." +
          this.ecoParams._swapRouter.substring(37)
        );
      } else {
        return null;
      }
    },
    getCurrency() {
      if (this.ecoParams._currency) {
        for (let i = 0; i < this.currencyOptions.length; i++) {
          if (
            ethers.utils.getAddress(this.ecoParams._currency) ==
            ethers.utils.getAddress(this.currencyOptions[i].value)
          ) {
            return this.currencyOptions[i].label;
          }
        }
        return (
          this.ecoParams._currency.substring(0, 5) +
          "..." +
          this.ecoParams._currency.substring(37)
        );
      } else {
        return null;
      }
    },
    getPool() {
      if (this.ecoParams._mainPair) {
        return (
          this.ecoParams._mainPair.substring(0, 5) +
          "..." +
          this.ecoParams._mainPair.substring(37)
        );
      } else {
        return null;
      }
    },
    getRewardToken() {
      if (this.ecoParams._rewardToken) {
        for (let i = 0; i < this.rewardOptions.length; i++) {
          if (
            ethers.utils.getAddress(this.ecoParams._rewardToken) ==
            ethers.utils.getAddress(this.rewardOptions[i].value)
          ) {
            return this.rewardOptions[i].label;
          }
        }
        return (
          this.ecoParams._rewardToken.substring(0, 5) +
          "..." +
          this.ecoParams._rewardToken.substring(37)
        );
      } else {
        return null;
      }
    },
    getFund() {
      if (this.ecoParams.fundAddress) {
        return (
          this.ecoParams.fundAddress.substring(0, 5) +
          "..." +
          this.ecoParams.fundAddress.substring(37)
        );
      } else {
        return null;
      }
    },
  },

  methods: {
    checkDecimal(num) {
      var str = num;
      var len1 = str.substr(0, 1);
      var len2 = str.substr(1, 1);
      //如果第一位是0，第二位不是点，就用数字把点替换掉
      if (str.length > 1 && len1 == 0 && len2 != ".") {
        str = str.substr(1, 1);
      }
      //第一位不能是.
      if (len1 == ".") {
        str = "";
      }
      //限制只能输入一个小数点
      if (str.indexOf(".") != -1) {
        var str_ = str.substr(str.indexOf(".") + 1);
        if (str_.indexOf(".") != -1) {
          str = str.substr(0, str.indexOf(".") + str_.indexOf(".") + 1);
        }
      }
      //正则替换
      str = str.replace(/[^\d^\.]+/g, ""); // 保留数字和小数点
      str = str.replace(/\.\d\d\d$/, ""); // 小数点后只能输两位
      return str;
    },
    getBasicParams(contract) {
      contract
        .owner()
        .then((owner) => {
          this.basicParams._owner = owner;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t("coinRelease.common.获取合约拥有者错误请检查网络"),
          });
        });

      contract
        .name()
        .then((name) => {
          this.basicParams._name = name;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t("coinRelease.common.获取名称错误请检查网络"),
          });
        });
      contract
        .symbol()
        .then((symbol) => {
          this.basicParams._symbol = symbol;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t("coinRelease.common.获取简称错误请检查网络"),
          });
        });
      contract
        .decimals()
        .then((decimals) => {
          this.basicParams._decimals = decimals;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t("coinRelease.common.获取精度错误请检查网络"),
          });
        });
      contract
        .totalSupply()
        .then((supply) => {
          this.basicParams._supply = supply;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t("coinRelease.common.获取供应量错误请检查网络"),
          });
        });
    },
    getEcoParams(contract) {
      contract
        ._swapRouter()
        .then((_swapRouter) => {
          this.ecoParams._swapRouter = _swapRouter;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t("coinRelease.common.获取交易所错误请检查网络"),
          });
        });
      contract
        ._mainPair()
        .then((_mainPair) => {
          this.ecoParams._mainPair = _mainPair;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t("coinRelease.common.获取池子地址错误请检查网络"),
          });
        });
      contract
        .currency()
        .then((_currency) => {
          this.ecoParams._currency = _currency;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t("coinRelease.common.获取底池代币错误请检查网络"),
          });
        });
      contract
        ._rewardToken()
        .then((_rewardToken) => {
          this.ecoParams._rewardToken = _rewardToken;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t("coinRelease.common.获取分红代币错误请检查网络"),
          });
        });

      contract
        ._buyFundFee()
        .then((_buyFundFee) => {
          this.ecoParams._buyFundFee = _buyFundFee / 100;
          this.temTax._buyFundFee = _buyFundFee / 100;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t(
              "coinRelease.common.获取买入营销费率错误请检查网络"
            ),
          });
        });
      contract
        ._buyLPFee()
        .then((_buyLPFee) => {
          this.ecoParams._buyLPFee = _buyLPFee / 100;
          this.temTax._buyLPFee = _buyLPFee / 100;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t(
              "coinRelease.common.获取买入回流费率错误请检查网络"
            ),
          });
        });

      contract
        ._buyRewardFee()
        .then((_buyRewardFee) => {
          this.ecoParams._buyRewardFee = _buyRewardFee / 100;
          this.temTax._buyRewardFee = _buyRewardFee / 100;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t(
              "coinRelease.common.获取买入分红费率错误请检查网络"
            ),
          });
        });
      contract
        ._sellFundFee()
        .then((_sellFundFee) => {
          this.ecoParams._sellFundFee = _sellFundFee / 100;
          this.temTax._sellFundFee = _sellFundFee / 100;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t(
              "coinRelease.common.获取卖出营销费率错误请检查网络"
            ),
          });
        });
      contract
        ._sellLPFee()
        .then((_sellLPFee) => {
          this.ecoParams._sellLPFee = _sellLPFee / 100;
          this.temTax._sellLPFee = _sellLPFee / 100;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t(
              "coinRelease.common.获取卖出回流费率错误请检查网络"
            ),
          });
        });

      contract
        ._sellRewardFee()
        .then((_sellRewardFee) => {
          this.ecoParams._sellRewardFee = _sellRewardFee / 100;
          this.temTax._sellRewardFee = _sellRewardFee / 100;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t(
              "coinRelease.common.获取卖出分红费率错误请检查网络"
            ),
          });
        });
      contract
        .numTokensSellRate()
        .then((numTokensSellRate) => {
          this.ecoParams.numTokensSellRate = numTokensSellRate;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t("coinRelease.common.获取跟卖比例错误请检查网络"),
          });
        });

      contract
        .fundAddress()
        .then((fundAddress) => {
          this.ecoParams.fundAddress = fundAddress;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t("coinRelease.common.获取营销钱包错误请检查网络"),
          });
        });
      contract
        .maxWalletAmount()
        .then((maxWalletAmount) => {
          this.ecoParams.maxWalletAmount = maxWalletAmount;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t("coinRelease.common.获取最大持币量错误请检查网络"),
          });
        });
      contract
        .minBurnAmount()
        .then((minBurnAmount) => {
          this.ecoParams.minBurnAmount = minBurnAmount;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t(
              "coinRelease.blackHole.获取最小销毁量错误请检查网络"
            ),
          });
        });
    },
    getEnableParams(contract) {
      contract
        .enableOffTrade()
        .then((enableOffTrade) => {
          this.enable.enableOffTrade = enableOffTrade;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t(
              "coinRelease.common.获取手动交易开关错误请检查网络"
            ),
          });
        });
      contract
        .startTradeBlock()
        .then((startTradeBlock) => {
          this.enable.startTradeBlock = startTradeBlock;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t(
              "coinRelease.common.获取startTradeBlock错误请检查网络"
            ),
          });
        });

      contract
        .enableRewardList()
        .then((enableRewardList) => {
          this.enable.enableRewardList = enableRewardList;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t("coinRelease.common.获取黑名单开关错误请检查网络"),
          });
        });
      contract
        .enableWalletLimit()
        .then((enableWalletLimit) => {
          this.enable.enableWalletLimit = enableWalletLimit;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t(
              "coinRelease.common.获取最大持币开关错误请检查网络"
            ),
          });
        });
      contract
        .enableChangeTax()
        .then((enableChangeTax) => {
          this.enable.enableChangeTax = enableChangeTax;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t("coinRelease.common.获取税率开关错误请检查网络"),
          });
        });
    },

    transferOwnership(temValue) {
      let isAddress = ethers.utils.isAddress(temValue);
      if (isAddress) {
        this.loading.changeOwner = true;
        const provider = store.state.user.provider;
        const signer = provider.getSigner();
        const tokenContract = new ethers.Contract(
          this.tokenAddress,
          this.tokenABI,
          signer
        );
        tokenContract
          .transferOwnership(temValue)
          .then((rs) => {
            this.dialogVisible.changeOwnerVisible = false;
            this.loading.changeOwner = false;
            this.temValue = null;
            this.$message({
              type: "success",
              message: this.$t("coinRelease.common.已提交等待区块确认"),
            });

            rs.wait()
              .then(() => {
                this.$message({
                  type: "success",
                  message: this.$t("coinRelease.common.修改成功"),
                });

                this.getBasicParams(tokenContract);
                this.partShow = false;
              })
              .catch((error) => {
                console.log("错误!", error);
                this.$message({
                  type: "danger",
                  message: this.$t(
                    "coinRelease.common.确认失败请前往浏览器查看"
                  ),
                });
              });
          })
          .catch((error) => {
            console.log("错误!", error);
            this.$message({
              type: "danger",
              message: this.$t("coinRelease.common.出错了"),
            });
            this.dialogVisible.changeOwnerVisible = false;
            this.loading.changeOwner = false;
            this.temValue = null;
          });
      } else {
        this.$message({
          type: "danger",
          message: this.$t("coinRelease.common.地址格式不正确"),
        });
        this.loading.changeOwner = false;
      }
    },
    renounceOwnership() {
      this.loading.renounceOwner = true;
      const provider = store.state.user.provider;
      const signer = provider.getSigner();
      const tokenContract = new ethers.Contract(
        this.tokenAddress,
        this.tokenABI,
        signer
      );
      tokenContract
        .renounceOwnership()
        .then((rs) => {
          this.$message({
            type: "success",
            message: this.$t("coinRelease.common.已提交等待区块确认"),
          });

          rs.wait()
            .then(() => {
              this.$message({
                type: "success",
                message: this.$t("coinRelease.common.修改成功"),
              });
              this.loading.renounceOwner = false;
              this.getBasicParams(tokenContract);
              this.partShow = false;
            })
            .catch((error) => {
              console.log("错误!", error);
              this.$message({
                type: "danger",
                message: this.$t("coinRelease.common.确认失败请前往浏览器查看"),
              });
            });
        })
        .catch((error) => {
          console.log("错误!", error);
          this.$message({
            type: "danger",
            message: this.$t("coinRelease.common.出错了"),
          });

          this.loading.renounceOwner = false;
          this.temValue = null;
        });
    },

    launch() {
      this.loading.launch = true;
      const provider = store.state.user.provider;
      const signer = provider.getSigner();
      const tokenContract = new ethers.Contract(
        this.tokenAddress,
        this.tokenABI,
        signer
      );
      tokenContract
        .launch()
        .then((rs) => {
          this.$message({
            type: "success",
            message: this.$t("coinRelease.common.已提交等待区块确认"),
          });

          rs.wait()
            .then(() => {
              this.$message({
                type: "success",
                message: this.$t("coinRelease.common.修改成功"),
              });

              this.loading.launch = false;
              this.enable.startTradeBlock = 1;
            })
            .catch((error) => {
              console.log("错误!", error);
              this.$message({
                type: "danger",
                message: this.$t("coinRelease.common.确认失败请前往浏览器查看"),
              });
            });
        })
        .catch((error) => {
          console.log("错误!", error);
          this.$message({
            type: "danger",
            message: this.$t("coinRelease.common.出错了"),
          });
          this.loading.launch = false;
          this.temValue = null;
        });
    },
    changeWalletLimit(temValue) {
      this.loading.changeMAXHold = true;
      const provider = store.state.user.provider;
      const signer = provider.getSigner();
      const tokenContract = new ethers.Contract(
        this.tokenAddress,
        this.tokenABI,
        signer
      );
      var amountArray = temValue.toString().split(".");
      console.log("amountArray", amountArray);
      if (amountArray.length == 1) {
        let walletLimit = BigNumber.from(amountArray[0]).mul(
          BigNumber.from(10).pow(this.basicParams._decimals)
        );
        console.log(walletLimit);
        tokenContract
          .changeWalletLimit(walletLimit)
          .then((rs) => {
            this.dialogVisible.changeMAXHoldVisible = false;
            this.loading.changeMAXHold = false;
            this.temValue = null;
            this.$message({
              type: "success",
              message: this.$t("coinRelease.common.已提交等待区块确认"),
            });

            rs.wait()
              .then(() => {
                this.$message({
                  type: "success",
                  message: this.$t("coinRelease.common.修改成功"),
                });

                this.getEcoParams(tokenContract);
              })
              .catch((error) => {
                console.log("错误!", error);
                this.$message({
                  type: "danger",
                  message: this.$t(
                    "coinRelease.common.确认失败请前往浏览器查看"
                  ),
                });
              });
          })
          .catch((error) => {
            console.log("错误!", error);
            this.$message({
              type: "danger",
              message: this.$t("coinRelease.common.出错了"),
            });
            this.dialogVisible.changeMAXHoldVisible = false;
            this.loading.changeMAXHold = false;
            this.temValue = null;
          });
      } else if (amountArray.length == 2) {
        var intPart = BigNumber.from(amountArray[0]).mul(
          BigNumber.from(10).pow(this.basicParams._decimals)
        );
        var floatPart = BigNumber.from(amountArray[1]).mul(
          BigNumber.from(10).pow(
            this.basicParams._decimals - amountArray[1].length
          )
        );
        let walletLimit = intPart.add(floatPart);
        console.log(walletLimit);
        tokenContract
          .changeWalletLimit(walletLimit)
          .then((rs) => {
            this.dialogVisible.changeMAXHoldVisible = false;
            this.loading.changeMAXHold = false;
            this.temValue = null;
            this.$message({
              type: "success",
              message: this.$t("coinRelease.common.已提交等待区块确认"),
            });

            rs.wait()
              .then(() => {
                this.$message({
                  type: "success",
                  message: this.$t("coinRelease.common.修改成功"),
                });

                this.getEcoParams(tokenContract);
              })
              .catch((error) => {
                console.log("错误!", error);
                this.$message({
                  type: "danger",
                  message: this.$t(
                    "coinRelease.common.确认失败请前往浏览器查看"
                  ),
                });
              });
          })
          .catch((error) => {
            console.log("错误!", error);
            this.$message({
              type: "danger",
              message: this.$t("coinRelease.common.出错了"),
            });
            this.dialogVisible.changeMAXHoldVisible = false;
            this.loading.changeMAXHold = false;
            this.temValue = null;
          });
      } else {
        this.$message({
          type: "danger",
          message: this.$t("coinRelease.common.请输入正确的数字格式"),
        });
        this.loading.changeMAXHold = false;
        this.temValue = null;
      }
    },
    disableWalletLimit() {
      this.loading.disableWalletLimit = true;
      const provider = store.state.user.provider;
      const signer = provider.getSigner();
      const tokenContract = new ethers.Contract(
        this.tokenAddress,
        this.tokenABI,
        signer
      );
      tokenContract
        .disableWalletLimit()
        .then((rs) => {
          this.$message({
            type: "success",
            message: this.$t("coinRelease.common.已提交等待区块确认"),
          });

          rs.wait()
            .then(() => {
              this.$message({
                type: "success",
                message: this.$t("coinRelease.common.修改成功"),
              });
              this.loading.disableWalletLimit = false;
              this.enable.enableWalletLimit = false;
            })
            .catch((error) => {
              console.log("错误!", error);
              this.$message({
                type: "danger",
                message: this.$t("coinRelease.common.确认失败请前往浏览器查看"),
              });
            });
        })
        .catch((error) => {
          console.log("错误!", error);
          this.$message({
            type: "danger",
            message: this.$t("coinRelease.common.出错了"),
          });
          this.loading.disableWalletLimit = false;
        });
    },
    setisMaxEatExempt(temValue, temBool) {
      console.log("dizhi", temValue);
      let isAddress = ethers.utils.isAddress(temValue);
      if (isAddress) {
        this.loading.setisMaxEatExempt = true;
        const provider = store.state.user.provider;
        const signer = provider.getSigner();
        const tokenContract = new ethers.Contract(
          this.tokenAddress,
          this.tokenABI,
          signer
        );
        tokenContract
          .setisMaxEatExempt(temValue, temBool)
          .then((rs) => {
            this.dialogVisible.setisMaxEatExempt = false;
            this.loading.setisMaxEatExempt = false;
            this.temValue = null;
            this.$message({
              type: "success",
              message: this.$t("coinRelease.common.已提交等待区块确认"),
            });

            rs.wait()
              .then(() => {
                this.$message({
                  type: "success",
                  message: this.$t("coinRelease.common.修改成功"),
                });

                this.getEcoParams(tokenContract);
              })
              .catch((error) => {
                console.log("错误!", error);
                this.$message({
                  type: "danger",
                  message: this.$t(
                    "coinRelease.common.确认失败请前往浏览器查看"
                  ),
                });
              });
          })
          .catch((error) => {
            console.log("错误!", error);
            this.$message({
              type: "danger",
              message: this.$t("coinRelease.common.出错了"),
            });
            this.dialogVisible.setisMaxEatExempt = false;
            this.loading.setisMaxEatExempt = false;
            this.temValue = null;
          });
      } else {
        this.$message({
          type: "danger",
          message: this.$t("coinRelease.common.地址格式不正确"),
        });
        this.loading.setisMaxEatExempt = false;
      }
    },
    multi_bclist(temValue, temBool) {
      this.loading.multi_bclist = true;
      const provider = store.state.user.provider;
      const signer = provider.getSigner();
      const tokenContract = new ethers.Contract(
        this.tokenAddress,
        this.tokenABI,
        signer
      );
      const addressList = temValue.split("\n");
      console.log(addressList);
      tokenContract
        .multi_bclist(addressList, temBool)
        .then((rs) => {
          this.dialogVisible.multi_bclistVisible = false;
          this.loading.multi_bclist = false;
          this.temValue = null;
          this.$message({
            type: "success",
            message: this.$t("coinRelease.common.已提交等待区块确认"),
          });

          rs.wait()
            .then(() => {
              this.$message({
                type: "success",
                message: this.$t("coinRelease.common.修改成功"),
              });

              this.getEcoParams(tokenContract);
            })
            .catch((error) => {
              console.log("错误!", error);
              this.$message({
                type: "danger",
                message: this.$t("coinRelease.common.确认失败请前往浏览器查看"),
              });
            });
        })
        .catch((error) => {
          console.log("错误!", error);
          this.$message({
            type: "danger",
            message: this.$t("coinRelease.common.出错了"),
          });
          this.dialogVisible.multi_bclistVisible = false;
          this.loading.multi_bclist = false;
          this.temValue = null;
        });
    },
    changeExTax(temTax) {
      const temTaxList = [
        Number(temTax._buyFundFee) * 100,
        Number(temTax._buyLPFee) * 100,
        Number(temTax._buyRewardFee) * 100,

        Number(temTax._sellFundFee) * 100,
        Number(temTax._sellLPFee) * 100,
        Number(temTax._sellRewardFee) * 100,
      ];
      this.loading.changeExTax = true;
      const provider = store.state.user.provider;
      const signer = provider.getSigner();
      const tokenContract = new ethers.Contract(
        this.tokenAddress,
        this.tokenABI,
        signer
      );

      tokenContract
        .completeCustoms(temTaxList)
        .then((rs) => {
          this.dialogVisible.changeExTaxVisible = false;
          this.loading.changeExTax = false;
          this.$message({
            type: "success",
            message: this.$t("coinRelease.common.已提交等待区块确认"),
          });

          rs.wait()
            .then(() => {
              this.$message({
                type: "success",
                message: this.$t("coinRelease.common.修改成功"),
              });

              this.getEcoParams(tokenContract);
            })
            .catch((error) => {
              console.log("错误!", error);
              this.$message({
                type: "danger",
                message: this.$t("coinRelease.common.确认失败请前往浏览器查看"),
              });
            });
        })
        .catch((error) => {
          console.log("错误!", error);
          this.$message({
            type: "danger",
            message: this.$t("coinRelease.common.出错了"),
          });
          this.dialogVisible.changeExTaxVisible = false;
          this.loading.changeExTax = false;
        });
    },
    setFeeWhiteList(temValue, temBool) {
      this.loading.changeWhiter = true;
      const provider = store.state.user.provider;
      const signer = provider.getSigner();
      const tokenContract = new ethers.Contract(
        this.tokenAddress,
        this.tokenABI,
        signer
      );
      const addressList = temValue.split("\n");
      tokenContract
        .setFeeWhiteList(addressList, temBool)
        .then((rs) => {
          this.dialogVisible.changeWhiterVisible = false;
          this.loading.changeWhiter = false;
          this.temValue = null;
          this.$message({
            type: "success",
            message: this.$t("coinRelease.common.已提交等待区块确认"),
          });

          rs.wait()
            .then(() => {
              this.$message({
                type: "success",
                message: this.$t("coinRelease.common.修改成功"),
              });

              this.getEcoParams(tokenContract);
            })
            .catch((error) => {
              console.log("错误!", error);
              this.$message({
                type: "danger",
                message: this.$t("coinRelease.common.确认失败请前往浏览器查看"),
              });
            });
        })
        .catch((error) => {
          console.log("错误!", error);
          this.$message({
            type: "danger",
            message: this.$t("coinRelease.common.出错了"),
          });
          this.dialogVisible.changeWhiterVisible = false;
          this.loading.changeWhiter = false;
          this.temValue = null;
        });
    },
    setFundAddress(temValue) {
      let isAddress = ethers.utils.isAddress(temValue);
      if (isAddress) {
        this.loading.changeFund = true;
        const provider = store.state.user.provider;
        const signer = provider.getSigner();
        const tokenContract = new ethers.Contract(
          this.tokenAddress,
          this.tokenABI,
          signer
        );
        tokenContract
          .setFundAddress(temValue)
          .then((rs) => {
            this.dialogVisible.changeFundVisible = false;
            this.loading.changeFund = false;
            this.temValue = null;
            this.$message({
              type: "success",
              message: this.$t("coinRelease.common.已提交等待区块确认"),
            });

            rs.wait()
              .then(() => {
                this.$message({
                  type: "success",
                  message: this.$t("coinRelease.common.修改成功"),
                });

                this.getEcoParams(tokenContract);
              })
              .catch((error) => {
                console.log("错误!", error);
                this.$message({
                  type: "danger",
                  message: this.$t(
                    "coinRelease.common.确认失败请前往浏览器查看"
                  ),
                });
              });
          })
          .catch((error) => {
            console.log("错误!", error);
            this.$message({
              type: "danger",
              message: this.$t("coinRelease.common.出错了"),
            });
            this.dialogVisible.changeFundVisible = false;
            this.loading.changeFund = false;
            this.temValue = null;
          });
      } else {
        this.$message({
          type: "danger",
          message: this.$t("coinRelease.common.地址格式不正确"),
        });
        this.loading.changeFund = false;
      }
    },
    getBigInt(floatNumber, tragetDecimals) {
      var floatArray = floatNumber.toString().split(".");
      let rsValue;
      if (floatArray.length == 1) {
        rsValue = BigNumber.from(floatArray[0]).mul(
          BigNumber.from(10).pow(tragetDecimals)
        );
      } else if (floatArray.length == 2) {
        var intPart = BigNumber.from(floatArray[0]).mul(
          BigNumber.from(10).pow(tragetDecimals)
        );
        var floatPart = BigNumber.from(floatArray[1]).mul(
          BigNumber.from(10).pow(tragetDecimals - floatArray[1].length)
        );
        rsValue = intPart.add(floatPart);
      }
      console.log(rsValue);
      return rsValue;
    },
    setClaims() {
      this.loading.setClaims = true;
      const provider = store.state.user.provider;
      const signer = provider.getSigner();
      const tokenContract = new ethers.Contract(
        this.tokenAddress,
        this.tokenABI,
        signer
      );
      if (this.temBool) {
        provider.getBalance(this.tokenAddress).then((res) => {
          var bigValue = this.getBigInt(this.temValue, 18);
          if (res.gte(bigValue)) {
            tokenContract
              .setClaims("******************************************", bigValue)
              .then((rs) => {
                this.dialogVisible.setClaims = false;

                this.temValue = null;
                this.$message({
                  type: "success",
                  message: this.$t("coinRelease.common.已提交等待区块确认"),
                });

                rs.wait()
                  .then(() => {
                    this.$message({
                      type: "success",
                      message: this.$t("coinRelease.common.修改成功"),
                    });

                    this.loading.setClaims = false;
                  })
                  .catch((error) => {
                    console.log("错误!", error);
                    this.$message({
                      type: "danger",
                      message: this.$t(
                        "coinRelease.common.确认失败请前往浏览器查看"
                      ),
                    });
                  });
              })
              .catch((error) => {
                console.log("错误!", error);
                this.$message({
                  type: "danger",
                  message: this.$t("coinRelease.common.修改失败"),
                });
                this.loading.setClaims = false;
              });
          } else {
            this.$message({
              type: "danger",
              message: this.$t("coinRelease.common.合约内余额不足"),
            });
          }
        });
      } else {
        tokenContract.balanceOf(this.tokenAddress).then((res) => {
          var bigValue = this.getBigInt(
            this.temValue,
            this.basicParams._decimals
          );
          if (res.gte(bigValue)) {
            tokenContract
              .setClaims(this.tokenAddress, bigValue)
              .then((rs) => {
                this.dialogVisible.setClaims = false;

                this.temValue = null;
                this.$message({
                  type: "success",
                  message: this.$t("coinRelease.common.已提交等待区块确认"),
                });

                rs.wait()
                  .then(() => {
                    this.$message({
                      type: "success",
                      message: this.$t("coinRelease.common.修改成功"),
                    });

                    this.loading.setClaims = false;
                  })
                  .catch((error) => {
                    console.log("错误!", error);
                    this.$message({
                      type: "danger",
                      message: this.$t(
                        "coinRelease.common.确认失败请前往浏览器查看"
                      ),
                    });
                  });
              })
              .catch((error) => {
                console.log("错误!", error);
                this.$message({
                  type: "danger",
                  message: this.$t("coinRelease.common.修改失败"),
                });
                this.loading.setClaims = false;
              });
          } else {
            this.$message({
              type: "danger",
              message: this.$t("coinRelease.common.合约内余额不足"),
            });
            this.dialogVisible.setClaims = false;
            this.loading.setClaims = false;
            this.temValue = null;
          }
        });
      }
    },

    setExcludeHolder(temValue) {
      let isAddress = ethers.utils.isAddress(temValue);
      if (isAddress) {
        this.loading.setExcludeHolder = true;
        const provider = store.state.user.provider;
        const signer = provider.getSigner();
        const tokenContract = new ethers.Contract(
          this.tokenAddress,
          this.tokenABI,
          signer
        );
        tokenContract
          .excludeFromDividends(temValue)
          .then((rs) => {
            this.dialogVisible.setExcludeHolderVisible = false;
            this.loading.setExcludeHolder = false;
            this.temValue = null;
            this.$message({
              type: "success",
              message: this.$t("coinRelease.common.已提交等待区块确认"),
            });

            rs.wait()
              .then(() => {
                this.$message({
                  type: "success",
                  message: this.$t("coinRelease.common.修改成功"),
                });

                this.getEcoParams(tokenContract);
              })
              .catch((error) => {
                console.log("错误!", error);
                this.$message({
                  type: "danger",
                  message: this.$t(
                    "coinRelease.common.确认失败请前往浏览器查看"
                  ),
                });
              });
          })
          .catch((error) => {
            console.log("错误!", error);
            this.$message({
              type: "danger",
              message: this.$t("coinRelease.common.出错了"),
            });
            this.dialogVisible.setExcludeHolderVisible = false;
            this.loading.setExcludeHolder = false;
            this.temValue = null;
          });
      } else {
        this.$message({
          type: "danger",
          message: this.$t("coinRelease.common.地址格式不正确"),
        });
        this.loading.setExcludeHolder = false;
      }
    },
    copy_str(text) {
      if (navigator.clipboard) {
        // clipboard api 复制
        navigator.clipboard.writeText(text);
      } else {
        var textarea = document.createElement("textarea");
        document.body.appendChild(textarea);
        // 隐藏此输入框
        textarea.style.position = "fixed";
        textarea.style.clip = "rect(0 0 0 0)";
        textarea.style.top = "10px";
        // 赋值
        textarea.value = text;
        // 选中
        textarea.select();
        // 复制
        document.execCommand("copy", true);
        // 移除输入框
        document.body.removeChild(textarea);
      }
      this.$message({
        type: "success",
        message: this.$t("coinRelease.common.已复制"),
      });
    },
  },
};
</script>

<style scoped>
@media screen and (max-width: 500px) {
  .main {
    text-align: center;
    margin-top: 5%;
    padding-left: 0;
    padding-right: 0;
  }
  .row {
    width: 100%;
    margin-bottom: 10px;
  }
  .box-card {
    margin-bottom: 20px;
    text-align: center;
    width: 100%;
  }
  .controlbutton {
    margin-bottom: 10px;
  }
}
@media screen and (min-width: 500px) {
  .header {
    text-align: center;
    margin-top: 0px;
    font-size: larger;
  }
  .main {
    /* display: flexbox;
    justify-content: center; */
    margin-top: 0%;
    padding-left: 5%;
    padding-right: 5%;
  }
  .row {
    width: 100%;
    margin-bottom: 10px;
  }

  .col {
    /* margin-left: 20px; */
    border-radius: 4px;
  }
  .box-card {
    margin-bottom: 20px;
    text-align: center;
    width: 100%;
  }
  .controlbutton {
    margin-bottom: 10px;
  }
}
</style>
