<template>
  <div class="app-container">
    <div class="inner">
      <el-header class="header">
        <p>即将上线,敬请期待！</p>
      </el-header>
      <el-main class="main">
        <p>
          如现有模版无法满足您的需求, 请联系我们的
          <el-link
            style="font-size: 22px; color: #30608f"
            icon="el-icon-telegram"
            href="https://t.me/btc6560"
            target="_blank"
            >工作人员</el-link
          >
          进行合约定制, 也可加入我们的
          <el-link
            style="font-size: 22px; color: #30608f"
            icon="el-icon-telegram"
            href="https://t.me/pandatool"
            target="_blank"
            >交流群组</el-link
          >
          进行反馈:
        </p>
      </el-main>
    </div>
  </div>
</template>

<script>
export default {
  name: "Dashboard",
  data() {
    return {};
  },
  methods: {},
};
</script>

<style scoped>
@media screen and (max-width: 500px) {
  .main {
    text-align: center;
    padding-left: 0;
    padding-right: 0;
  }
}
@media screen and (min-width: 500px) {
  .header {
    text-align: center;
    margin-top: 20px;
    font-size: 30px;
  }
  .main {
    font-size: 24px;
    text-align: center;
    padding-left: 5%;
    padding-right: 5%;
  }
}
</style>
