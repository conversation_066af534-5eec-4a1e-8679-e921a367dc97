<template>
  <div class="app-container">
    <div class="inner">
      <el-header class="header">
        <p>{{$t('coinRelease.console.代币列表')}}</p>
      </el-header>
      <el-main class="main">
        <el-table
          :data="CoinList"
          :default-sort="{ prop: 'createdTime', order: 'descending' }"
          size="medium"
          class="table"
        >
          <el-table-column prop="coinAddress" :label="$t('coinRelease.common.合约地址')" width="210">
            <template slot-scope="scope"
              >{{ scope.row.coinAddress }}
              <i
                class="el-icon-copy-document"
                @click="copy_str(scope.row.coinAddress)"
              ></i
            ></template>
          </el-table-column>
          <el-table-column prop="name" :label="$t('coinRelease.console.名称')" width="100">
          </el-table-column>
          <el-table-column prop="symbol" :label="$t('coinRelease.common.简称')" width="100">
          </el-table-column>
          <el-table-column prop="mode" :label="$t('coinRelease.console.模版')" width="200">
          </el-table-column>
          <el-table-column prop="owner" :label="$t('coinRelease.common.所有者')" width="210">
            <template slot-scope="scope"
              >{{ scope.row.owner }}
              <i
                class="el-icon-copy-document"
                @click="copy_str(scope.row.owner)"
              ></i
            ></template>
          </el-table-column>
          <el-table-column prop="createdTime" :label="$t('coinRelease.console.创建时间')" width="150">
            <template slot-scope="scope"
              >{{ getDate(scope.row.createdTime) }}
            </template>
          </el-table-column>
          <el-table-column width="120" :label="$t('coinRelease.console.操作')">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="primary"
                @click="enterConsole(scope.row)"
                >{{$t('coinRelease.common.进入控制台')}}</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </el-main>
    </div>
  </div>
</template>

<script>
import store from "@/store";
import Mode1 from "@/contracts/pandaMode1.json";
import chainParams from "@/contracts/coinReleaseParamsV1.json";
import standardData from "@/contracts/standardCoin.json";
const ethers = require("ethers");
// const AbiCoder = require('ethers.utils.AbiCoder')
export default {
  name: "Dashboard",
  data() {
    return {
      standardData,
      chainParams,
      Mode1,
      CleanModeAddress: null,
      LPModeAddress: null,
      LPInviterModeAddress: null,
      LPMineModeAddress: null,
      holdInviterModeAddress: null,
      holdRefelctionModeAddress: null,
      LPBurnModeAddress: null,
      holdOthersModeAddress: null,
      TOFModeAddress: null,
      tokenAddress: null,
      store,
      CoinList: [],
      modeADDList: [],
    };
  },
  created() {
    let Inval = setInterval(() => {
      if (store.state.user.chainId && store.state.user.address) {
        window.clearInterval(Inval);
        const chainId = store.state.user.chainId;
        for (let i = 0; i < chainParams[chainId].length; i++) {
          if (i == 1) {
            this.CleanModeAddress = chainParams[chainId][1].ModeAddress;
            this.modeADDList.push(this.CleanModeAddress);
          } else if (i == 2) {
            this.LPModeAddress = chainParams[chainId][2].ModeAddress;
            this.modeADDList.push(this.LPModeAddress);
          } else if (i == 3) {
            this.LPInviterModeAddress = chainParams[chainId][3].ModeAddress;
            this.modeADDList.push(this.LPInviterModeAddress);
          } else if (i == 4) {
            this.LPMineModeAddress = chainParams[chainId][4].ModeAddress;
            this.modeADDList.push(this.LPMineModeAddress);
          } else if (i == 5) {
            this.holdInviterModeAddress = chainParams[chainId][5].ModeAddress;
            this.modeADDList.push(this.holdInviterModeAddress);
          } else if (i == 6) {
            this.holdRefelctionModeAddress =
              chainParams[chainId][6].ModeAddress;
            this.modeADDList.push(this.holdRefelctionModeAddress);
          } else if (i == 7) {
            this.LPBurnModeAddress = chainParams[chainId][7].ModeAddress;
            this.modeADDList.push(this.LPBurnModeAddress);
          } else if (i == 8) {
            this.holdOthersModeAddress = chainParams[chainId][8].ModeAddress;
            this.modeADDList.push(this.holdOthersModeAddress);
          } else if (i == 9) {
            this.TOFModeAddress = chainParams[chainId][9].ModeAddress;
            this.modeADDList.push(this.TOFModeAddress);
          } else {
          }
        }
        // this.CleanModeAddress = chainParams[chainId][1].ModeAddress
        // this.LPModeAddress = chainParams[chainId][2].ModeAddress
        // this.LPInviterModeAddress = chainParams[chainId][3].ModeAddress
        // this.LPMineModeAddress = chainParams[chainId][4].ModeAddress
        // this.holdInviterModeAddress = chainParams[chainId][5].ModeAddress
        // this.holdRefelctionModeAddress = chainParams[chainId][6].ModeAddress
        this.getCoinList();
      }
    }, 1000);
  },
  computed: {
    isMobile() {
      if (window.screen.width < 500) {
        return true;
      } else {
        return false;
      }
    },
    getShortAddress(address) {
      return address.substring(0, 5) + "..." + address.substring(37);
    },
  },
  methods: {
    getCoinList() {
      const modeABI = this.Mode1;
      // const modeADDList = [this.CleanModeAddress,this.LPModeAddress,this.LPInviterModeAddress,this.LPMineModeAddress,this.holdInviterModeAddress,this.holdRefelctionModeAddress]
      const modeADDList = this.modeADDList;
      const provider = store.state.user.provider;

      for (let i = 0; i < modeADDList.length; i++) {
        console.log(modeADDList[i]);
        if (i == 0) {
          const ModeContract = new ethers.Contract(
            modeADDList[i],
            modeABI,
            provider
          );
          ModeContract.checkCreatedCoin(store.state.user.address)
            .then((result) => {
              console.log("got coin list", result);
              const tokenABI = this.standardData.Coin1abi;
              var modeType =  this.$t("coinRelease.modeType.标准模版");
              this.getTableData(result, modeType, tokenABI);
            })
            .catch(() => {
              this.$message({
                type: "danger",
                message: this.$t('coinRelease.console.获取标准合约错误请检查网络'),
              });
            });
        } else if (i == 1) {
          console.log(i);
          const Mode2Contract = new ethers.Contract(
            modeADDList[i],
            modeABI,
            provider
          );
          Mode2Contract.checkCreatedCoin(store.state.user.address)
            .then((result) => {
              console.log("got LP list", result);
              const token2ABI = this.standardData.Coin2abi;
              var modeType = this.$t("coinRelease.modeType.LP分红");
              this.getTableData(result, modeType, token2ABI);
            })
            .catch(() => {
              this.$message({
                type: "danger",
                message: this.$t('coinRelease.console.获取LP分红合约错误请检查网络'),
              });
            });
        } else if (i == 2) {
          console.log(i);
          const Mode3Contract = new ethers.Contract(
            modeADDList[i],
            modeABI,
            provider
          );
          Mode3Contract.checkCreatedCoin(store.state.user.address)
            .then((result) => {
              console.log("got LPWithInviter list", result);
              const token3ABI = this.standardData.Coin3abi;
              var modeType = this.$t("coinRelease.modeType.LP分红推荐奖励");
              this.getTableData(result, modeType, token3ABI);
            })
            .catch(() => {
              this.$message({
                type: "danger",
                message: this.$t('coinRelease.console.获取LP分红+推荐奖励合约错误请检查网络'),
              });
            });
        } else if (i == 3) {
          console.log(i);
          const Mode3Contract = new ethers.Contract(
            modeADDList[i],
            modeABI,
            provider
          );
          Mode3Contract.checkCreatedCoin(store.state.user.address)
            .then((result) => {
              console.log("got LPWithInviter list", result);
              const token3ABI = this.standardData.Coin4abi;
              var modeType = this.$t("coinRelease.modeType.LP挖矿推荐奖励");
              this.getTableData(result, modeType, token3ABI);
            })
            .catch(() => {
              this.$message({
                type: "danger",
                message: this.$t('coinRelease.console.获取LP挖矿推荐奖励合约错误请检查网络')
              });
            });
        } else if (i == 4) {
          console.log(i);
          const Mode4Contract = new ethers.Contract(
            modeADDList[i],
            modeABI,
            provider
          );
          Mode4Contract.checkCreatedCoin(store.state.user.address)
            .then((result) => {
              console.log("got HoldWithInviter list", result);
              const token4ABI = this.standardData.Coin5abi;
              var modeType = this.$t("coinRelease.modeType.持币复利推荐奖励");
              this.getTableData(result, modeType, token4ABI);
            })
            .catch(() => {
              this.$message({
                type: "danger",
                message: this.$t('coinRelease.console.获取持币复利推荐奖励合约错误请检查网络') 
              });
            });
        } else if (i == 5) {
          console.log(i);
          const Mode5Contract = new ethers.Contract(
            modeADDList[i],
            modeABI,
            provider
          );
          Mode5Contract.checkCreatedCoin(store.state.user.address)
            .then((result) => {
              console.log("got HoldWithInviter list", result);
              const token5ABI = this.standardData.Coin6abi;
              var modeType = this.$t("coinRelease.modeType.分红本币");
              this.getTableData(result, modeType, token5ABI);
            })
            .catch(() => {
              this.$message({
                type: "danger",
                message:  this.$t('coinRelease.console.获取持币分红合约错误请检查网络'),
              });
            });
        } else if (i == 6) {
          console.log(i);
          const Mode5Contract = new ethers.Contract(
            modeADDList[i],
            modeABI,
            provider
          );
          Mode5Contract.checkCreatedCoin(store.state.user.address)
            .then((result) => {
              const token5ABI = this.standardData.Coin6abi;
              var modeType = this.$t("coinRelease.modeType.Mint燃烧底池");
              this.getTableData(result, modeType, token5ABI);
            })
            .catch(() => {
              this.$message({
                type: "danger",
                message: this.$t('coinRelease.console.获取燃烧底池合约错误请检查网络'),
              });
            });
        } else if (i == 7) {
          console.log(i);
          const Mode5Contract = new ethers.Contract(
            modeADDList[i],
            modeABI,
            provider
          );
          Mode5Contract.checkCreatedCoin(store.state.user.address)
            .then((result) => {
              const token5ABI = this.standardData.Coin6abi;
              var modeType = this.$t("coinRelease.modeType.Mint暴力分红")
              // var modeType = "Mint+暴力分红";
              this.getTableData(result, modeType, token5ABI);
            })
            .catch(() => {
              this.$message({
                type: "danger",
                message: this.$t('coinRelease.console.获取暴力分红合约错误请检查网络'),
              });
            });
        } else if (i == 8) {
          console.log(i);
          const Mode5Contract = new ethers.Contract(
            modeADDList[i],
            modeABI,
            provider
          );
          Mode5Contract.checkCreatedCoin(store.state.user.address)
            .then((result) => {
              console.log("314LIstd", result);
              const token5ABI = this.standardData.Coin6abi;
              var modeType = "314协议";
              this.getTableData(result, modeType, token5ABI);
            })
            .catch(() => {
              this.$message({
                type: "danger",
                message: this.$t('coinRelease.console.获取314合约错误请检查网络'),
              });
            });
        } else {
          this.$message({
            type: "danger",
            message:  this.$t('coinRelease.console.未知错误'),
          });
        }
      }
    },

    async getTableData(result, modeType, tokenABI) {
      for (let i = 0; i < result.length; i++) {
        const tokenContract = new ethers.Contract(
          result[i]._tokenAddress,
          tokenABI,
          store.state.user.provider
        );
        let owner = await tokenContract.owner();
        let createdtime = result[i]._createdTime.toNumber();
        let temToken = {
          coinAddress: result[i]._tokenAddress,
          name: result[i]._name,
          symbol: result[i]._symbol,
          mode: modeType,
          owner: owner,
          createdTime: createdtime,
        };
        this.CoinList.push(temToken);
      }
      console.log("final Table Data", this.CoinList);
    },

    enterConsole(row) {
      console.log("row", row);
      if (row.mode ==  this.$t("coinRelease.modeType.标准模版") || row.mode == this.$t("coinRelease.modeType.LP分红")) {
        this.$router.push({
          path: "/coinrelease/detail/",
          query: { coinAddress: row.coinAddress, modeType: row.mode },
        });
      } else if (row.mode == this.$t("coinRelease.modeType.LP分红推荐奖励")) {
        this.$router.push({
          path: "/coinrelease/LPinviterDetail/",
          query: { coinAddress: row.coinAddress, modeType: row.mode },
        });
      } else if (row.mode == this.$t("coinRelease.modeType.LP挖矿推荐奖励")) {
        this.$router.push({
          path: "/coinrelease/LPMineDetail/",
          query: { coinAddress: row.coinAddress, modeType: row.mode },
        });
      } else if (row.mode == this.$t("coinRelease.modeType.持币复利推荐奖励")) {
        this.$router.push({
          path: "/coinrelease/holdInviterDetail/",
          query: { coinAddress: row.coinAddress, modeType: row.mode },
        });
      } else if (row.mode == this.$t("coinRelease.modeType.分红本币")) {
        this.$router.push({
          path: "/coinrelease/holdReflectionDetail/",
          query: { coinAddress: row.coinAddress, modeType: row.mode },
        });
      } else if (row.mode == this.$t("coinRelease.modeType.Mint燃烧底池")) {
        this.$router.push({
          path: "/coinrelease/LPBurnDetail/",
          query: { coinAddress: row.coinAddress, modeType: row.mode },
        });
      } else if (row.mode == this.$t("coinRelease.modeType.Mint暴力分红")) {
        this.$router.push({
          path: "/coinrelease/holdRefOthersDetail/",
          query: { coinAddress: row.coinAddress, modeType: row.mode },
        });
      } else if (row.mode == this.$t("coinRelease.modeType.三14协议")) {
        this.$router.push({
          path: "/coinrelease/314Detail/",
          query: { coinAddress: row.coinAddress, modeType: row.mode },
        });
      } else {
        console.log("未完待续");
      }
    },
    getDate(timestamp) {
      var date = new Date(timestamp * 1000);
      var Y = date.getFullYear() + "-";
      var M =
        (date.getMonth() + 1 < 10
          ? "0" + (date.getMonth() + 1)
          : date.getMonth() + 1) + "-";
      var D = date.getDate() + " ";
      var h = date.getHours() + ":";
      var m = date.getMinutes();
      return Y + M + D + h + m;
    },

    copy_str(text) {
      if (navigator.clipboard) {
        // clipboard api 复制
        navigator.clipboard.writeText(text);
      } else {
        var textarea = document.createElement("textarea");
        document.body.appendChild(textarea);
        // 隐藏此输入框
        textarea.style.position = "fixed";
        textarea.style.clip = "rect(0 0 0 0)";
        textarea.style.top = "10px";
        // 赋值
        textarea.value = text;
        // 选中
        textarea.select();
        // 复制
        document.execCommand("copy", true);
        // 移除输入框
        document.body.removeChild(textarea);
      }
      this.$message({
        type: "success",
        message: this.$t('coinRelease.common.已复制'),
      });
    },
  },
};
</script>

<style scoped>
@media screen and (max-width: 500px) {
  .main {
    text-align: center;
    margin-top: 5%;
    padding-left: 0;
    padding-right: 0;
  }
}
@media screen and (min-width: 500px) {
  .inner {
    background-color: #ffff;
    padding: 40px;
  }
  .header {
    text-align: center;
    margin-top: 20px;
    font-size: larger;
  }
  .main {
    /* display: flex;
  align-items:center; */
    text-align: center;
    margin-top: 2%;
    padding-left: 5%;
    padding-right: 5%;
  }
  .table {
    width: 100%;
    size: medium;
  }
}
</style>
