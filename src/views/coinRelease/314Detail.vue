<template>
  <div class="app-container">
    <div class="inner">
      <el-header class="header">
        <p>
          {{ $t("coinRelease.common.代币详情") }}
          <i
            class="el-icon-copy-document"
            style="
              margin-left: 10px;
              font-size: 12px;
              font-weight: 400;
              color: #409eff;
            "
            @click="copy_str(url)"
            >{{ $t("coinRelease.common.复制链接") }}</i
          >
        </p>
      </el-header>
      <el-main class="main">
        <el-row :gutter="24">
          <el-col :xs="24" :sm="18">
            <el-card shadow="always" class="box-card">
              <div slot="header" class="clearfix">
                <span>{{ $t("coinRelease.common.基本信息") }}</span>
              </div>
              <el-descriptions size="medium" :column="isMobile ? 1 : 2" border>
                <el-descriptions-item :label="$t('coinRelease.common.全称')">{{
                  basicParams._name
                }}</el-descriptions-item>
                <el-descriptions-item :label="$t('coinRelease.common.简称')">{{
                  basicParams._symbol
                }}</el-descriptions-item>
                <el-descriptions-item :label="$t('coinRelease.common.精度')">{{
                  basicParams._decimals
                }}</el-descriptions-item>
                <el-descriptions-item :label="$t('coinRelease.common.总量')">{{
                  getSupply
                }}</el-descriptions-item>
                <el-descriptions-item
                  :label="$t('coinRelease.common.代币模版')"
                  >{{ modeType }}</el-descriptions-item
                >
                <el-descriptions-item :label="$t('coinRelease.common.所有者')">
                  {{ getOwner }}
                  <i
                    class="el-icon-copy-document"
                    @click="copy_str(basicParams._owner)"
                  ></i>
                </el-descriptions-item>
                <el-descriptions-item
                  :label="$t('coinRelease.common.合约地址')"
                >
                  {{ getContract }}
                  <i
                    class="el-icon-copy-document"
                    @click="copy_str(tokenAddress)"
                  ></i>
                </el-descriptions-item>
              </el-descriptions>
            </el-card>

            <el-card shadow="always" class="box-card">
              <div slot="header" class="clearfix">
                <span>{{ $t("coinRelease.common.经济模型") }}</span>
              </div>
              <el-descriptions
                size="medium"
                :column="1"
                border
                :contentStyle="CS"
                :label-style="LS"
              >
                <el-descriptions-item
                  :label="$t('coinRelease.common.营销钱包')"
                >
                  {{ getFund }}
                  <i
                    class="el-icon-copy-document"
                    @click="copy_str(ecoParams.fundAddress)"
                  ></i>
                </el-descriptions-item>

                <el-descriptions-item :label="$t('coinRelease.common.买入税率')"
                  >{{ getBuyFee }}% ({{ $t("coinRelease.common.营销")
                  }}{{ ecoParams._buyFundFee }}% +
                  {{ $t("coinRelease.common.销毁")
                  }}{{ ecoParams.buy_burnFee }}% )
                </el-descriptions-item>
                <el-descriptions-item :label="$t('coinRelease.common.卖出税率')"
                  >{{ getSellFee }}% ({{ $t("coinRelease.common.营销")
                  }}{{ ecoParams._sellFundFee }}% +
                  {{ $t("coinRelease.common.销毁")
                  }}{{ ecoParams.sell_burnFee }}% )
                </el-descriptions-item>
                <el-descriptions-item :label="$t('coinRelease.a314.冷却时间')">
                  {{ ecoParams.cooldownSec }} {{ $t("coinRelease.a314.秒") }}
                </el-descriptions-item>
                <el-descriptions-item :label="$t('coinRelease.a314.解锁区块')">
                  {{ ecoParams.blockToUnlockLiquidity }}
                </el-descriptions-item>

                <el-descriptions-item
                  :label="$t('coinRelease.common.单钱包持币上限')"
                  >{{ getMaxWalletAmount }}</el-descriptions-item
                >
              </el-descriptions>
            </el-card>
          </el-col>
          <el-col :xs="24" :sm="6">
            <el-card v-show="partShow" shadow="always" class="box-card">
              <div slot="header" class="clearfix">
                <span>{{ $t("coinRelease.common.权限控制") }}</span>
              </div>

              <el-button
                size="mini"
                v-if="loading.renounceOwner"
                :loading="true"
                plain
                >{{ $t("coinRelease.common.丢弃权限") }}</el-button
              >
              <el-button
                size="mini"
                v-else
                plain
                @click="renounceOwnership()"
                >{{ $t("coinRelease.common.丢弃权限") }}</el-button
              >
            </el-card>
            <el-card shadow="always" class="box-card">
              <div slot="header" class="clearfix">
                <span>{{ $t("coinRelease.common.流动性控制") }}</span>
              </div>

              <div class="controlbutton">
                <el-button
                  size="mini"
                  plain
                  v-if="ecoParams.liquidityAdded"
                  disabled
                >
                  {{ $t("coinRelease.a314.添加流动性") }}</el-button
                >

                <el-button
                  size="mini"
                  plain
                  v-else
                  @click="getBlockNumberAdd()"
                >
                  {{ $t("coinRelease.a314.添加流动性") }}</el-button
                >
                <el-dialog
                  :title="$t('coinRelease.a314.添加流动性')"
                  :visible.sync="dialogVisible.addLiquidity"
                  width="30%"
                  center
                >
                  <div>
                    <p>
                      {{ $t("coinRelease.a314.当前区块") }}:{{
                        this.lastestBlock
                      }}
                    </p>
                    <p>{{ $t("coinRelease.a314.解锁区块") }}:</p>
                    <el-input
                      v-model="temValue"
                      type="number"
                      placeholder="10000"
                    >
                    </el-input>
                    <p>{{ $t("coinRelease.a314.添加BNB数量") }}:</p>
                    <el-input
                      v-model="addETH"
                      @keyup.native="addETH = checkDecimal(addETH)"
                      placeholder="0.01"
                    >
                    </el-input>
                    <div style="margin-top: 10px">
                      {{ $t("coinRelease.a314.预估初始价格") }}:
                      {{ getEstimatePrice }}
                    </div>
                    <p>
                      {{
                        $t(
                          "coinRelease.a314.由于314协议无滑点机制实际成交价格与池子大小数量均有关系预估价格仅供参考"
                        )
                      }}
                    </p>
                  </div>

                  <span slot="footer" class="dialog-footer">
                    <el-button
                      size="mini"
                      plain
                      @click="dialogVisible.addLiquidity = false"
                      >{{ $t("coinRelease.common.取消") }}</el-button
                    >
                    <el-button
                      size="mini"
                      plain
                      v-if="loading.addLiquidity"
                      type="primary"
                      :loading="true"
                      >{{ $t("coinRelease.common.确定") }}</el-button
                    >
                    <el-button
                      size="mini"
                      plain
                      v-else
                      type="primary"
                      @click="AddLiquidity(temValue)"
                      >{{ $t("coinRelease.common.确定") }}</el-button
                    >
                  </span>
                </el-dialog>
              </div>
              <div class="controlbutton">
                <el-button
                  size="mini"
                  v-if="loading.removeLiquidity"
                  :loading="true"
                  plain
                  >{{ $t("coinRelease.a314.移除流动性") }}</el-button
                >

                <el-button
                  size="mini"
                  v-else
                  plain
                  @click="RemoveLiquidity()"
                  >{{ $t("coinRelease.a314.移除流动性") }}</el-button
                >
              </div>

              <div class="controlbutton">
                <el-button size="mini" plain @click="getBlockNumberExtend()">{{
                  $t("coinRelease.a314.延长锁池时间")
                }}</el-button>
                <el-dialog
                  :title="$t('coinRelease.a314.延长锁池区块')"
                  :visible.sync="dialogVisible.extendLiquidityLock"
                  width="30%"
                  center
                >
                  <div>
                    {{ $t("coinRelease.a314.当前区块") }}:{{
                      this.lastestBlock
                    }}
                    <p>
                      {{
                        $t("coinRelease.a314.新的锁池区块数需大于之前的设定值")
                      }}
                    </p>
                    <el-input
                      v-model="temValue"
                      type="number"
                      placeholder="10000"
                    >
                    </el-input>
                  </div>

                  <span slot="footer" class="dialog-footer">
                    <el-button
                      size="mini"
                      plain
                      @click="dialogVisible.extendLiquidityLock = false"
                      >{{ $t("coinRelease.common.取消") }}</el-button
                    >
                    <el-button
                      size="mini"
                      plain
                      v-if="loading.extendLiquidityLock"
                      type="primary"
                      :loading="true"
                      >{{ $t("coinRelease.common.确定") }}</el-button
                    >
                    <el-button
                      size="mini"
                      plain
                      v-else
                      type="primary"
                      @click="extendLiquidityLock(temValue)"
                      >{{ $t("coinRelease.common.确定") }}</el-button
                    >
                  </span>
                </el-dialog>
              </div>
              <p>{{ $t("coinRelease.a314.仅营销钱包有权控制流动性") }}</p>
            </el-card>
            <el-card v-show="partShow" shadow="always" class="box-card">
              <div slot="header" class="clearfix">
                <span>{{ $t("coinRelease.a314.交易控制") }}</span>
              </div>
              <div class="controlbutton">
                <el-button
                  size="mini"
                  v-if="ecoParams.enableWalletLimit"
                  plain
                  @click="dialogVisible.changeMAXHoldVisible = true"
                  >{{ $t("coinRelease.a314.修改持币上限") }}</el-button
                >
                <el-button size="mini" v-else plain disabled>{{
                  $t("coinRelease.a314.修改持币上限")
                }}</el-button>
                <el-dialog
                  :title="$t('coinRelease.a314.修改持币上限')"
                  :visible.sync="dialogVisible.changeMAXHoldVisible"
                  width="30%"
                  center
                >
                  <div>
                    <p>{{ $t("coinRelease.a314.持币上限") }}</p>
                    <el-input
                      v-model="temValue"
                      oninput="value=value.replace(/[^0-9.]/g,'')"
                      placeholder="..."
                    />
                  </div>

                  <span slot="footer" class="dialog-footer">
                    <el-button
                      @click="dialogVisible.changeMAXHoldVisible = false"
                      >{{ $t("coinRelease.common.取消") }}</el-button
                    >
                    <el-button
                      v-if="loading.changeMAXHold"
                      type="primary"
                      :loading="true"
                      >{{ $t("coinRelease.common.确定") }}</el-button
                    >
                    <el-button
                      v-else
                      type="primary"
                      @click="changeWalletLimit(temValue)"
                      >{{ $t("coinRelease.common.确定") }}</el-button
                    >
                  </span>
                </el-dialog>
              </div>
              <div class="controlbutton">
                <el-button
                  size="mini"
                  v-if="loading.disableWalletLimit"
                  :loading="true"
                  plain
                  >{{ $t("coinRelease.a314.关闭持币限制") }}</el-button
                >
                <el-button
                  size="mini"
                  v-else-if="!ecoParams.enableWalletLimit"
                  plain
                  disabled
                  >{{ $t("coinRelease.a314.关闭持币限制") }}</el-button
                >
                <el-button
                  size="mini"
                  v-else
                  plain
                  @click="disableWalletLimit()"
                  >{{ $t("coinRelease.a314.关闭持币限制") }}</el-button
                >
              </div>
              <div class="controlbutton">
                <el-button
                  size="mini"
                  v-if="loading.setisMaxEatExempt"
                  :loading="true"
                  plain
                  >{{ $t("coinRelease.a314.设置持币白名单") }}</el-button
                >
                <el-button
                  size="mini"
                  v-else-if="!ecoParams.enableWalletLimit"
                  plain
                  disabled
                  >{{ $t("coinRelease.a314.设置持币白名单") }}</el-button
                >
                <el-button
                  size="mini"
                  v-else
                  plain
                  @click="dialogVisible.setisMaxEatExempt = true"
                  >{{ $t("coinRelease.a314.设置持币白名单") }}</el-button
                >
                <el-dialog
                  :title="$t('coinRelease.a314.设置持币白名单')"
                  :visible.sync="dialogVisible.setisMaxEatExempt"
                  width="30%"
                  center
                >
                  <el-radio-group v-model="temBool">
                    <el-radio :label="true">{{
                      $t("coinRelease.a314.添加白名单")
                    }}</el-radio>
                    <el-radio :label="false">{{
                      $t("coinRelease.a314.移除白名单")
                    }}</el-radio>
                  </el-radio-group>
                  <div style="margin-top: 30px">
                    <p>{{ $t("coinRelease.a314.白名单地址") }}</p>
                    <el-input v-model="temValue" placeholder="0x..." />
                  </div>

                  <span slot="footer" class="dialog-footer">
                    <el-button
                      size="mini"
                      @click="dialogVisible.setisMaxEatExempt = false"
                      >{{ $t("coinRelease.common.取消") }}</el-button
                    >
                    <el-button
                      size="mini"
                      v-if="loading.setisMaxEatExempt"
                      :loading="true"
                      type="primary"
                      plain
                      >{{ $t("coinRelease.common.确定") }}</el-button
                    >
                    <el-button
                      size="mini"
                      v-else
                      type="primary"
                      plain
                      @click="setisMaxEatExempt(temValue, temBool)"
                      >{{ $t("coinRelease.common.确定") }}</el-button
                    >
                  </span>
                </el-dialog>
              </div>
              <div class="controlbutton">
                <el-button
                  size="mini"
                  plain
                  @click="dialogVisible.changeCooldownSec = true"
                  >{{ $t("coinRelease.a314.修改冷却时间") }}</el-button
                >
                <el-dialog
                  :title="$t('coinRelease.a314.修改冷却时间')"
                  :visible.sync="dialogVisible.changeCooldownSec"
                  width="30%"
                  center
                >
                  <div>
                    <p>{{ $t("coinRelease.a314.修改冷却时间") }}</p>
                    <el-input v-model="temValue" type="number" placeholder="10">
                    </el-input>
                  </div>

                  <span slot="footer" class="dialog-footer">
                    <el-button
                      size="mini"
                      plain
                      @click="dialogVisible.changeCooldownSec = false"
                      >{{ $t("coinRelease.common.取消") }}</el-button
                    >
                    <el-button
                      size="mini"
                      plain
                      v-if="loading.changeCooldownSec"
                      type="primary"
                      :loading="true"
                      >{{ $t("coinRelease.common.确定") }}</el-button
                    >
                    <el-button
                      size="mini"
                      plain
                      v-else
                      type="primary"
                      @click="changeCooldownSec(temValue)"
                      >{{ $t("coinRelease.common.确定") }}</el-button
                    >
                  </span>
                </el-dialog>
              </div>
              <div class="controlbutton">
                <el-button
                  size="mini"
                  v-if="loading.setExcludeCooling"
                  :loading="true"
                  plain
                  >{{ $t("coinRelease.a314.设置冷却白名单") }}</el-button
                >
                <el-button
                  size="mini"
                  v-else
                  plain
                  @click="dialogVisible.setExcludeCooling = true"
                  >{{ $t("coinRelease.a314.设置冷却白名单") }}</el-button
                >
                <el-dialog
                  :title="$t('coinRelease.a314.设置冷却白名单')"
                  :visible.sync="dialogVisible.setExcludeCooling"
                  width="30%"
                  center
                >
                  <el-radio-group v-model="temBool">
                    <el-radio :label="true">{{
                      $t("coinRelease.a314.添加白名单")
                    }}</el-radio>
                    <el-radio :label="false">{{
                      $t("coinRelease.a314.移除白名单")
                    }}</el-radio>
                  </el-radio-group>
                  <div style="margin-top: 30px">
                    <p>{{ $t("coinRelease.a314.白名单地址") }}</p>
                    <el-input v-model="temValue" placeholder="0x..." />
                  </div>

                  <span slot="footer" class="dialog-footer">
                    <el-button
                      size="mini"
                      @click="dialogVisible.setExcludeCooling = false"
                      >{{ $t("coinRelease.common.取消") }}</el-button
                    >
                    <el-button
                      size="mini"
                      v-if="loading.setExcludeCooling"
                      :loading="true"
                      type="primary"
                      plain
                      >{{ $t("coinRelease.common.确定") }}</el-button
                    >
                    <el-button
                      size="mini"
                      v-else
                      type="primary"
                      plain
                      @click="setExcludeCooling(temValue, temBool)"
                      >{{ $t("coinRelease.common.确定") }}</el-button
                    >
                  </span>
                </el-dialog>
              </div>

              <div class="controlbutton">
                <el-button
                  size="mini"
                  plain
                  @click="dialogVisible.changeFundVisible = true"
                  >{{ $t("coinRelease.common.修改营销钱包") }}</el-button
                >
                <el-dialog
                  :title="$t('coinRelease.common.修改营销钱包')"
                  :visible.sync="dialogVisible.changeFundVisible"
                  width="30%"
                  center
                >
                  <div>
                    <p>{{ $t("coinRelease.common.营销钱包地址") }}</p>
                    <el-input v-model="temValue" placeholder="0x..." />
                  </div>

                  <span slot="footer" class="dialog-footer">
                    <el-button
                      size="mini"
                      plain
                      @click="dialogVisible.changeFundVisible = false"
                      >{{ $t("coinRelease.common.取消") }}</el-button
                    >
                    <el-button
                      size="mini"
                      v-if="loading.changeFund"
                      :loading="true"
                      type="primary"
                      plain
                      >{{ $t("coinRelease.common.确定") }}</el-button
                    >
                    <el-button
                      size="mini"
                      v-else
                      type="primary"
                      plain
                      @click="setFundAddress(temValue)"
                      >{{ $t("coinRelease.common.确定") }}</el-button
                    >
                  </span>
                </el-dialog>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </el-main>
    </div>
  </div>
</template>

<script>
import store from "@/store";
import chainParams from "@/contracts/coinReleaseParams.json";
import Mode1 from "@/contracts/pandaMode1.json";
import standardData from "@/contracts/standardCoin.json";
const { ethers, BigNumber } = require("ethers");
export default {
  name: "Dashboard",
  data() {
    return {
      standardData,
      chainParams,
      Mode1,
      ModeType: null,
      url: null,
      tokenAddress: null,
      tokenABI: null,
      partShow: true,
      temValue: null,
      temBool: true,
      addETH: null,
      bigEthers: null,
      lastestBlock: null,
      contractBalance: null,
      estimatePrice: null,
      basicParams: {
        _name: null,
        _symbol: null,
        _decimals: null,
        _supply: null,
        _owner: null,
      },
      ecoParams: {
        _buyFundFee: null,
        buy_burnFee: null,

        _sellFundFee: null,
        sell_burnFee: null,

        blockToUnlockLiquidity: null,
        maxWalletAmount: null,
        cooldownSec: null,

        fundAddress: null,

        enableWalletLimit: false,
        liquidityAdded: false,
      },
      dialogVisible: {
        addLiquidity: false,
        removeLiquidity: false,
        changeMAXHoldVisible: false,
        changeCooldownSec: false,
        changeFundVisible: false,
        extendLiquidityLock: false,
        setisMaxEatExempt: false,
        setExcludeCooling: false,
      },
      loading: {
        renounceOwner: false,
        extendLiquidityLock: false,
        addLiquidity: false,
        removeLiquidity: false,
        changeMAXHold: false,
        disableWalletLimit: false,
        changeCooldownSec: false,
        changeFund: false,
        setisMaxEatExempt: false,
        setExcludeCooling: false,
      },

      modeType: null,

      currencyOptions: [],
      swapOptions: [],

      CS: {
        "text-align": "left",
        "min-width": "120px",
        "word-break": "break-all",
      },
      LS: {
        "text-align": "center",
        height: "40px",
        "max-width": "70px",
        "word-break": "break-all",
      },
    };
  },

  created() {
    this.tokenAddress = this.$route.query.coinAddress;
    this.modeType = this.$route.query.modeType;
    this.url = window.location.href;
    this.tokenABI = standardData.Coin9abi;
    this.bigEthers = ethers.constants.WeiPerEther;
    const provider = store.state.user.provider;
    const signer = provider.getSigner();

    const tokenContract = new ethers.Contract(
      this.tokenAddress,
      this.tokenABI,
      signer
    );
    this.getBasicParams(tokenContract);
    let Inval = setInterval(() => {
      if (this.basicParams._owner) {
        if (
          ethers.utils.getAddress(this.basicParams._owner) !=
          ethers.utils.getAddress(store.state.user.address)
        ) {
          this.partShow = false;
        }
      }
      if (store.state.user.chainId) {
        window.clearInterval(Inval);
        const chainId = store.state.user.chainId;

        const selectChainParams = chainParams[chainId][5];
      }
    }, 1000);
    this.getEcoParams(tokenContract);
  },
  computed: {
    isMobile() {
      if (window.screen.width < 500) {
        return true;
      } else {
        return false;
      }
    },

    getBuyFee() {
      return (
        Number(this.ecoParams._buyFundFee) + Number(this.ecoParams.buy_burnFee)
      );
    },
    getSellFee() {
      return (
        Number(this.ecoParams._sellFundFee) +
        Number(this.ecoParams.sell_burnFee)
      );
    },
    getSupply() {
      if (this.basicParams._supply) {
        return parseFloat(
          new Number(this.basicParams._supply.toString()) /
            10 ** this.basicParams._decimals
        ).toFixed(6);
      } else {
        return null;
      }
    },

    getEstimatePrice() {
      return parseFloat(
        Number(this.addETH) / (Number(this.contractBalance) / 10 ** 18)
      ).toFixed(10);
    },
    getOwner() {
      if (this.basicParams._owner) {
        return (
          this.basicParams._owner.substring(0, 5) +
          "..." +
          this.basicParams._owner.substring(37)
        );
      } else {
        return null;
      }
    },
    getContract() {
      if (this.tokenAddress) {
        return (
          this.tokenAddress.substring(0, 5) +
          "..." +
          this.tokenAddress.substring(37)
        );
      } else {
        return null;
      }
    },

    getFund() {
      if (this.ecoParams.fundAddress) {
        return (
          this.ecoParams.fundAddress.substring(0, 5) +
          "..." +
          this.ecoParams.fundAddress.substring(37)
        );
      } else {
        return null;
      }
    },

    getMaxWalletAmount() {
      if (this.ecoParams.enableWalletLimit) {
        if (this.ecoParams.maxWalletAmount) {
          return parseFloat(
            new Number(this.ecoParams.maxWalletAmount.toString()) /
              10 ** this.basicParams._decimals
          ).toFixed(6);
        } else {
          return this.$t("coinRelease.common.获取中");
        }
      } else {
        return this.$t("coinRelease.common.无上限");
      }
    },
  },

  methods: {
    getBasicParams(contract) {
      contract
        .owner()
        .then((owner) => {
          this.basicParams._owner = owner;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t("coinRelease.common.获取合约拥有者错误请检查网络"),
          });
        });
      contract
        .balanceOf(this.tokenAddress)
        .then((_balance) => {
          this.contractBalance = _balance;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t("coinRelease.a314.获取合约余额错误请检查网络"),
          });
        });
      contract
        .name()
        .then((name) => {
          this.basicParams._name = name;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t("coinRelease.common.获取名称错误请检查网络"),
          });
        });
      contract
        .symbol()
        .then((symbol) => {
          this.basicParams._symbol = symbol;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t("coinRelease.common.获取简称错误请检查网络"),
          });
        });
      contract
        .decimals()
        .then((decimals) => {
          this.basicParams._decimals = decimals;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t("coinRelease.common.获取精度错误请检查网络"),
          });
        });
      contract
        .totalSupply()
        .then((supply) => {
          console.log("supply", typeof supply);
          this.basicParams._supply = supply;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t("coinRelease.common.获取供应量错误请检查网络"),
          });
        });
    },
    getEcoParams(contract) {
      contract
        ._buyFundFee()
        .then((_buyFundFee) => {
          this.ecoParams._buyFundFee = _buyFundFee / 100;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t(
              "coinRelease.common.获取买入营销费率错误请检查网络"
            ),
          });
        });

      contract
        .buy_burnFee()
        .then((buy_burnFee) => {
          this.ecoParams.buy_burnFee = buy_burnFee / 100;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t(
              "coinRelease.common.获取买入销毁费率错误请检查网络"
            ),
          });
        });
      contract
        ._sellFundFee()
        .then((_sellFundFee) => {
          this.ecoParams._sellFundFee = _sellFundFee / 100;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t(
              "coinRelease.common.获取卖出营销费率错误请检查网络"
            ),
          });
        });

      contract
        .sell_burnFee()
        .then((sell_burnFee) => {
          this.ecoParams.sell_burnFee = sell_burnFee / 100;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t(
              "coinRelease.common.获取卖出销毁费率错误请检查网络"
            ),
          });
        });

      contract
        .fundAddress()
        .then((fundAddress) => {
          this.ecoParams.fundAddress = fundAddress;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t("coinRelease.common.获取营销钱包错误请检查网络"),
          });
        });
      contract
        .cooldownSec()
        .then((cooldownSec) => {
          this.ecoParams.cooldownSec = cooldownSec;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t("coinRelease.common.获取冷却时间错误请检查网络"),
          });
        });
      contract
        .enableWalletLimit()
        .then((enableWalletLimit) => {
          this.ecoParams.enableWalletLimit = enableWalletLimit;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t(
              "coinRelease.common.获取持币限制开关错误请检查网络"
            ),
          });
        });
      contract
        .maxWalletAmount()
        .then((maxWalletAmount) => {
          this.ecoParams.maxWalletAmount = maxWalletAmount;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t(
              "coinRelease.common.获取持币限制开关错误请检查网络"
            ),
          });
        });
      contract
        .liquidityAdded()
        .then((liquidityAdded) => {
          this.ecoParams.liquidityAdded = liquidityAdded;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t("coinRelease.a314.获取加池确认错误请检查网络"),
          });
        });
      contract
        .blockToUnlockLiquidity()
        .then((blockToUnlockLiquidity) => {
          this.ecoParams.blockToUnlockLiquidity = blockToUnlockLiquidity;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t("coinRelease.a314.获取锁定区块错误请检查网络"),
          });
        });
    },
    getBigInt(floatNumber, tragetDecimals) {
      var floatArray = floatNumber.toString().split(".");
      let rsValue;
      if (floatArray.length == 1) {
        rsValue = BigNumber.from(floatArray[0]).mul(
          BigNumber.from(10).pow(tragetDecimals)
        );
      } else if (floatArray.length == 2) {
        var intPart = BigNumber.from(floatArray[0]).mul(
          BigNumber.from(10).pow(tragetDecimals)
        );
        var floatPart = BigNumber.from(floatArray[1]).mul(
          BigNumber.from(10).pow(tragetDecimals - floatArray[1].length)
        );
        rsValue = intPart.add(floatPart);
      } else {
        this.$message({
          type: "danger",
          message: this.$t("coinRelease.common.请输入正确的数字格式"),
        });
      }
      console.log(rsValue);
      return rsValue;
    },
    async getBlockNumberAdd() {
      const provider = store.state.user.provider;
      this.lastestBlock = await provider.getBlockNumber();
      this.dialogVisible.addLiquidity = true;
    },
    async getBlockNumberExtend() {
      const provider = store.state.user.provider;
      this.lastestBlock = await provider.getBlockNumber();
      this.dialogVisible.extendLiquidityLock = true;
    },
    AddLiquidity(temValue) {
      let _value = this.getBigInt(this.addETH, 18);
      let overrides = {
        value: _value,
      };
      this.loading.addLiquidity = true;
      const provider = store.state.user.provider;
      const signer = provider.getSigner();
      const tokenContract = new ethers.Contract(
        this.tokenAddress,
        this.tokenABI,
        signer
      );

      tokenContract
        .addLiquidity(temValue, overrides)
        .then((rs) => {
          this.dialogVisible.addLiquidity = false;
          this.loading.addLiquidity = false;
          this.temValue = null;
          this.$message({
            type: "success",
            message: this.$t("coinRelease.common.已提交等待区块确认"),
          });

          rs.wait()
            .then(() => {
              this.$message({
                type: "success",
                message: this.$t("coinRelease.common.修改成功"),
              });

              this.getEcoParams(tokenContract);
            })
            .catch((error) => {
              console.log("错误!", error);
              this.$message({
                type: "danger",
                message: this.$t("coinRelease.common.确认失败请前往浏览器查看"),
              });
            });
        })
        .catch((error) => {
          console.log("错误!", error);
          this.$message({
            type: "danger",
            message: this.$t("coinRelease.common.出错了"),
          });
          this.dialogVisible.addLiquidity = false;
          this.loading.addLiquidity = false;
          this.temValue = null;
        });
    },
    async RemoveLiquidity() {
      this.loading.removeLiquidity = true;
      const provider = store.state.user.provider;
      const signer = provider.getSigner();
      const nowBlock = await provider.getBlockNumber();
      if (nowBlock > this.ecoParams.blockToUnlockLiquidity) {
        const tokenContract = new ethers.Contract(
          this.tokenAddress,
          this.tokenABI,
          signer
        );
        tokenContract
          .removeLiquidity()
          .then((rs) => {
            this.loading.removeLiquidity = false;
            this.$message({
              type: "success",
              message: this.$t("coinRelease.common.已提交等待区块确认"),
            });

            rs.wait()
              .then(() => {
                this.$message({
                  type: "success",
                  message: this.$t(
                    "coinRelease.a314.撤池成功请在钱包内查看余额"
                  ),
                });
              })
              .catch((error) => {
                console.log("错误!", error);
                this.$message({
                  type: "danger",
                  message: this.$t(
                    "coinRelease.common.确认失败请前往浏览器查看"
                  ),
                });
              });
          })
          .catch((error) => {
            console.log("错误!", error);
            this.$message({
              type: "danger",
              message: this.$t("coinRelease.common.出错了"),
            });
            this.loading.removeLiquidity = false;
          });
      } else {
        this.$message({
          type: "danger",
          message: this.$t("coinRelease.a314.未到解锁时间无法撤出流动性"),
        });
        this.loading.removeLiquidity = false;
      }
    },
    disableWalletLimit() {
      this.loading.disableWalletLimit = true;
      const provider = store.state.user.provider;
      const signer = provider.getSigner();
      const tokenContract = new ethers.Contract(
        this.tokenAddress,
        this.tokenABI,
        signer
      );
      tokenContract
        .disableWalletLimit()
        .then((rs) => {
          this.$message({
            type: "success",
            message: this.$t("coinRelease.common.已提交等待区块确认"),
          });

          rs.wait()
            .then(() => {
              this.$message({
                type: "success",
                message: this.$t("coinRelease.common.修改成功"),
              });
              this.loading.disableWalletLimit = false;
              this.enable.enableWalletLimit = false;
            })
            .catch((error) => {
              console.log("错误!", error);
              this.$message({
                type: "danger",
                message: this.$t("coinRelease.common.确认失败请前往浏览器查看"),
              });
            });
        })
        .catch((error) => {
          console.log("错误!", error);
          this.$message({
            type: "danger",
            message: this.$t("coinRelease.common.出错了"),
          });
          this.loading.disableWalletLimit = false;
        });
    },
    changeWalletLimit(temValue) {
      this.loading.changeMAXHold = true;
      const provider = store.state.user.provider;
      const signer = provider.getSigner();
      const tokenContract = new ethers.Contract(
        this.tokenAddress,
        this.tokenABI,
        signer
      );
      const walletLimit = this.getBigInt(temValue, this.basicParams._decimals);
      if (walletLimit) {
        tokenContract
          .changeWalletLimit(walletLimit)
          .then((rs) => {
            this.dialogVisible.changeMAXHoldVisible = false;
            this.loading.changeMAXHold = false;
            this.temValue = null;
            this.$message({
              type: "success",
              message: this.$t("coinRelease.common.已提交等待区块确认"),
            });

            rs.wait()
              .then(() => {
                this.$message({
                  type: "success",
                  message: this.$t("coinRelease.common.修改成功"),
                });

                this.getEcoParams(tokenContract);
              })
              .catch((error) => {
                console.log("错误!", error);
                this.$message({
                  type: "danger",
                  message: this.$t(
                    "coinRelease.common.确认失败请前往浏览器查看"
                  ),
                });
              });
          })
          .catch((error) => {
            console.log("错误!", error);
            this.$message({
              type: "danger",
              message: this.$t("coinRelease.common.出错了"),
            });
            this.dialogVisible.changeMAXHoldVisible = false;
            this.loading.changeMAXHold = false;
            this.temValue = null;
          });
      } else {
        this.loading.changeMAXHold = false;
      }
    },
    setisMaxEatExempt(temValue, temBool) {
      console.log("dizhi", temValue);
      let isAddress = ethers.utils.isAddress(temValue);
      if (isAddress) {
        this.loading.setisMaxEatExempt = true;
        const provider = store.state.user.provider;
        const signer = provider.getSigner();
        const tokenContract = new ethers.Contract(
          this.tokenAddress,
          this.tokenABI,
          signer
        );
        tokenContract
          .setisMaxEatExempt(temValue, temBool)
          .then((rs) => {
            this.dialogVisible.setisMaxEatExempt = false;
            this.loading.setisMaxEatExempt = false;
            this.temValue = null;
            this.$message({
              type: "success",
              message: this.$t("coinRelease.common.已提交等待区块确认"),
            });

            rs.wait()
              .then(() => {
                this.$message({
                  type: "success",
                  message: this.$t("coinRelease.common.修改成功"),
                });

                this.getEcoParams(tokenContract);
              })
              .catch((error) => {
                console.log("错误!", error);
                this.$message({
                  type: "danger",
                  message: this.$t(
                    "coinRelease.common.确认失败请前往浏览器查看"
                  ),
                });
              });
          })
          .catch((error) => {
            console.log("错误!", error);
            this.$message({
              type: "danger",
              message: this.$t("coinRelease.common.出错了"),
            });
            this.dialogVisible.setisMaxEatExempt = false;
            this.loading.setisMaxEatExempt = false;
            this.temValue = null;
          });
      } else {
        this.$message({
          type: "danger",
          message: this.$t("coinRelease.common.地址格式不正确"),
        });
        this.loading.setisMaxEatExempt = false;
      }
    },
    extendLiquidityLock(temValue) {
      if (Number(temValue) <= this.ecoParams.blockToUnlockLiquidity) {
        this.$message({
          type: "danger",
          message: this.$t(
            "coinRelease.a314.新的解锁区块需大于之前设定的解锁区块"
          ),
        });
      } else {
        this.loading.extendLiquidityLock = true;
        const provider = store.state.user.provider;
        const signer = provider.getSigner();
        const tokenContract = new ethers.Contract(
          this.tokenAddress,
          this.tokenABI,
          signer
        );

        tokenContract
          .extendLiquidityLock(temValue)
          .then((rs) => {
            this.dialogVisible.extendLiquidityLock = false;
            this.loading.extendLiquidityLock = false;
            this.temValue = null;
            this.$message({
              type: "success",
              message: this.$t("coinRelease.common.已提交等待区块确认"),
            });

            rs.wait()
              .then(() => {
                this.$message({
                  type: "success",
                  message: this.$t("coinRelease.common.修改成功"),
                });

                this.getEcoParams(tokenContract);
              })
              .catch((error) => {
                console.log("错误!", error);
                this.$message({
                  type: "danger",
                  message: this.$t(
                    "coinRelease.common.确认失败请前往浏览器查看"
                  ),
                });
              });
          })
          .catch((error) => {
            console.log("错误!", error);
            this.$message({
              type: "danger",
              message: this.$t("coinRelease.common.出错了"),
            });
            this.dialogVisible.extendLiquidityLock = false;
            this.loading.extendLiquidityLock = false;
            this.temValue = null;
          });
      }
    },
    changeCooldownSec(temValue) {
      if (temValue == 0 || temValue > 60) {
        this.$message({
          type: "danger",
          message: this.$t("coinRelease.a314.冷却时间需在之间"),
        });
      } else {
        this.loading.changeCooldownSec = true;
        const provider = store.state.user.provider;
        const signer = provider.getSigner();
        const tokenContract = new ethers.Contract(
          this.tokenAddress,
          this.tokenABI,
          signer
        );

        tokenContract
          .changeCooldownSec(temValue)
          .then((rs) => {
            this.dialogVisible.changeCooldownSec = false;
            this.loading.changeCooldownSec = false;
            this.temValue = null;
            this.$message({
              type: "success",
              message: this.$t("coinRelease.common.已提交等待区块确认"),
            });

            rs.wait()
              .then(() => {
                this.$message({
                  type: "success",
                  message: this.$t("coinRelease.common.修改成功"),
                });

                this.getEcoParams(tokenContract);
              })
              .catch((error) => {
                console.log("错误!", error);
                this.$message({
                  type: "danger",
                  message: this.$t(
                    "coinRelease.common.确认失败请前往浏览器查看"
                  ),
                });
              });
          })
          .catch((error) => {
            console.log("错误!", error);
            this.$message({
              type: "danger",
              message: this.$t("coinRelease.common.出错了"),
            });
            this.dialogVisible.changeCooldownSec = false;
            this.loading.changeCooldownSec = false;
            this.temValue = null;
          });
      }
    },
    setExcludeCooling(temValue, temBool) {
      console.log("dizhi", temValue);
      let isAddress = ethers.utils.isAddress(temValue);
      if (isAddress) {
        this.loading.setExcludeCooling = true;
        const provider = store.state.user.provider;
        const signer = provider.getSigner();
        const tokenContract = new ethers.Contract(
          this.tokenAddress,
          this.tokenABI,
          signer
        );
        tokenContract
          .setExcludeCooling(temValue, temBool)
          .then((rs) => {
            this.dialogVisible.setExcludeCooling = false;
            this.loading.setExcludeCooling = false;
            this.temValue = null;
            this.$message({
              type: "success",
              message: this.$t("coinRelease.common.已提交等待区块确认"),
            });

            rs.wait()
              .then(() => {
                this.$message({
                  type: "success",
                  message: this.$t("coinRelease.common.修改成功"),
                });
              })
              .catch((error) => {
                console.log("错误!", error);
                this.$message({
                  type: "danger",
                  message: this.$t(
                    "coinRelease.common.确认失败请前往浏览器查看"
                  ),
                });
              });
          })
          .catch((error) => {
            console.log("错误!", error);
            this.$message({
              type: "danger",
              message: this.$t("coinRelease.common.出错了"),
            });
            this.dialogVisible.setExcludeCooling = false;
            this.loading.setExcludeCooling = false;
            this.temValue = null;
          });
      } else {
        this.$message({
          type: "danger",
          message: this.$t("coinRelease.common.地址格式不正确"),
        });
        this.loading.setExcludeCooling = false;
      }
    },
    setFundAddress(temValue) {
      let isAddress = ethers.utils.isAddress(temValue);
      if (isAddress) {
        this.loading.changeFund = true;
        const provider = store.state.user.provider;
        const signer = provider.getSigner();
        const tokenContract = new ethers.Contract(
          this.tokenAddress,
          this.tokenABI,
          signer
        );
        tokenContract
          .setFundAddress(temValue)
          .then((rs) => {
            this.dialogVisible.changeFundVisible = false;
            this.loading.changeFund = false;
            this.temValue = null;
            this.$message({
              type: "success",
              message: this.$t("coinRelease.common.已提交等待区块确认"),
            });

            rs.wait()
              .then(() => {
                this.$message({
                  type: "success",
                  message: this.$t("coinRelease.common.修改成功"),
                });

                this.getEcoParams(tokenContract);
              })
              .catch((error) => {
                console.log("错误!", error);
                this.$message({
                  type: "danger",
                  message: this.$t(
                    "coinRelease.common.确认失败请前往浏览器查看"
                  ),
                });
              });
          })
          .catch((error) => {
            console.log("错误!", error);
            this.$message({
              type: "danger",
              message: this.$t("coinRelease.common.出错了"),
            });
            this.dialogVisible.changeFundVisible = false;
            this.loading.changeFund = false;
            this.temValue = null;
          });
      } else {
        this.$message({
          type: "danger",
          message: this.$t("coinRelease.common.地址格式不正确"),
        });
        this.loading.changeFund = false;
      }
    },
    renounceOwnership() {
      this.loading.renounceOwner = true;
      const provider = store.state.user.provider;
      const signer = provider.getSigner();
      const tokenContract = new ethers.Contract(
        this.tokenAddress,
        this.tokenABI,
        signer
      );
      tokenContract
        .renounceOwnership()
        .then((rs) => {
          this.$message({
            type: "success",
            message: this.$t("coinRelease.common.已提交等待区块确认"),
          });

          rs.wait()
            .then(() => {
              this.$message({
                type: "success",
                message: this.$t("coinRelease.common.修改成功"),
              });
              this.loading.renounceOwner = false;
              this.getBasicParams(tokenContract);
              this.partShow = false;
            })
            .catch((error) => {
              console.log("错误!", error);
              this.$message({
                type: "danger",
                message: this.$t("coinRelease.common.确认失败请前往浏览器查看"),
              });
            });
        })
        .catch((error) => {
          console.log("错误!", error);
          this.$message({
            type: "danger",
            message: this.$t("coinRelease.common.出错了"),
          });

          this.loading.renounceOwner = false;
          this.temValue = null;
        });
    },
    copy_str(text) {
      if (navigator.clipboard) {
        // clipboard api 复制
        navigator.clipboard.writeText(text);
      } else {
        var textarea = document.createElement("textarea");
        document.body.appendChild(textarea);
        // 隐藏此输入框
        textarea.style.position = "fixed";
        textarea.style.clip = "rect(0 0 0 0)";
        textarea.style.top = "10px";
        // 赋值
        textarea.value = text;
        // 选中
        textarea.select();
        // 复制
        document.execCommand("copy", true);
        // 移除输入框
        document.body.removeChild(textarea);
      }
      this.$message({
        type: "success",
        message: this.$t("coinRelease.common.已复制"),
      });
    },
    checkDecimal(num) {
      var str = num;
      var len1 = str.substr(0, 1);
      var len2 = str.substr(1, 1);
      //如果第一位是0，第二位不是点，就用数字把点替换掉
      if (str.length > 1 && len1 == 0 && len2 != ".") {
        str = str.substr(1, 1);
      }
      //第一位不能是.
      if (len1 == ".") {
        str = "";
      }
      //限制只能输入一个小数点
      if (str.indexOf(".") != -1) {
        var str_ = str.substr(str.indexOf(".") + 1);
        if (str_.indexOf(".") != -1) {
          str = str.substr(0, str.indexOf(".") + str_.indexOf(".") + 1);
        }
      }
      //正则替换
      str = str.replace(/[^\d^\.]+/g, ""); // 保留数字和小数点
      str = str.replace(/\.\d\d\d$/, ""); // 小数点后只能输两位
      return str;
    },
  },
};
</script>

<style scoped>
@media screen and (max-width: 500px) {
  .main {
    text-align: center;
    margin-top: 5%;
    padding-left: 0;
    padding-right: 0;
  }
  .row {
    width: 100%;
    margin-bottom: 10px;
  }
  .box-card {
    margin-bottom: 20px;
    text-align: center;
    width: 100%;
  }
  .controlbutton {
    margin-bottom: 10px;
  }
}
@media screen and (min-width: 500px) {
  .header {
    text-align: center;
    margin-top: 0px;
    font-size: larger;
  }
  .main {
    /* display: flexbox;
    justify-content: center; */
    margin-top: 0%;
    padding-left: 5%;
    padding-right: 5%;
  }
  .row {
    width: 100%;
    margin-bottom: 10px;
  }

  .col {
    /* margin-left: 20px; */
    border-radius: 4px;
  }
  .box-card {
    margin-bottom: 20px;
    text-align: center;
    width: 100%;
  }
  .controlbutton {
    margin-bottom: 10px;
  }
}
</style>
