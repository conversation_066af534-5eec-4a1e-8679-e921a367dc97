<template>
  <div class="app-container">
    <div class="inner">
      <el-alert
        v-show="support"
        :title="$t('lock.暂不支持此链')"
        type="error"
        :description="$t('lock.联系定制')"
        center
        :closable="false"
        show-icon
      >
      </el-alert>
      <el-header class="header" style="margin-bottom: 25px">
        <p>
          {{ $t("lock.创建锁仓") }}
          <a
            style="
              margin-left: 10px;
              font-size: 12px;
              font-weight: 400;
              color: #409eff;
            "
            :href="helpURL"
            target="_blank"
            >{{ $t("coinRelease.common.教程") }}</a
          >
        </p>
        <p style="font-size: 14px; font-weight: 300">
          {{ $t("lock.锁仓说明") }}
        </p>
      </el-header>
      <el-main class="main">
        <el-form>
          <el-form-item :label="$t('lock.锁仓代币地址')">
            <el-input
              v-model="tokenAddress"
              :placeholder="$t('lock.请输入代币地址')"
            />
          </el-form-item>
          <el-row type="flex" justify="space-around">
            <el-button v-if="checkLoading" :loading="true" type="primary">{{
              $t("lock.请稍后")
            }}</el-button>

            <el-button v-else type="primary" @click="checkToken">{{
              $t("lock.查询代币")
            }}</el-button>
          </el-row>

          <el-form-item :label="$t('lock.锁仓数量')">
            <el-row> </el-row>
            <el-row style="margin-top: 5px">
              <el-col :xs="4" :sm="3" style="margin-top: 10px"
                >{{ getTokenName }}:</el-col
              >

              <el-col :xs="24" :sm="10" style="margin-top: 10px">
                <el-input
                  v-model="lockTokenNum"
                  @input="handleLockToken()"
                  oninput="value=value.replace(/[^0-9.]/g,'')"
                  placeholder="0.000000000000000001"
                >
                </el-input>

                <i
                  class="el-icon-wallet"
                  style="
                    margin-left: 10px;
                    font-size: 12px;
                    font-weight: 400;
                    color: #409eff;
                  "
                  @click="addAll(0)"
                  >{{ $t("lock.全部") }}</i
                >
              </el-col>
              <el-col
                :xs="12"
                :sm="5"
                style="
                  margin-top: 10px;
                  font-size: 12px;

                  margin-left: 10px;
                "
                >{{ $t("lock.余额") }}:{{ getTokenBalance }}
              </el-col>
            </el-row>
          </el-form-item>
          <el-form-item :label="$t('lock.解锁日期')">
            <el-date-picker
              v-model="unlockTime"
              type="datetime"
              value-format="timestamp"
              :picker-options="pickerOptions"
              :placeholder="$t('lock.请选择解锁日期')"
            />
          </el-form-item>
          <el-form-item :label="$t('lock.锁仓标题')">
            <el-input
              style="width: 70%"
              v-model="lockTitle"
              type="text"
              :placeholder="$t('lock.请输入锁仓标题')"
            />
          </el-form-item>

          <el-form-item :label="$t('lock.授权')">
            <el-button
              style="margin-left: 20px"
              v-if="approvedTokenLoading"
              :loading="true"
              type="primary"
              plain
              @click="approveToken"
              size="medium"
              >{{ $t("lock.正在授权") }}{{ getTokenName }}</el-button
            >
            <el-button
              style="margin-left: 20px"
              v-else-if="approvedToken"
              type="success"
              plain
              disabled
              size="medium"
              >{{ getTokenName }}{{ $t("lock.已授权") }}</el-button
            >
            <el-button
              style="margin-left: 20px"
              v-else
              type="primary"
              plain
              @click="approveToken"
              size="medium"
              >{{ $t("lock.授权") }}{{ getTokenName }}</el-button
            >
          </el-form-item>
        </el-form>
        <p style="font-size: 14px; font-weight: 300">
          {{ $t("lock.有限制功能的代币") }}
        </p>

        <el-row type="flex" justify="space-around" style="margin-top: 20px">
          <div>
            <el-button v-if="lockLoading" type="primary" :loading="true">{{
              $t("lock.请稍后")
            }}</el-button>

            <el-button
              v-else-if="lockCenter && !lockLoading"
              type="info"
              plain
              disabled
              >{{ $t("lock.锁仓完成") }}</el-button
            >
            <el-button v-else type="primary" @click="submitLock">{{
              $t("lock.立即锁仓")
            }}</el-button>
            <span style="font-size: 12px; margin-left: 10px"
              >{{ $t("coinRelease.common.费用") }}:{{ getCostAndSymbol }}</span
            >
          </div>
        </el-row>
      </el-main>
    </div>
  </div>
</template>

<script>
import CoinData from "@/contracts/PancakeERC20.json";
import lpManageABI from "@/contracts/lpManageABI.json";
import factoryABI from "@/contracts/PancakeFactory.json";
import lpAddParam from "@/contracts/lpAddParam.json";
import chainParams from "@/contracts/coinReleaseParams.json";
import store from "@/store";
const { ethers, BigNumber } = require("ethers");
const supportChain = [56, 97];
const tokenABI = [
  "function balanceOf(address owner) view returns (uint256)",
  "function decimals() view returns (uint8)",
  "function symbol() view returns (string)",
  "function transfer(address to, uint amount) returns (bool)",
  "function approve(address spender, uint256 amount) external returns (bool)",
  "function allowance(address owner, address spender) external view returns (uint256)",
];
export default {
  name: "Dashboard",
  data() {
    return {
      CoinData,
      factoryABI,
      lpManageABI,
      chainParams,
      lpAddParam,
      store,
      support: null,
      supportChain,
      helpURL:
        this.$i18n.locale == "en-US"
          ? "https://help.pandatool.org/english/createtoken/createlock"
          : "https://help.pandatool.org/createtoken/createlock",
      tokenABI,
      baseAddress: null,
      chainSymbol: null,
      Fee: null,

      poolType: "v2",
      lpManageAddress: null,
      routerAddress: null,
      factoryAddress: null,
      pinkLockAddr: null,
      poolABI: null,
      isLP: false,
      unlockTime: null,
      lockTitle: null,
      tokenAddress: null,
      tokenName: null,
      tokenBalance: null,
      tokenDecimals: null,

      lockTokenNum: null,
      ETHCost: null,

      approvedToken: false,

      approvedTokenLoading: false,

      checkLoading: false,

      lockLoading: false,
      lockCenter: false,
      pickerOptions: {
        disabledDate(time) {
          // 禁用明日以前的时间
          return time.getTime() < Date.now() + 86400; // 加一天的时间戳
        },
      },
    };
  },
  created() {
    let Inval = setInterval(() => {
      if (store.state.user.chainId) {
        window.clearInterval(Inval);
        const chainId = store.state.user.chainId;
        if (supportChain.includes(chainId)) {
          this.support = false;
        } else {
          this.support = true;
        }
        const selectChainParams = chainParams[chainId][6];
        this._WETH = ethers.utils.getAddress(selectChainParams._WETH);
        this.baseAddress = lpAddParam[chainId];
        this.routerAddress = this.baseAddress.v2Router;
        this.factoryAddress = this.baseAddress.v2Factory;
        this.Fee = this.baseAddress.AddRemoveFee;
        // console.log("this.baseAddress", this.baseAddress);
        this.chainSymbol = selectChainParams.chainSymbol;
        this.lpManageAddress = lpAddParam[chainId].lpManage;
        this.poolABI = CoinData.abi;

        this.pinkLockAddr = this.baseAddress.pinkLockAddr;
      }
    }, 1000);
  },
  computed: {
    getTokenName() {
      if (this.tokenName == null) {
        return "???";
      } else {
        return this.tokenName;
      }
    },
    getTokenBalance() {
      if (this.tokenBalance == null) {
        return "???";
      } else {
        return new Number(
          ethers.utils.formatUnits(this.tokenBalance, this.tokenDecimals)
        )
          .toFixed(6)
          .toString();
      }
    },
    getCostAndSymbol() {
      return this.Fee + " " + this.chainSymbol;
    },
    getPoolAddress() {
      if (this.poolAddress == null) {
        return this.$t("lock.未创建");
      } else {
        return this.poolAddress;
      }
    },
  },
  methods: {
    async checkToken() {
      this.checkLoading = true;

      const provider = store.state.user.provider;
      let isToken = ethers.utils.isAddress(this.tokenAddress);

      let tokenCode;
      if (!isToken) {
        this.$message({
          type: "error",
          message: this.$t("lock.代币地址地址不正确") + "!",
        });
        this.checkLoading = false;
        return;
      }
      try {
        tokenCode = await provider.getCode(this.tokenAddress);
        if (tokenCode == "0x") {
          this.$message({
            type: "error",
            message: this.$t("lock.代币地址地址不正确") + "!",
          });
          this.checkLoading = false;
          return;
        }
      } catch {
        this.$message({
          type: "error",
          message: this.$t("lock.代币地址地址不正确") + "!",
        });
        this.checkLoading = false;
        return;
      }

      this.tokenAddress = ethers.utils.getAddress(this.tokenAddress);

      await this.getBasicParams();
      this.checkLoading = false;
    },
    async getBasicParams() {
      const provider = store.state.user.provider;
      const signer = provider.getSigner();
      const tokenContract = new ethers.Contract(
        this.tokenAddress,
        this.tokenABI,
        signer
      );
      tokenContract
        .symbol()
        .then((symbol) => {
          this.tokenName = symbol;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t("lock.获取代币简称错误") + "!",
          });
        });
      tokenContract
        .balanceOf(store.state.user.address)
        .then((temBalance) => {
          this.tokenBalance = temBalance;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t("lock.获取代币余额错误") + "!",
          });
        });
      tokenContract
        .decimals()
        .then((decimals) => {
          this.tokenDecimals = decimals;
        })
        .catch(() => {
          this.$message({
            type: "danger",
            message: this.$t("lock.获取代币精度错误") + "!",
          });
        });
      try {
        const poolContract = new ethers.Contract(
          this.tokenAddress,
          this.poolABI,
          signer
        );
        const factoryAddr = await poolContract.factory();
        if (
          factoryAddr == this.factoryAddress ||
          factoryAddr != "******************************************"
        ) {
          this.isLP = true;
        }
        this.$message({
          type: "success",
          message: this.$t("lock.代币正常") + "!",
        });
      } catch (error) {
        console.log("错误!", error);
        this.$message({
          type: "success",
          message: this.$t("lock.代币正常") + "!",
        });
      }
      this.checkLoading = false;
    },
    handleLockToken() {
      const temNum = ethers.utils.parseUnits(
        this.lockTokenNum.toString(),
        this.tokenDecimals
      );
      if (temNum.gt(this.tokenBalance)) {
        this.$message({
          type: "danger",
          message: this.$t("lock.锁仓数量超过余额") + "!",
        });
      }
      if (!this.lockTokenNum || temNum.eq(ethers.constants.Zero)) {
        this.$message({
          type: "danger",
          message: this.$t("lock.锁仓数量不能为0") + "!",
        });
      }
    },

    approveToken() {
      this.approvedTokenLoading = true;
      if (!this.tokenDecimals) {
        this.approvedTokenLoading = false;

        this.$message({
          type: "danger",
          message: this.$t("lock.请先查询代币") + "!",
        });
        return;
      }
      this.approve(this.tokenAddress, this.tokenDecimals)
        .then((rs) => {
          this.approvedToken = true;
          this.approvedTokenLoading = false;
        })
        .catch((error) => {
          console.log("错误!", error);
          this.$message({
            type: "danger",
            message: this.$t("lock.授权失败请重试"),
          });
          this.approvedTokenLoading = false;
        });
    },

    async approve(tokenAddress, decimals) {
      const abi = [
        "function balanceOf(address owner) view returns (uint256)",
        "function decimals() view returns (uint8)",
        "function symbol() view returns (string)",
        "function transfer(address to, uint amount) returns (bool)",
        "function approve(address spender, uint256 amount) external returns (bool)",
        "function allowance(address owner, address spender) external view returns (uint256)",
      ];
      const provider = store.state.user.provider;
      const signer = provider.getSigner();
      const ContractAddress = ethers.utils.getAddress(tokenAddress);
      const erc20 = new ethers.Contract(ContractAddress, abi, signer);

      var getAllowance = await erc20.allowance(
        store.state.user.address,
        this.lpManageAddress
      );

      const bigApproveAmount = ethers.utils.parseUnits(
        "10000000000000000000",
        decimals
      );

      if (getAllowance.gt(bigApproveAmount)) {
        return true;
      } else {
        var unlimited =
          "115792089237316195423570985008687907853269984665640564039457584007913129639935";
        const tx = await erc20.approve(this.lpManageAddress, unlimited);
        this.$message({
          type: "success",
          message: this.$t("lock.已提交等待区块确认") + "!",
        });
        await tx.wait();
        this.$message({
          type: "success",
          message: this.$t("lock.授权成功") + "!",
        });
        return true;
      }
    },
    async submitLock() {
      this.lockLoading = true;
      if (!this.tokenDecimals) {
        this.lockLoading = false;

        this.$message({
          type: "danger",
          message: this.$t("lock.请先查询代币") + "!",
        });
        return;
      }

      const bigLockToken = ethers.utils.parseUnits(
        this.lockTokenNum,
        this.tokenDecimals
      );

      if (bigLockToken.gt(this.tokenBalance)) {
        this.$message({
          type: "danger",
          message: this.$t("lock.锁仓数量超过余额") + "!",
        });
        this.lockLoading = false;
        return;
      }
      if (bigLockToken.eq(ethers.constants.Zero)) {
        this.$message({
          type: "danger",
          message: this.$t("lock.锁仓数量不能为0") + "!",
        });
        this.lockLoading = false;
        return;
      }

      const provider = store.state.user.provider;
      const signer = provider.getSigner();
      const tokenContract = new ethers.Contract(
        this.tokenAddress,
        this.tokenABI,
        signer
      );

      var getAllowanceToken = await tokenContract.allowance(
        store.state.user.address,
        this.lpManageAddress
      );

      if (getAllowanceToken.lt(bigLockToken)) {
        this.lockLoading = false;
        this.$message({
          type: "danger",
          message: this.$t("lock.锁仓代币未授权") + "!",
        });
        this.lockLoading = false;
        return;
      }
      const lpManageContract = new ethers.Contract(
        this.lpManageAddress,
        this.lpManageABI,
        signer
      );
      const bigFee = ethers.utils.parseEther(this.Fee);
      let overrides = { value: bigFee };
      const unlockTime = Math.floor(new Number(this.unlockTime) / 1000);

      try {
        const lockRs = await lpManageContract.lockToken(
          this.pinkLockAddr,
          this.tokenAddress,
          bigLockToken,
          unlockTime,
          this.isLP,
          this.lockTitle ? this.lockTitle : " ",
          overrides
        );
        this.$message({
          type: "success",
          message: this.$t("lock.已提交等待区块确认") + "!",
        });
        await lockRs.wait();
        this.$message({
          type: "success",
          message: this.$t("lock.锁仓成功") + "!",
        });

        this.lockLoading = false;
        this.lockCenter = true;
      } catch (error) {
        console.log("错误!", error);
        this.$message({
          type: "danger",
          message: this.$t("lock.锁仓失败") + "!",
        });
        this.lockLoading = false;
      }

      this.lockLoading = false;
    },

    addAll(num) {
      if (num == 0) {
        this.lockTokenNum = parseFloat(
          this.tokenBalance / 10 ** this.tokenDecimals
        )
          .toFixed(6)
          .toString();
      } else {
        this.addCurrencyNum = parseFloat(
          new Number(this.currencyBalance) /
            10 ** new Number(this.currencyDecimals)
        )
          .toFixed(6)
          .toString();
      }
    },
    enterManage() {
      this.$router.push({
        path: "/LPmanage/",
      });
    },

    copy_str(text) {
      if (navigator.clipboard) {
        // clipboard api 复制
        navigator.clipboard.writeText(text);
      } else {
        var textarea = document.createElement("textarea");
        document.body.appendChild(textarea);
        // 隐藏此输入框
        textarea.style.position = "fixed";
        textarea.style.clip = "rect(0 0 0 0)";
        textarea.style.top = "10px";
        // 赋值
        textarea.value = text;
        // 选中
        textarea.select();
        // 复制
        document.execCommand("copy", true);
        // 移除输入框
        document.body.removeChild(textarea);
      }
      this.$message({
        type: "success",
        message: this.$t("lock.已复制") + "!",
      });
    },
  },
};
</script>

<style scoped>
@media screen and (max-width: 500px) {
}
@media screen and (min-width: 500px) {
  .el-input {
    width: 80%;
  }
  .el-select {
    width: 80%;
  }
}
</style>
