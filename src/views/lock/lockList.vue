<template>
  <div class="app-container">
    <div class="inner">
      <el-alert
        v-show="support"
        :title="$t('lock.暂不支持此链')"
        type="error"
        :description="$t('lock.联系定制')"
        center
        :closable="false"
        show-icon
      >
      </el-alert>
      <el-header class="header" style="margin-bottom: 25px">
        <p>
          {{ $t("lock.锁仓控制台") }}
          <a
            style="
              margin-left: 10px;
              font-size: 12px;
              font-weight: 400;
              color: #409eff;
            "
            :href="helpURL"
            target="_blank"
            >{{ $t("coinRelease.common.教程") }}</a
          >
        </p>
        <p style="font-size: 14px; font-weight: 300">
          {{ $t("lock.锁仓控制台说明") }}
        </p>
      </el-header>
      <div class="lock-container">
        <!-- 类型选择区域 -->

        <el-row
          class="type-switch"
          type="flex"
          justify="space-between"
          :gutter="10"
        >
          <el-col :xs="24" :sm="14">
            <div>
              {{ $t("lock.锁仓类型") }}
              :
              <el-radio-group
                v-model="lockType"
                size="small"
                @change="changeTypeList"
              >
                <el-radio-button label="LP">{{
                  $t("lock.锁池")
                }}</el-radio-button>
                <el-radio-button label="Token">{{
                  $t("lock.锁币")
                }}</el-radio-button>
              </el-radio-group>
            </div>
          </el-col>

          <el-col :xs="24" :sm="10" class="text-right">
            <el-button
              size="small"
              type="info"
              plain
              @click="fetchDialogVisible = !fetchDialogVisible"
              >{{ $t("lock.查找锁仓") }}</el-button
            >
          </el-col>
        </el-row>
        <el-dialog
          :title="$t('lock.查找锁仓')"
          :visible.sync="fetchDialogVisible"
          :close-on-click-modal="false"
          show-close
        >
          <el-form>
            <el-form-item :label="$t('lock.代币地址')">
              <el-input
                v-model="tokenAddress"
                :placeholder="$t('lock.请输入代币地址')"
              />
            </el-form-item>
          </el-form>

          <span slot="footer" class="dialog-footer">
            <el-button
              v-show="goCreate"
              type="primary"
              plain
              @click="enterCreatePool"
              >{{ $t("lock.前往锁仓") }}</el-button
            >
            <el-button v-if="checkLoading" :loading="true" type="primary">{{
              $t("lock.请稍后")
            }}</el-button>
            <el-button v-else type="primary" @click="checkLock">{{
              $t("lock.查询锁仓")
            }}</el-button>
          </span>
        </el-dialog>

        <div
          v-if="!LockListLoading && LockList.length === 0"
          class="noLiquidity"
        >
          {{ $t("lock.当前地址未发现锁仓记录") }}
        </div>

        <el-card
          v-else
          class="lock-card"
          v-for="(item, index) in LockList"
          :key="index"
        >
          <el-row :gutter="20" class="card-row">
            <el-col :xs="12" :sm="4" class="item">
              <div class="label">{{ $t("lock.代币简称") }}</div>
              <div class="LPValue">
                {{ item.tokenSymbol }}
              </div>
            </el-col>

            <el-col :xs="12" :sm="5" class="item">
              <div class="label">{{ $t("lock.代币地址") }}</div>
              <div class="LPValue">
                {{ getShortAddr(item.tokenAddress) }}
                <i
                  class="el-icon-copy-document"
                  style="
                    margin-left: 2px;
                    font-size: 12px;
                    font-weight: 400;
                    color: #409eff;
                  "
                  @click="copy_str(item.tokenAddress)"
                  >{{ $t("lock.复制") }}</i
                >
              </div>
            </el-col>

            <el-col :xs="12" :sm="4" class="item">
              <div class="label">
                {{ $t("lock.锁仓数量") }}
              </div>
              <div class="LPValue">
                {{ getLockAmount(item.lockAmount, item.tokenDecimals) }}
              </div>
            </el-col>

            <el-col :xs="12" :sm="5" class="item">
              <div class="label">{{ $t("lock.解锁日期") }}</div>
              <div class="LPValue">
                {{ getLockDate(item.tgeDate) }}
              </div>
            </el-col>

            <el-col :xs="12" :sm="3" class="item">
              <div class="label">{{ $t("lock.解锁") }}</div>
              <el-button
                v-if="unlockLoading"
                :loading="true"
                size="small"
                plain
              ></el-button>
              <el-button
                v-else-if="!unlockLoading && checkLockTime(item.tgeDate)"
                disabled
                type="info"
                size="small"
                plain
                >🔑</el-button
              >
              <el-button v-else @click="unlock(item)" size="small" plain
                >🔑</el-button
              >
            </el-col>

            <el-col :xs="12" :sm="3" class="item">
              <div class="label">
                {{ $t("lock.弃权") }}
                <el-tooltip placement="top" effect="light">
                  <div style="font-size: 14px; font-weight: 300" slot="content">
                    {{ $t("lock.弃权说明") }}<br /><br />

                    {{ $t("lock.弃权将永久锁仓") }}<br /><br />
                  </div>
                  <i class="el-icon-warning-outline" />
                </el-tooltip>
              </div>
              <el-button
                v-if="quitLoading"
                :loading="true"
                size="small"
                plain
              ></el-button>
              <el-button
                v-else-if="
                  !quitLoading && item.owner != store.state.user.address
                "
                disabled
                type="info"
                size="small"
                plain
                >{{ $t("lock.弃权") }}</el-button
              >
              <el-button
                v-else
                @click="renounceOwner(item)"
                size="small"
                plain
                >{{ $t("lock.弃权") }}</el-button
              >
            </el-col>
          </el-row>
        </el-card>
        <div
          v-loading="LockListLoading"
          :element-loading-text="$t('lock.正在加载')"
          class="spinner"
        ></div>
      </div>
    </div>
  </div>
</template>

<script>
import CoinData from "@/contracts/PancakeERC20.json";
import lpManageABI from "@/contracts/lpManageABI.json";
import PinkLockABI from "@/contracts/PinkLockABI.json";
import lpAddParam from "@/contracts/lpAddParam.json";
import chainParams from "@/contracts/coinReleaseParams.json";
import store from "@/store";
const { ethers, BigNumber } = require("ethers");
const supportChain = [56, 97];
const tokenABI = [
  "function balanceOf(address owner) view returns (uint256)",
  "function decimals() view returns (uint8)",
  "function symbol() view returns (string)",
  "function transfer(address to, uint amount) returns (bool)",
  "function approve(address spender, uint256 amount) external returns (bool)",
  "function allowance(address owner, address spender) external view returns (uint256)",
];
export default {
  name: "LiquidityList",
  data() {
    return {
      CoinData,
      PinkLockABI,
      lpManageABI,
      chainParams,
      lpAddParam,
      store,
      support: null,
      supportChain,
      helpURL:
        this.$i18n.locale == "en-US"
          ? "https://help.pandatool.org/english/createtoken/lockmanage"
          : "https://help.pandatool.org/createtoken/lockmanage",
      tokenABI,
      baseAddress: null,
      chainSymbol: null,
      Fee: null,
      pinkLockAddr: null,

      lockType: "LP",
      lpManageAddress: null,
      routerAddress: null,
      factoryAddress: null,
      tokenAddress: null,
      poolABI: null,
      tokenisETH: 2,
      LockListLoading: true,
      LockList: [],

      checkLoading: false,

      quitLoading: false,
      unlockLoading: false,
      fetchDialogVisible: false,
      fetchLoading: false,
      goCreate: false,
    };
  },
  created() {
    let Inval = setInterval(() => {
      if (store.state.user.chainId) {
        window.clearInterval(Inval);
        const chainId = store.state.user.chainId;
        if (supportChain.includes(chainId)) {
          this.support = false;
        } else {
          this.support = true;
        }
        const selectChainParams = chainParams[chainId][6];
        this._WETH = ethers.utils.getAddress(selectChainParams._WETH);
        this.baseAddress = lpAddParam[chainId];

        this.Fee = this.baseAddress.AddRemoveFee;
        this.recipient = store.state.user.address;
        this.pinkLockAddr = this.baseAddress.pinkLockAddr;
        this.chainSymbol = selectChainParams.chainSymbol;
        this.lpManageAddress = lpAddParam[chainId].lpManage;
        this.poolABI = CoinData.abi;

        this.getLockList();
      }
    }, 1000);
  },
  computed: {
    getToken0Name() {
      if (this.LPItem == null) {
        return "???";
      } else {
        return this.LPItem.token0Symbol;
      }
    },
  },
  methods: {
    changeTypeList(val) {
      this.lockType = val;
      this.getLockList();
    },
    getLockDate(timestamp) {
      if (timestamp == null) {
        return "???";
      } else {
        const date = new Date(new Number(timestamp) * 1000);
        return date.toLocaleString();
      }
    },
    checkLockTime(timestamp) {
      if (timestamp == null) {
        return false;
      } else {
        const unlockTime = new Number(timestamp) * 1000;
        return Date.now() < unlockTime;
      }
    },
    getLockAmount(amount, decimals) {
      if (amount == null) {
        return "???";
      } else {
        return new Number(ethers.utils.formatUnits(amount, decimals))
          .toFixed(6)
          .toString();
      }
    },
    getShortAddr(address) {
      if (ethers.utils.isAddress(address)) {
        return address.slice(0, 5) + "..." + address.slice(-5);
      } else {
        return "0x??";
      }
    },
    async getLockList() {
      this.LockList = [];
      this.LockListLoading = true;
      const provider = store.state.user.provider;
      const signer = provider.getSigner();
      const pinkLockContract = new ethers.Contract(
        this.pinkLockAddr,
        this.PinkLockABI,
        signer
      );

      let temLockList = [];
      if (this.lockType == "LP") {
        try {
          temLockList = await pinkLockContract.lpLocksForUser(
            store.state.user.address
          );
        } catch (error) {
          this.$message({
            type: "error",
            message: this.$t("lock.查询失败检查网络") + "!",
          });
          this.LockListLoading = false;
          return;
        }
      } else {
        try {
          temLockList = await pinkLockContract.normalLocksForUser(
            store.state.user.address
          );
        } catch (error) {
          this.$message({
            type: "error",
            message: this.$t("lock.查询失败检查网络") + "!",
          });
          this.LockListLoading = false;
          return;
        }
      }
      // 遍历结果，处理空余额或格式化输出
      for (const lock of temLockList) {
        let tokenSymbol = "???";

        let tokenDecimals = 18;
        try {
          const ERC20Contract = new ethers.Contract(
            lock.token,
            this.tokenABI,
            signer
          );
          tokenSymbol = await ERC20Contract.symbol();
          if (this.lockType == "LP") {
            tokenDecimals = 18;
          } else {
            tokenDecimals = await ERC20Contract.decimals();
          }
        } catch (error) {
          console.log("错误!", error);
          this.$message({
            type: "error",
            message: this.$t("lock.获取代币资料失败") + "!",
          });
          this.LockListLoading = false;
          return;
        }
        let temLock = {
          lockTitle: lock.description,
          tokenSymbol: tokenSymbol,
          tokenDecimals: tokenDecimals,
          tokenAddress: lock.token,
          lockAmount: lock.amount,
          lockID: lock.id.toString(),
          lockDate: lock.lockDate.toString(),
          owner: ethers.utils.getAddress(lock.owner),
          unlockedAmount: lock.unlockedAmount.toString(),
          tgeDate: lock.tgeDate.toString(),
        };

        this.LockList.push(temLock);
      }
      this.LockListLoading = false;
    },
    async checkLock() {
      this.checkLoading = true;

      const provider = store.state.user.provider;
      const signer = provider.getSigner();
      let isToken = ethers.utils.isAddress(this.tokenAddress);

      let tokenCode;

      if (!isToken) {
        this.$message({
          type: "error",
          message: this.$t("lock.代币地址地址不正确") + "!",
        });
        this.checkLoading = false;
        return;
      }
      try {
        tokenCode = await provider.getCode(this.tokenAddress);
        if (tokenCode == "0x") {
          this.$message({
            type: "error",
            message: this.$t("lock.代币地址地址不正确") + "!",
          });
          this.checkLoading = false;
          return;
        }
      } catch {
        this.$message({
          type: "error",
          message: this.$t("lock.代币地址地址不正确") + "!",
        });
        this.checkLoading = false;
        return;
      }

      this.tokenAddress = ethers.utils.getAddress(this.tokenAddress);

      const pinkLockContract = new ethers.Contract(
        this.pinkLockAddr,
        this.PinkLockABI,
        signer
      );
      let temTokenLocks = [];
      try {
        temTokenLocks = await pinkLockContract.getLocksForToken(
          this.tokenAddress,
          0,
          20
        );
      } catch (error) {
        this.$message({
          type: "info",
          message: this.$t("lock.未发现锁仓记录") + "!",
        });
        this.checkLoading = false;
        this.goCreate = true;
      }
      if (temTokenLocks.length != 0) {
        this.LockList = [];
        for (const lock of temTokenLocks) {
          let tokenSymbol = "???";

          let tokenDecimals = 18;
          try {
            const ERC20Contract = new ethers.Contract(
              lock.token,
              this.tokenABI,
              signer
            );
            tokenSymbol = await ERC20Contract.symbol();
            if (this.lockType == "LP") {
              tokenDecimals = 18;
            } else {
              tokenDecimals = await ERC20Contract.decimals();
            }
          } catch (error) {
            console.log("错误!", error);
            this.$message({
              type: "error",
              message: this.$t("lock.获取代币资料失败") + "!",
            });
            this.LockListLoading = false;
            return;
          }
          let temLock = {
            lockTitle: lock.description,
            tokenSymbol: tokenSymbol,
            tokenDecimals: tokenDecimals,
            tokenAddress: lock.token,
            lockAmount: lock.amount,
            lockID: lock.id.toString(),
            lockDate: lock.lockDate.toString(),
            owner: ethers.utils.getAddress(lock.owner),
            unlockedAmount: lock.unlockedAmount.toString(),
            tgeDate: lock.tgeDate.toString(),
          };

          this.LockList.push(temLock);
        }
        this.lockType = "";
        this.checkLoading = false;
        this.fetchDialogVisible = false;
      }
    },

    enterCreatePool() {
      this.$router.push({
        path: "/createlock/",
      });
    },
    async unlock(item) {
      this.unlockLoading = true;
      const provider = store.state.user.provider;
      const signer = provider.getSigner();
      const pinkLockContract = new ethers.Contract(
        this.pinkLockAddr,
        this.PinkLockABI,
        signer
      );
      try {
        const unLockRs = await pinkLockContract.unlock(item.lockID);
        this.$message({
          type: "success",
          message: this.$t("lock.已提交等待区块确认") + "!",
        });
        await unLockRs.wait();
        this.$message({
          type: "success",
          message: this.$t("lock.解锁成功") + "!",
        });
        this.getLockList();
      } catch (error) {
        console.log("错误!", error);
        this.$message({
          type: "danger",
          message: this.$t("lock.解锁失败") + "!",
        });
      }
      this.unlockLoading = false;
    },
    async renounceOwner(item) {
      this.quitLoading = true;
      const provider = store.state.user.provider;
      const signer = provider.getSigner();
      const pinkLockContract = new ethers.Contract(
        this.pinkLockAddr,
        this.PinkLockABI,
        signer
      );
      try {
        const quitRs = await pinkLockContract.renounceLockOwnership(
          item.lockID
        );
        this.$message({
          type: "success",
          message: this.$t("lock.已提交等待区块确认") + "!",
        });
        await quitRs.wait();
        this.$message({
          type: "success",
          message: this.$t("lock.弃权成功") + "!",
        });
        this.getLockList();
      } catch (error) {
        console.log("错误!", error);
        this.$message({
          type: "danger",
          message: this.$t("lock.弃权失败") + "!",
        });
      }
      this.quitLoading = false;
    },
    copy_str(text) {
      if (navigator.clipboard) {
        // clipboard api 复制
        navigator.clipboard.writeText(text);
      } else {
        var textarea = document.createElement("textarea");
        document.body.appendChild(textarea);
        // 隐藏此输入框
        textarea.style.position = "fixed";
        textarea.style.clip = "rect(0 0 0 0)";
        textarea.style.top = "10px";
        // 赋值
        textarea.value = text;
        // 选中
        textarea.select();
        // 复制
        document.execCommand("copy", true);
        // 移除输入框
        document.body.removeChild(textarea);
      }
      this.$message({
        type: "success",
        message: this.$t("lock.已复制") + "!",
      });
    },
  },
};
</script>

<style scoped>
.text-right {
  padding: 5px;
  text-align: right;
}
.noLiquidity {
  margin-top: 80px;
  margin-left: 6%;
  text-align: center;

  font-size: 16px;
  color: #999;
}
@media (max-width: 768px) {
  .text-right {
    text-align: left; /* 移动端改为左对齐（也可以继续右对齐） */
  }
  .noLiquidity {
    margin-top: 80px;
    margin-left: 1%;
    text-align: center;

    font-size: 16px;
    color: #999;
  }
}
.lock-container {
  padding: 20px;
  max-width: 900px;
  margin: auto;
}

.lock-card {
  margin-bottom: 15px;
}

.item {
  margin-top: 15px;
  text-align: center;
}

.label {
  font-size: 14px;
  color: #888;
  margin-bottom: 5px;
}

.LPValue {
  padding-top: 8px;
  font-size: 14px;
  font-weight: normal;
}

.note {
  font-size: 14px;
  color: #666;
  text-align: center;

  line-height: 1.6;
}

.highlight {
  color: red;
  font-weight: bold;
}

.spinner {
  margin-top: 60px;
  margin-bottom: 60px;
}
.type-switch {
  margin: 15px 0 15px;
  font-size: 14px;
  color: #444;
}
</style>
