// sidebar
$menuText: #2b343d;
$menuActiveText: #3990e7;
$subMenuActiveText: #30608f; //https://github.com/ElemeFE/element/issues/12951

$menuBg: #ffff;
$menuHover: #90a4b8;

$subMenuBg: #ffff;
$subMenuHover: #92aec2;

$sideBarWidth: 230px;

// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  menuText: $menuText;
  menuActiveText: $menuActiveText;
  subMenuActiveText: $subMenuActiveText;
  menuBg: $menuBg;
  menuHover: $menuHover;
  subMenuBg: $subMenuBg;
  subMenuHover: $subMenuHover;
  sideBarWidth: $sideBarWidth;
}
