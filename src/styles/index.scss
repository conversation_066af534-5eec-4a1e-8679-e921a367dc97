@import "./variables.scss";
@import "./mixin.scss";
@import "./transition.scss";
@import "./element-ui.scss";
@import "./sidebar.scss";

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB,
    Microsoft YaHei, Arial, sans-serif;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

// main-container global css
// .app-container {
//   padding: 20px;

//   padding-bottom: 100px;
//   background-color: #eeeeee;
// }
// .inner {
//   background-color: #ffff;
//   padding: 40px;
// }
@media screen and (max-width: 500px) {
  .app-container {
    padding: 10px;

    padding-bottom: 60px;
    background-color: #eeeeee;
  }
  .inner {
    background-color: #ffff;
    padding: 10px;
  }

  .header {
    text-align: center;
    margin-top: 20px;
    font-size: larger;
  }
  .main {
    /* text-align: center; */
    padding-left: 5%;
    padding-right: 5%;
    padding-bottom: 100px;

    overflow-x: hidden;
  }
  .flex-container {
    display: flex;
    flex-direction: row;
    margin-top: 10px;
  }
  .el-dialog {
    width: 90% !important;
  }
  .el-form {
    label-position: "top";
  }
}
@media screen and (min-width: 500px) {
  .app-container {
    padding: 20px;

    padding-bottom: 60px;
    background-color: #eeeeee;
  }
  .inner {
    background-color: #ffff;
    padding: 40px;
  }
  .header {
    text-align: center;
    margin-top: 20px;
    font-size: larger;
  }
  .main {
    /* text-align: center; */
    padding-left: 15%;
    padding-right: 15%;
    padding-bottom: 100px;
    // overflow-x: scroll;
  }
  .flex-container {
    display: flex;
    flex-direction: row;
    margin-top: 20px;
  }
  .el-dialog {
    width: 50%;
  }
  .el-form {
    label-position: "left";
    label-width: "auto";
  }
}
