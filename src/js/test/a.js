const BASE58_ALPHABET =
  "123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz";

// Base58 编码函数
function base58Encode(bytes) {
  let intVal = BigInt(0);
  for (let i = 0; i < bytes.length; i++) {
    intVal = (intVal << 8n) + BigInt(bytes[i]);
  }

  let result = "";
  const base = BigInt(58);
  while (intVal > 0n) {
    const mod = intVal % base;
    result = BASE58_ALPHABET[Number(mod)] + result;
    intVal = intVal / base;
  }

  // 处理前导0
  for (let i = 0; i < bytes.length && bytes[i] === 0; i++) {
    result = "1" + result;
  }

  return result;
}

// 两次 SHA256（返回 Uint8Array）
async function sha256Twice(bytes) {
  const hash1 = await crypto.subtle.digest("SHA-256", bytes);
  const hash2 = await crypto.subtle.digest("SHA-256", hash1);
  return new Uint8Array(hash2);
}

// bs58check 编码函数（浏览器兼容版）
async function bs58checkEncode(payload) {
  const checksum = await sha256Twice(payload);
  const full = new Uint8Array(payload.length + 4);
  full.set(payload, 0);
  full.set(checksum.slice(0, 4), payload.length);
  return base58Encode(full);
}

// 以太坊地址转 TRON 地址（浏览器兼容）
async function ethToTron(ethAddress) {
  // if (!/^0x[0-9a-fA-F]{40}$/.test(ethAddress)) {
  //   throw new Error("Invalid Ethereum address");
  // }

  // // 将地址字符串转为 Uint8Array（不含0x）
  const hex = ethAddress.slice(2);
  const addrBytes = new Uint8Array(hex.length / 2);
  for (let i = 0; i < addrBytes.length; i++) {
    addrBytes[i] = parseInt(hex.slice(i * 2, i * 2 + 2), 16);
  }

  // 前缀 0x41 + 地址体
  const payload = new Uint8Array(21);
  payload[0] = 0x41;
  payload.set(addrBytes, 1);

  return await bs58checkEncode(payload);
}

//----------------------------------------

// SHA256 function using Web Crypto API
async function sha256(data) {
  const hashBuffer = await crypto.subtle.digest("SHA-256", data);
  return new Uint8Array(hashBuffer);
}
// Keccak256 implementation (simplified - using SHA256 as placeholder)
async function keccak256(data) {
  // In a real implementation, you'd use a proper keccak library
  // For now, using SHA256 as a placeholder
  return await sha256(data);
}
// Generate random bytes using Web Crypto API
function randomBytes(size) {
  const array = new Uint8Array(size);
  crypto.getRandomValues(array);
  return array;
}
// Calculate checksum for Base58Check
async function calculateChecksum(buffer) {
  const hash = await sha256(buffer);
  return hash.slice(0, 4);
}
// Convert private key to Ethereum address
async function privateToAddress(privateKey) {
  // Simplified implementation - in production you'd need proper secp256k1
  const hash = await sha256(privateKey);
  const addressBytes = hash.slice(-20);
  // Convert to hex string
  let hex = "";
  for (let i = 0; i < addressBytes.length; i++) {
    hex += addressBytes[i].toString(16).padStart(2, "0");
  }

  return hex;
}

// Create checksum address
async function toChecksumAddress(address) {
  const hash = await sha256(new TextEncoder().encode(address.toLowerCase()));

  // Convert hash to hex string
  let hashHex = "";
  for (let i = 0; i < hash.length; i++) {
    hashHex += hash[i].toString(16).padStart(2, "0");
  }

  let ret = "0x";
  for (let i = 0; i < address.length; i++) {
    if (parseInt(hashHex[i], 16) >= 8) {
      ret += address[i].toUpperCase();
    } else {
      ret += address[i].toLowerCase();
    }
  }

  return ret;
}

/**
 * Create a Tron wallet from a random private key
 * @returns {{address: string, privKey: string}}
 */
const getRandomWallet = async () => {
  const randbytes = randomBytes(32);
  const address = await privateToAddress(randbytes);
  const evmAddress =  await toChecksumAddress(address)
  const tronAddress = await ethToTron(evmAddress)
  let privKeyHex = "";
  for (let i = 0; i < randbytes.length; i++) {
    privKeyHex += randbytes[i].toString(16).padStart(2, "0");
  }
  return {
    evmAddress: evmAddress,
    tronAddress: tronAddress, // Tron addresses are already checksummed
    privKey: privKeyHex,
  };
};
getRandomWallet().then(console.log)