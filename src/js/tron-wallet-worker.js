/* eslint-env worker */

console.log("Worker script loaded tron");

/* eslint-env worker */
const TronWeb = require('tronweb');
/**
 * Create a wallet from a random private key
 * @returns {{address: string, privKey: string}}
 */
 const getRandomWallet = () => {
    
    const wallet = TronWeb.utils.accounts.generateAccount();
    return {
        address: wallet.address.base58,
        checksumAddress:wallet.address.base58,
        privKey: wallet.privateKey
    };
};

let count = 0;
let stopped = false;
let progressTimer = null;
let lastReportTime = 0;
const REPORT_INTERVAL = 2000; // 改为2秒报告一次，减少消息频率

// 设置消息处理器
self.onmessage = function (e) {
  if (e.data && e.data.type === "getCount") {
    self.postMessage({ type: "count", count });
    return;
  }

  // 其余为任务启动
  const { prefix, suffix, caseSensitive } = e.data;
  count = 0;
  stopped = false;
  lastReportTime = Date.now();

  // console.log('Starting generation with:', { prefix, suffix, caseSensitive });

  // 定期报告进度 - 降低频率
  progressTimer = setInterval(() => {
    if (!stopped) {
      self.postMessage({ type: "count", count });
    }
  }, REPORT_INTERVAL);
  // 优化的生成循环 - 使用批量处理
  const generateBatch = () => {
    if (stopped) return;

    const batchSize = 100; // 批量处理100个钱包
    const startTime = Date.now();

    for (let i = 0; i < batchSize && !stopped; i++) {
      const randomWallet = getRandomWallet();
     
      let addr = randomWallet.checksumAddress.slice(1);
      let prefixMatch = addr.slice(0, prefix.length);
      let suffixMatch = suffix.length > 0 ? addr.slice(-suffix.length) : "";
      let match = false;

      if (!caseSensitive) {
        match =
          prefix.toLowerCase() === prefixMatch.toLowerCase() &&
          suffix.toLowerCase() === suffixMatch.toLowerCase();
      } else {
        match = prefix === prefixMatch && suffix === suffixMatch;
      }

      if (match) {
        stopped = true;
        if (progressTimer) {
          clearInterval(progressTimer);
          progressTimer = null;
        }
        console.log("Found match!", randomWallet.address);
        self.postMessage({
          type: "result",
          success: true,
          wallet: randomWallet,
          count,
        });
        return;
      }

      count++;
    }

    // 检查是否需要报告进度（基于时间间隔）
    const now = Date.now();
    if (now - lastReportTime >= REPORT_INTERVAL) {
      self.postMessage({ type: "count", count });
      lastReportTime = now;
    }

    // 使用 requestAnimationFrame 或 setTimeout 继续下一批
    if (!stopped) {
      // 如果批处理很快完成，使用 setTimeout 避免阻塞
      const elapsed = Date.now() - startTime;
      if (elapsed < 16) {
        // 小于16ms，使用setTimeout
        setTimeout(generateBatch, 0);
      } else {
        // 使用 requestAnimationFrame 或立即执行下一批
        generateBatch();
      }
    }
  };

  generateBatch();
};

console.log("Worker script ready");
